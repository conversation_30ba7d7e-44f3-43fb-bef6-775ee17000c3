-- CLEAN AGENTS FIX - COPY THIS ENTIRE CONTENT
-- This creates agents with proper API compatibility

-- First, ensure the account exists in basejump.accounts
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Safe cleanup that respects foreign key constraints
UPDATE public.agents SET current_version_id = NULL WHERE current_version_id IS NOT NULL;
DELETE FROM public.agent_versions;
DELETE FROM public.agents;

-- Add required columns to agents table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'system_prompt' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN system_prompt text;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_default' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_default boolean DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_public' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_public boolean DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'configured_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN configured_mcps jsonb DEFAULT '[]'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'custom_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN custom_mcps jsonb DEFAULT '[]'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'agentpress_tools' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN agentpress_tools jsonb DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- Create agents with all required fields
DO $$
DECLARE
    suna_id uuid := gen_random_uuid();
    code_id uuid := gen_random_uuid();
    marketing_id uuid := gen_random_uuid();
    suna_version_id uuid;
    code_version_id uuid;
    marketing_version_id uuid;
BEGIN
    -- Insert agents
    INSERT INTO public.agents (
        agent_id,
        account_id, 
        name, 
        description, 
        role,
        system_prompt,
        is_default,
        is_public,
        configured_mcps,
        custom_mcps,
        agentpress_tools
    ) VALUES 
    (
        suna_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Suna',
        'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
        'assistant',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        true,
        false,
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb
    ),
    (
        code_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Code Assistant',
        'A specialized AI assistant for coding, debugging, and technical development tasks.',
        'assistant',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        false,
        false,
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb
    ),
    (
        marketing_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Marketing Advisor',
        'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
        'assistant',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        false,
        false,
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb
    );

    -- Create agent versions
    INSERT INTO public.agent_versions (
        agent_id,
        version_number,
        name,
        system_prompt
    ) VALUES
    (
        suna_id,
        1,
        'Suna v1.0',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.'
    ),
    (
        code_id,
        1,
        'Code Assistant v1.0',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.'
    ),
    (
        marketing_id,
        1,
        'Marketing Advisor v1.0',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.'
    );

    -- Get version IDs and update current_version_id
    SELECT version_id INTO suna_version_id FROM public.agent_versions WHERE agent_id = suna_id AND version_number = 1;
    SELECT version_id INTO code_version_id FROM public.agent_versions WHERE agent_id = code_id AND version_number = 1;
    SELECT version_id INTO marketing_version_id FROM public.agent_versions WHERE agent_id = marketing_id AND version_number = 1;

    -- Update agents with current version references
    UPDATE public.agents SET current_version_id = suna_version_id WHERE agent_id = suna_id;
    UPDATE public.agents SET current_version_id = code_version_id WHERE agent_id = code_id;
    UPDATE public.agents SET current_version_id = marketing_version_id WHERE agent_id = marketing_id;

    RAISE NOTICE 'Successfully created 3 agents with full API compatibility';
END $$;

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';
