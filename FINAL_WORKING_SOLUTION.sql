-- =====================================================
-- FINAL DEFINITIVE WORKING SOLUTION FOR SUNA AGENTS
-- =====================================================
-- This SQL creates agents that match the exact API expectations
-- Based on thorough audit of database schema and API code

-- Ensure the account exists
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Clean up existing data safely
UPDATE agents SET current_version_id = NULL WHERE current_version_id IS NOT NULL;
DELETE FROM agent_versions;
DELETE FROM agents;

-- Create agents with exact schema match
DO $$
DECLARE
    suna_agent_id uuid := gen_random_uuid();
    code_agent_id uuid := gen_random_uuid();
    marketing_agent_id uuid := gen_random_uuid();
    suna_version_id uuid;
    code_version_id uuid;
    marketing_version_id uuid;
BEGIN
    -- Insert agents with all required fields matching the exact schema
    INSERT INTO agents (
        agent_id,
        account_id,
        name,
        description,
        system_prompt,
        configured_mcps,
        agentpress_tools,
        is_default,
        avatar,
        avatar_color,
        created_at,
        updated_at
    ) VALUES 
    (
        suna_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Suna',
        'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        '🚀',
        '#3B82F6',
        now(),
        now()
    ),
    (
        code_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Code Assistant',
        'A specialized AI assistant for coding, debugging, and technical development tasks.',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        '[]'::jsonb,
        '{}'::jsonb,
        false,
        '💻',
        '#10B981',
        now(),
        now()
    ),
    (
        marketing_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Marketing Advisor',
        'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        '[]'::jsonb,
        '{}'::jsonb,
        false,
        '📈',
        '#F59E0B',
        now(),
        now()
    );

    -- Create agent versions with exact schema match
    INSERT INTO agent_versions (
        version_id,
        agent_id,
        version_number,
        version_name,
        system_prompt,
        configured_mcps,
        custom_mcps,
        agentpress_tools,
        is_active,
        created_at,
        updated_at,
        created_by
    ) VALUES
    (
        gen_random_uuid(),
        suna_agent_id,
        1,
        'v1',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        now(),
        now(),
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid
    ),
    (
        gen_random_uuid(),
        code_agent_id,
        1,
        'v1',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        now(),
        now(),
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid
    ),
    (
        gen_random_uuid(),
        marketing_agent_id,
        1,
        'v1',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        now(),
        now(),
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid
    );

    -- Get the version IDs that were just created
    SELECT version_id INTO suna_version_id 
    FROM agent_versions 
    WHERE agent_id = suna_agent_id AND version_number = 1;
    
    SELECT version_id INTO code_version_id 
    FROM agent_versions 
    WHERE agent_id = code_agent_id AND version_number = 1;
    
    SELECT version_id INTO marketing_version_id 
    FROM agent_versions 
    WHERE agent_id = marketing_agent_id AND version_number = 1;

    -- Update agents with current_version_id and version_count
    UPDATE agents 
    SET 
        current_version_id = suna_version_id,
        version_count = 1
    WHERE agent_id = suna_agent_id;

    UPDATE agents 
    SET 
        current_version_id = code_version_id,
        version_count = 1
    WHERE agent_id = code_agent_id;

    UPDATE agents 
    SET 
        current_version_id = marketing_version_id,
        version_count = 1
    WHERE agent_id = marketing_agent_id;

    -- Log success
    RAISE NOTICE 'Successfully created 3 agents with full API compatibility:';
    RAISE NOTICE 'Suna: agent_id=%, version_id=%', suna_agent_id, suna_version_id;
    RAISE NOTICE 'Code Assistant: agent_id=%, version_id=%', code_agent_id, code_version_id;
    RAISE NOTICE 'Marketing Advisor: agent_id=%, version_id=%', marketing_agent_id, marketing_version_id;
END $$;

-- Verify the creation
DO $$
DECLARE
    agent_count integer;
    version_count integer;
    agent_rec record;
BEGIN
    SELECT COUNT(*) INTO agent_count 
    FROM agents 
    WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    
    SELECT COUNT(*) INTO version_count 
    FROM agent_versions av
    JOIN agents a ON av.agent_id = a.agent_id
    WHERE a.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    
    RAISE NOTICE 'Verification: % agents created, % versions created', agent_count, version_count;
    
    -- Show each agent with its details
    FOR agent_rec IN 
        SELECT a.name, a.agent_id, a.current_version_id, a.system_prompt IS NOT NULL as has_system_prompt
        FROM agents a 
        WHERE a.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid
        ORDER BY a.name
    LOOP
        RAISE NOTICE 'Agent: % | ID: % | Version: % | Has System Prompt: %', 
            agent_rec.name, agent_rec.agent_id, agent_rec.current_version_id, agent_rec.has_system_prompt;
    END LOOP;
END $$;

-- Refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';
