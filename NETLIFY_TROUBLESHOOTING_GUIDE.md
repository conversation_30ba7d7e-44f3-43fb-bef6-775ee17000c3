# 🔧 Netlify Deployment Troubleshooting Guide

## 🚨 Common Issues & Solutions

### Issue 1: App Not Loading - Environment Variables Missing

**Symptoms:**
- Site builds successfully but shows blank page or errors
- <PERSON>sol<PERSON> shows "Failed to fetch" or API errors
- Authentication not working

**Solution:**
Add these **REQUIRED** environment variables in Netlify:

```bash
# Frontend Environment Variables (Netlify Dashboard > Site Settings > Environment Variables)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_BACKEND_URL=https://your-backend-url.com/api
NEXT_PUBLIC_URL=https://your-netlify-site.netlify.app
NEXT_PUBLIC_ENV_MODE=PRODUCTION

# Optional but recommended
ADMIN_API_KEY=your-secure-admin-key
```

### Issue 2: Backend URL Configuration

**Problem:** The app is configured to use `http://127.0.0.1:8000/api` which only works locally.

**Fix:** Update `NEXT_PUBLIC_BACKEND_URL` to your production backend URL.

### Issue 3: Build Errors

**Check Netlify Build Logs:**
1. Go to Netlify Dashboard
2. Click on your site
3. Go to "Deploys" tab
4. Click on the failed deploy
5. Check the build logs for errors

**Common Build Fixes:**
```bash
# If you see "Module not found" errors
npm install

# If you see TypeScript errors
npm run build

# If you see environment variable errors
# Add missing variables in Netlify dashboard
```

### Issue 4: Runtime Errors

**Check Browser Console:**
1. Open your deployed site
2. Press F12 to open Developer Tools
3. Check Console tab for errors
4. Check Network tab for failed requests

**Common Runtime Fixes:**
- Supabase connection errors → Check SUPABASE_URL and ANON_KEY
- API call failures → Check BACKEND_URL
- Authentication issues → Check environment variables

## 🔍 Diagnostic Steps

### Step 1: Verify Environment Variables
```javascript
// Add this to a page to debug (remove after testing)
console.log('Environment Check:', {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
  backendUrl: process.env.NEXT_PUBLIC_BACKEND_URL,
  envMode: process.env.NEXT_PUBLIC_ENV_MODE
});
```

### Step 2: Test Supabase Connection
```javascript
// Test in browser console on your deployed site
import { createClient } from '@supabase/supabase-js'
const supabase = createClient(
  'YOUR_SUPABASE_URL', 
  'YOUR_ANON_KEY'
)
supabase.from('agents').select('*').limit(1).then(console.log)
```

### Step 3: Check Network Requests
- Open Network tab in browser dev tools
- Look for failed requests (red entries)
- Check if API calls are going to correct URLs

## 🛠️ Quick Fixes

### Fix 1: Update Environment Variables
1. Go to Netlify Dashboard
2. Site Settings → Environment Variables
3. Add all required variables
4. Trigger new deploy

### Fix 2: Force Redeploy
1. Go to Deploys tab
2. Click "Trigger deploy"
3. Select "Deploy site"

### Fix 3: Clear Build Cache
1. Go to Site Settings
2. Build & Deploy → Post processing
3. Clear cache and deploy

## 📞 Getting Help

If the app is still not working:

1. **Check Netlify Function Logs:**
   - Go to Functions tab in Netlify dashboard
   - Check for any function errors

2. **Enable Debug Mode:**
   Add to environment variables:
   ```bash
   DEBUG=true
   NEXT_PUBLIC_DEBUG=true
   ```

3. **Check Supabase Logs:**
   - Go to your Supabase dashboard
   - Check API logs for errors

4. **Verify Build Output:**
   - Ensure `.next` folder is being published
   - Check that all required files are present
