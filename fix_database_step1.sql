-- STEP 1: CREATE BASIC TABLES WITHOUT FOREIGN KEYS
-- This avoids dependency issues

-- Create projects table (basic version)
CREATE TABLE IF NOT EXISTS public.projects (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    name text NOT NULL,
    description text,
    status text DEFAULT 'active',
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

-- Create threads table (basic version)
CREATE TABLE IF NOT EXISTS public.threads (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    title text,
    status text DEFAULT 'active',
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

-- Create messages table (basic version)
CREATE TABLE IF NOT EXISTS public.messages (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    role text NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content text,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

-- Create agent_versions table (basic version)
CREATE TABLE IF NOT EXISTS public.agent_versions (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    version_number integer NOT NULL DEFAULT 1,
    name text NOT NULL,
    description text,
    instructions text,
    system_prompt text,
    metadata jsonb DEFAULT '{}'::jsonb,
    is_current boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

-- Grant permissions
GRANT ALL ON TABLE public.projects TO authenticated, service_role;
GRANT ALL ON TABLE public.threads TO authenticated, service_role;
GRANT ALL ON TABLE public.messages TO authenticated, service_role;
GRANT ALL ON TABLE public.agent_versions TO authenticated, service_role;

-- Enable RLS with simple policies
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_versions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Authenticated users can view projects" ON public.projects;
DROP POLICY IF EXISTS "Authenticated users can insert projects" ON public.projects;
DROP POLICY IF EXISTS "Authenticated users can update projects" ON public.projects;
DROP POLICY IF EXISTS "Authenticated users can delete projects" ON public.projects;

DROP POLICY IF EXISTS "Authenticated users can view threads" ON public.threads;
DROP POLICY IF EXISTS "Authenticated users can insert threads" ON public.threads;
DROP POLICY IF EXISTS "Authenticated users can update threads" ON public.threads;
DROP POLICY IF EXISTS "Authenticated users can delete threads" ON public.threads;

DROP POLICY IF EXISTS "Authenticated users can view messages" ON public.messages;
DROP POLICY IF EXISTS "Authenticated users can insert messages" ON public.messages;
DROP POLICY IF EXISTS "Authenticated users can update messages" ON public.messages;
DROP POLICY IF EXISTS "Authenticated users can delete messages" ON public.messages;

DROP POLICY IF EXISTS "Authenticated users can view agent versions" ON public.agent_versions;
DROP POLICY IF EXISTS "Authenticated users can insert agent versions" ON public.agent_versions;
DROP POLICY IF EXISTS "Authenticated users can update agent versions" ON public.agent_versions;
DROP POLICY IF EXISTS "Authenticated users can delete agent versions" ON public.agent_versions;

-- Create simple policies
CREATE POLICY "Authenticated users can view projects" ON public.projects
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can insert projects" ON public.projects
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Authenticated users can update projects" ON public.projects
    FOR UPDATE TO authenticated USING (true);

CREATE POLICY "Authenticated users can delete projects" ON public.projects
    FOR DELETE TO authenticated USING (true);

CREATE POLICY "Authenticated users can view threads" ON public.threads
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can insert threads" ON public.threads
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Authenticated users can update threads" ON public.threads
    FOR UPDATE TO authenticated USING (true);

CREATE POLICY "Authenticated users can delete threads" ON public.threads
    FOR DELETE TO authenticated USING (true);

CREATE POLICY "Authenticated users can view messages" ON public.messages
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can insert messages" ON public.messages
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Authenticated users can update messages" ON public.messages
    FOR UPDATE TO authenticated USING (true);

CREATE POLICY "Authenticated users can delete messages" ON public.messages
    FOR DELETE TO authenticated USING (true);

CREATE POLICY "Authenticated users can view agent versions" ON public.agent_versions
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can insert agent versions" ON public.agent_versions
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Authenticated users can update agent versions" ON public.agent_versions
    FOR UPDATE TO authenticated USING (true);

CREATE POLICY "Authenticated users can delete agent versions" ON public.agent_versions
    FOR DELETE TO authenticated USING (true);

-- Create timestamp triggers
DROP TRIGGER IF EXISTS set_timestamps_projects ON public.projects;
CREATE TRIGGER set_timestamps_projects BEFORE INSERT OR UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

DROP TRIGGER IF EXISTS set_timestamps_threads ON public.threads;
CREATE TRIGGER set_timestamps_threads BEFORE INSERT OR UPDATE ON public.threads
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

DROP TRIGGER IF EXISTS set_timestamps_messages ON public.messages;
CREATE TRIGGER set_timestamps_messages BEFORE INSERT OR UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

DROP TRIGGER IF EXISTS set_timestamps_agent_versions ON public.agent_versions;
CREATE TRIGGER set_timestamps_agent_versions BEFORE INSERT OR UPDATE ON public.agent_versions
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

-- Create basic sample data
INSERT INTO public.projects (name, description, status)
VALUES ('Welcome Project', 'Your first project to get started with Suna', 'active')
ON CONFLICT DO NOTHING;

INSERT INTO public.threads (title, status)
VALUES ('Welcome Chat', 'active')
ON CONFLICT DO NOTHING;
