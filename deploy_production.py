#!/usr/bin/env python3
"""
Production deployment script for Suna application
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run a shell command and return the result"""
    print(f"Running: {command}")
    result = subprocess.run(
        command, 
        shell=True, 
        cwd=cwd, 
        capture_output=True, 
        text=True,
        check=check
    )
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr, file=sys.stderr)
    return result

def check_prerequisites():
    """Check if all prerequisites are installed"""
    print("🔍 Checking prerequisites...")
    
    prerequisites = [
        ("python3", "python3 --version"),
        ("node", "node --version"),
        ("npm", "npm --version"),
        ("docker", "docker --version"),
        ("redis", "redis-cli --version")
    ]
    
    missing = []
    for name, command in prerequisites:
        try:
            result = run_command(command, check=False)
            if result.returncode == 0:
                print(f"✅ {name}: {result.stdout.strip()}")
            else:
                missing.append(name)
                print(f"❌ {name}: Not found")
        except Exception as e:
            missing.append(name)
            print(f"❌ {name}: Error checking - {e}")
    
    if missing:
        print(f"\n❌ Missing prerequisites: {', '.join(missing)}")
        print("Please install missing prerequisites before deploying.")
        return False
    
    print("✅ All prerequisites found!")
    return True

def validate_environment():
    """Validate environment configuration"""
    print("\n🔧 Validating environment configuration...")
    
    # Check if .env file exists
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("❌ backend/.env file not found!")
        return False
    
    # Load and validate environment variables
    required_vars = [
        "ENV_MODE",
        "SUPABASE_URL",
        "SUPABASE_SERVICE_ROLE_KEY",
        "REDIS_HOST",
        "REDIS_PORT",
        "GROQ_API_KEY",
        "MODEL_TO_USE",
        "ADMIN_API_KEY"
    ]
    
    missing_vars = []
    demo_keys = []
    
    with open(env_file) as f:
        env_content = f.read()
    
    for var in required_vars:
        if f"{var}=" not in env_content:
            missing_vars.append(var)
        elif "demo_key_for_development" in env_content:
            demo_keys.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    if demo_keys:
        print(f"⚠️  Demo keys found for: {', '.join(demo_keys)}")
        print("   Please replace demo keys with production keys!")
        return False
    
    print("✅ Environment configuration validated!")
    return True

def install_dependencies():
    """Install Python and Node.js dependencies"""
    print("\n📦 Installing dependencies...")
    
    # Install Python dependencies
    print("Installing Python dependencies...")
    result = run_command("pip install -r requirements.txt", cwd="backend")
    if result.returncode != 0:
        print("❌ Failed to install Python dependencies")
        return False
    
    # Install Node.js dependencies
    print("Installing Node.js dependencies...")
    result = run_command("npm install", cwd="frontend")
    if result.returncode != 0:
        print("❌ Failed to install Node.js dependencies")
        return False
    
    print("✅ Dependencies installed successfully!")
    return True

def build_frontend():
    """Build the frontend for production"""
    print("\n🏗️  Building frontend...")
    
    result = run_command("npm run build", cwd="frontend")
    if result.returncode != 0:
        print("❌ Frontend build failed")
        return False
    
    print("✅ Frontend built successfully!")
    return True

def run_tests():
    """Run tests to ensure everything is working"""
    print("\n🧪 Running tests...")
    
    # Run backend tests if they exist
    if Path("backend/tests").exists():
        print("Running backend tests...")
        result = run_command("python -m pytest tests/", cwd="backend", check=False)
        if result.returncode != 0:
            print("⚠️  Some backend tests failed")
    
    # Run frontend tests if they exist
    if Path("frontend/package.json").exists():
        print("Running frontend tests...")
        result = run_command("npm test -- --watchAll=false", cwd="frontend", check=False)
        if result.returncode != 0:
            print("⚠️  Some frontend tests failed")
    
    print("✅ Tests completed!")
    return True

def start_services():
    """Start the production services"""
    print("\n🚀 Starting production services...")
    
    # Start Redis if not running
    print("Checking Redis...")
    result = run_command("redis-cli ping", check=False)
    if result.returncode != 0:
        print("Starting Redis...")
        run_command("redis-server --daemonize yes", check=False)
        time.sleep(2)
    
    # Start backend
    print("Starting backend...")
    backend_cmd = "python3 -m uvicorn api:app --host 0.0.0.0 --port 8000 --workers 4"
    run_command(f"nohup {backend_cmd} > backend.log 2>&1 &", cwd="backend", check=False)
    
    # Wait for backend to start
    print("Waiting for backend to start...")
    for i in range(30):
        try:
            result = run_command("curl -s http://localhost:8000/api/health", check=False)
            if result.returncode == 0:
                print("✅ Backend started successfully!")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("❌ Backend failed to start within 30 seconds")
        return False
    
    # Start frontend
    print("Starting frontend...")
    frontend_cmd = "npm start"
    run_command(f"nohup {frontend_cmd} > frontend.log 2>&1 &", cwd="frontend", check=False)
    
    print("✅ Services started successfully!")
    return True

def run_health_checks():
    """Run comprehensive health checks"""
    print("\n🏥 Running health checks...")
    
    endpoints = [
        "http://localhost:8000/api/health",
        "http://localhost:8000/api/health/detailed",
        "http://localhost:8000/api/health/ready"
    ]
    
    for endpoint in endpoints:
        try:
            result = run_command(f"curl -s {endpoint}", check=False)
            if result.returncode == 0:
                response = json.loads(result.stdout)
                status = response.get("status", "unknown")
                print(f"✅ {endpoint}: {status}")
            else:
                print(f"❌ {endpoint}: Failed to connect")
        except Exception as e:
            print(f"❌ {endpoint}: Error - {e}")
    
    print("✅ Health checks completed!")
    return True

def main():
    """Main deployment function"""
    print("🚀 Suna Production Deployment Script")
    print("=" * 50)
    
    steps = [
        ("Prerequisites", check_prerequisites),
        ("Environment", validate_environment),
        ("Dependencies", install_dependencies),
        ("Frontend Build", build_frontend),
        ("Tests", run_tests),
        ("Services", start_services),
        ("Health Checks", run_health_checks)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 Step: {step_name}")
        if not step_func():
            print(f"\n❌ Deployment failed at step: {step_name}")
            sys.exit(1)
    
    print("\n🎉 Deployment completed successfully!")
    print("\n📊 Service URLs:")
    print("   Frontend: http://localhost:3000")
    print("   Backend:  http://localhost:8000")
    print("   Health:   http://localhost:8000/api/health/detailed")
    print("\n📝 Logs:")
    print("   Backend:  backend/backend.log")
    print("   Frontend: frontend/frontend.log")
    print("\n🔧 Management commands:")
    print("   Stop services: pkill -f 'uvicorn\\|npm'")
    print("   View logs:     tail -f backend/backend.log")
    print("   Health check:  curl http://localhost:8000/api/health/detailed")

if __name__ == "__main__":
    main()
