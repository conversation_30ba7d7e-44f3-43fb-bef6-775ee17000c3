#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Find Backend Hosting Platform

This script helps identify where your backend is hosted and how to deploy it.
"""

import subprocess
import sys

def run_command(cmd):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except subprocess.TimeoutExpired:
        return "", "Command timed out", 1
    except Exception as e:
        return "", str(e), 1

def check_dns_info():
    """Check DNS information for the backend domain"""
    print("🔍 Checking Backend DNS Information")
    print("=" * 40)
    
    # Try to get DNS info
    stdout, stderr, code = run_command("nslookup api.ai-co-founder.com")
    if code == 0 and stdout:
        print("DNS Information:")
        print(stdout)
        
        # Look for hosting platform indicators
        if "railway" in stdout.lower():
            print("🚂 Likely hosted on Railway")
            return "railway"
        elif "render" in stdout.lower():
            print("🎨 Likely hosted on Render")
            return "render"
        elif "heroku" in stdout.lower():
            print("🟣 Likely hosted on Heroku")
            return "heroku"
        elif "digitalocean" in stdout.lower():
            print("🌊 Likely hosted on DigitalOcean")
            return "digitalocean"
        elif "amazonaws" in stdout.lower():
            print("☁️ Likely hosted on AWS")
            return "aws"
        elif "googleusercontent" in stdout.lower():
            print("☁️ Likely hosted on Google Cloud")
            return "gcp"
    else:
        print("Could not retrieve DNS information")
    
    return "unknown"

def check_http_headers():
    """Check HTTP headers for hosting platform clues"""
    print("\n🔍 Checking HTTP Headers")
    print("=" * 40)
    
    # Try to get HTTP headers
    stdout, stderr, code = run_command("curl -I -s https://api.ai-co-founder.com/api/health")
    if code == 0 and stdout:
        print("HTTP Headers:")
        print(stdout)
        
        # Look for platform-specific headers
        headers_lower = stdout.lower()
        if "railway" in headers_lower:
            print("🚂 Railway detected in headers")
            return "railway"
        elif "render" in headers_lower:
            print("🎨 Render detected in headers")
            return "render"
        elif "heroku" in headers_lower:
            print("🟣 Heroku detected in headers")
            return "heroku"
        elif "digitalocean" in headers_lower:
            print("🌊 DigitalOcean detected in headers")
            return "digitalocean"
        elif "cloudflare" in headers_lower:
            print("☁️ Cloudflare detected (could be proxying)")
            return "cloudflare"
    else:
        print("Could not retrieve HTTP headers")
    
    return "unknown"

def provide_deployment_instructions(platform):
    """Provide deployment instructions based on detected platform"""
    print("\n🚀 Deployment Instructions")
    print("=" * 40)
    
    if platform == "railway":
        print("🚂 Railway Deployment:")
        print("1. Go to: https://railway.app/dashboard")
        print("2. Find your backend service")
        print("3. Click 'Deploy' or 'Redeploy'")
        print("4. Wait 5-10 minutes for deployment")
        print("5. Test CORS after completion")
        
    elif platform == "render":
        print("🎨 Render Deployment:")
        print("1. Go to: https://dashboard.render.com/")
        print("2. Find your backend service")
        print("3. Click 'Manual Deploy'")
        print("4. Wait 5-10 minutes for deployment")
        print("5. Test CORS after completion")
        
    elif platform == "heroku":
        print("🟣 Heroku Deployment:")
        print("1. Go to: https://dashboard.heroku.com/apps")
        print("2. Find your app")
        print("3. Go to Deploy tab")
        print("4. Click 'Deploy Branch'")
        print("5. Wait 5-10 minutes for deployment")
        
    elif platform == "digitalocean":
        print("🌊 DigitalOcean Deployment:")
        print("1. Go to: https://cloud.digitalocean.com/apps")
        print("2. Find your app")
        print("3. Click 'Deploy'")
        print("4. Wait 5-10 minutes for deployment")
        
    else:
        print("❓ Platform not detected automatically")
        print("\nTry these common platforms:")
        print("1. 🚂 Railway: https://railway.app/dashboard")
        print("2. 🎨 Render: https://dashboard.render.com/")
        print("3. 🟣 Heroku: https://dashboard.heroku.com/apps")
        print("4. 🌊 DigitalOcean: https://cloud.digitalocean.com/apps")
        print("5. ☁️ Vercel: https://vercel.com/dashboard")

def check_github_webhooks():
    """Suggest checking GitHub webhooks"""
    print("\n🔗 Check GitHub Repository Settings")
    print("=" * 40)
    
    print("1. Go to your GitHub repository:")
    print("   https://github.com/flowtasksdev/ultimate-co-founder")
    print("2. Click Settings → Webhooks")
    print("3. Look for webhook URLs that might indicate hosting platform:")
    print("   - railway.app")
    print("   - render.com") 
    print("   - heroku.com")
    print("   - digitalocean.com")
    print("   - vercel.com")

def test_cors_current():
    """Test current CORS status"""
    print("\n🧪 Testing Current CORS Status")
    print("=" * 40)
    
    stdout, stderr, code = run_command("curl -s -o /dev/null -w '%{http_code}' https://api.ai-co-founder.com/api/health")
    if code == 0:
        print("Backend HTTP Status: " + stdout)
        if stdout == "200":
            print("✅ Backend is responding")
            print("❌ But CORS headers are missing (as seen in browser)")
        else:
            print("⚠️ Backend may have issues (status: " + stdout + ")")
    else:
        print("❌ Cannot reach backend")

def main():
    print("🔍 Backend Hosting Platform Detection")
    print("=" * 50)
    print()
    print("🎯 Goal: Find where https://api.ai-co-founder.com is hosted")
    print("📍 Issue: CORS fixes need to be deployed to backend")
    print()
    
    # Test current CORS status
    test_cors_current()
    
    # Try to detect platform
    dns_platform = check_dns_info()
    header_platform = check_http_headers()
    
    # Determine most likely platform
    if dns_platform != "unknown":
        detected_platform = dns_platform
    elif header_platform != "unknown":
        detected_platform = header_platform
    else:
        detected_platform = "unknown"
    
    print("\n" + "=" * 50)
    print("📊 DETECTION RESULTS")
    print("=" * 50)
    
    if detected_platform != "unknown":
        print("🎯 Likely Platform: " + detected_platform.upper())
    else:
        print("❓ Platform: Could not detect automatically")
    
    provide_deployment_instructions(detected_platform)
    check_github_webhooks()
    
    print("\n" + "=" * 50)
    print("⚡ IMMEDIATE ACTION REQUIRED")
    print("=" * 50)
    
    print("1. 🔍 Check the platforms above for your backend service")
    print("2. 🚀 Deploy/redeploy your backend service")
    print("3. ⏱️ Wait 5-10 minutes for deployment")
    print("4. 🧪 Test CORS:")
    print("   Open https://aicofounder.site")
    print("   Run: fetch('https://api.ai-co-founder.com/api/health')")
    print("   Should see success instead of CORS error")
    print()
    print("🎯 The CORS fixes are ready in your code - just need deployment!")

if __name__ == "__main__":
    main()
