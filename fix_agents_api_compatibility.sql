-- FIX AGENTS API COMPATIBILITY
-- The API expects system_prompt directly in the agents table
-- This adds the missing fields and updates existing agents

-- First, ensure the account exists in basejump.accounts
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Add system_prompt column to agents table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'system_prompt' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN system_prompt text;
    END IF;
    
    -- Add other missing columns that the API expects
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_default' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_default boolean DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_public' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_public boolean DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'configured_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN configured_mcps jsonb DEFAULT '[]'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'custom_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN custom_mcps jsonb DEFAULT '[]'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'agentpress_tools' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN agentpress_tools jsonb DEFAULT '{}'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'created_at' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN created_at timestamptz DEFAULT now();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'updated_at' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN updated_at timestamptz DEFAULT now();
    END IF;
END $$;

-- Add version_name column to agent_versions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_versions' AND column_name = 'version_name' AND table_schema = 'public') THEN
        ALTER TABLE public.agent_versions ADD COLUMN version_name text;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_versions' AND column_name = 'is_active' AND table_schema = 'public') THEN
        ALTER TABLE public.agent_versions ADD COLUMN is_active boolean DEFAULT true;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_versions' AND column_name = 'created_at' AND table_schema = 'public') THEN
        ALTER TABLE public.agent_versions ADD COLUMN created_at timestamptz DEFAULT now();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_versions' AND column_name = 'updated_at' AND table_schema = 'public') THEN
        ALTER TABLE public.agent_versions ADD COLUMN updated_at timestamptz DEFAULT now();
    END IF;
END $$;

-- Safe cleanup that respects foreign key constraints
UPDATE public.agents SET current_version_id = NULL;
DELETE FROM public.agent_versions;
DELETE FROM public.agents;

-- Create agents step by step to handle foreign key constraints properly
DO $$
DECLARE
    suna_agent_id uuid := gen_random_uuid();
    code_agent_id uuid := gen_random_uuid();
    marketing_agent_id uuid := gen_random_uuid();
    suna_version_id uuid;
    code_version_id uuid;
    marketing_version_id uuid;
    agent_record record;
BEGIN
    -- Step 1: Insert agents first (without current_version_id)
    INSERT INTO public.agents (
        agent_id,
        account_id,
        name,
        description,
        role,
        system_prompt,
        is_default,
        is_public,
        configured_mcps,
        custom_mcps,
        agentpress_tools,
        created_at,
        updated_at
    ) VALUES
    (
        suna_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Suna',
        'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
        'assistant',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        true,
        false,
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        now(),
        now()
    ),
    (
        code_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Code Assistant',
        'A specialized AI assistant for coding, debugging, and technical development tasks.',
        'assistant',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        false,
        false,
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        now(),
        now()
    ),
    (
        marketing_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Marketing Advisor',
        'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
        'assistant',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        false,
        false,
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        now(),
        now()
    );

    -- Verify agents were created
    FOR agent_record IN SELECT agent_id, name FROM public.agents WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid LOOP
        RAISE NOTICE 'Created agent: % with ID: %', agent_record.name, agent_record.agent_id;
    END LOOP;

    -- Step 2: Create agent versions (now that agents exist)
    INSERT INTO public.agent_versions (
        agent_id,
        version_number,
        name,
        version_name,
        system_prompt,
        is_active,
        created_at,
        updated_at
    ) VALUES
    (
        suna_agent_id,
        1,
        'Suna v1.0',
        'v1.0',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        true,
        now(),
        now()
    ),
    (
        code_agent_id,
        1,
        'Code Assistant v1.0',
        'v1.0',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        true,
        now(),
        now()
    ),
    (
        marketing_agent_id,
        1,
        'Marketing Advisor v1.0',
        'v1.0',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        true,
        now(),
        now()
    );

    -- Step 3: Get the version IDs and update current_version_id
    SELECT version_id INTO suna_version_id FROM public.agent_versions WHERE agent_id = suna_agent_id AND version_number = 1;
    SELECT version_id INTO code_version_id FROM public.agent_versions WHERE agent_id = code_agent_id AND version_number = 1;
    SELECT version_id INTO marketing_version_id FROM public.agent_versions WHERE agent_id = marketing_agent_id AND version_number = 1;

    -- Step 4: Update agents to reference their current versions
    UPDATE public.agents SET current_version_id = suna_version_id WHERE agent_id = suna_agent_id;
    UPDATE public.agents SET current_version_id = code_version_id WHERE agent_id = code_agent_id;
    UPDATE public.agents SET current_version_id = marketing_version_id WHERE agent_id = marketing_agent_id;

    RAISE NOTICE 'Successfully created 3 agents with full API compatibility';
    RAISE NOTICE 'Suna agent: % with version: %', suna_agent_id, suna_version_id;
    RAISE NOTICE 'Code agent: % with version: %', code_agent_id, code_version_id;
    RAISE NOTICE 'Marketing agent: % with version: %', marketing_agent_id, marketing_version_id;
END $$;

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';
