-- Comprehensive fix for messages table schema inconsistencies
-- This addresses the message sending failure and agent execution issues

BEGIN;

-- First, let's check the current table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'messages' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Ensure we have the correct primary key column
DO $$
BEGIN
    -- Check if we have 'id' column (standard)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'id'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE 'Using existing id column as primary key';
    -- Check if we have 'message_id' column instead
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'message_id'
        AND table_schema = 'public'
    ) THEN
        -- Rename message_id to id for consistency
        ALTER TABLE messages RENAME COLUMN message_id TO id;
        RAISE NOTICE 'Renamed message_id column to id';
    ELSE
        -- Add id column if neither exists
        ALTER TABLE messages ADD COLUMN id UUID PRIMARY KEY DEFAULT gen_random_uuid();
        RAISE NOTICE 'Added id column as primary key';
    END IF;
END $$;

-- Ensure we have the 'role' column (standard for message types)
DO $$
BEGIN
    -- Check if we have 'role' column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'role'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE 'role column already exists';
    -- Check if we have 'type' column that we can rename
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'type'
        AND table_schema = 'public'
    ) THEN
        -- Rename 'type' to 'role' for consistency
        ALTER TABLE messages RENAME COLUMN type TO role;
        RAISE NOTICE 'Renamed type column to role';
    ELSE
        -- Add role column from scratch
        ALTER TABLE messages ADD COLUMN role TEXT NOT NULL DEFAULT 'user';
        RAISE NOTICE 'Added role column';
    END IF;
END $$;

-- Ensure thread_id column exists and has proper foreign key
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'thread_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN thread_id UUID REFERENCES threads(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added thread_id column';
    END IF;
END $$;

-- Ensure content column exists and is JSONB
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'content'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN content JSONB NOT NULL DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Added content column as JSONB';
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'content'
        AND data_type = 'text'
        AND table_schema = 'public'
    ) THEN
        -- Convert content from TEXT to JSONB
        ALTER TABLE messages ALTER COLUMN content TYPE JSONB USING 
            CASE 
                WHEN content ~ '^[\[\{]' THEN content::jsonb
                ELSE json_build_object('content', content)::jsonb
            END;
        RAISE NOTICE 'Converted content column to JSONB';
    END IF;
END $$;

-- Ensure metadata column exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'metadata'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN metadata JSONB DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Added metadata column';
    END IF;
END $$;

-- Ensure is_llm_message column exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'is_llm_message'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;
        RAISE NOTICE 'Added is_llm_message column';
    END IF;
END $$;

-- Ensure timestamp columns exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'created_at'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT now();
        RAISE NOTICE 'Added created_at column';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'updated_at'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();
        RAISE NOTICE 'Added updated_at column';
    END IF;
END $$;

-- Drop any restrictive constraints on role column
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_role_check;
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_type_check;

-- Add comprehensive constraint for role column
ALTER TABLE messages ADD CONSTRAINT messages_role_check 
CHECK (role IN (
    'user', 
    'assistant', 
    'system', 
    'tool', 
    'status', 
    'assistant_response_end',
    'assistant_response_start',
    'tool_result',
    'error'
));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_thread_id ON messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_messages_role ON messages(role);
CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);

-- Update existing messages to have proper is_llm_message values
UPDATE messages 
SET is_llm_message = false 
WHERE role = 'user' AND is_llm_message = true;

UPDATE messages 
SET is_llm_message = true 
WHERE role IN ('assistant', 'system', 'tool', 'status') AND is_llm_message = false;

-- Show final table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'messages' 
AND table_schema = 'public'
ORDER BY ordinal_position;

COMMIT;

SELECT 'Messages schema fix completed successfully!' as status;
