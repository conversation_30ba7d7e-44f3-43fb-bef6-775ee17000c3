-- FIX ALL MISSING TABLES AND PERMISSIONS
-- Complete setup for Suna database

-- Create all missing tables first
CREATE TABLE IF NOT EXISTS public.projects (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    status text DEFAULT 'active',
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

CREATE TABLE IF NOT EXISTS public.threads (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE,
    title text,
    status text DEFAULT 'active',
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

CREATE TABLE IF NOT EXISTS public.messages (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    thread_id uuid REFERENCES public.threads(id) ON DELETE CASCADE,
    account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    role text NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content text,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

-- Add missing columns to existing tables if they don't exist
DO $$
BEGIN
    -- Add missing columns to projects table if they don't exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects' AND table_schema = 'public') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'status' AND table_schema = 'public') THEN
            ALTER TABLE public.projects ADD COLUMN status text DEFAULT 'active';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'metadata' AND table_schema = 'public') THEN
            ALTER TABLE public.projects ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
        END IF;
    END IF;

    -- Add missing columns to threads table if they don't exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'threads' AND table_schema = 'public') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'status' AND table_schema = 'public') THEN
            ALTER TABLE public.threads ADD COLUMN status text DEFAULT 'active';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'metadata' AND table_schema = 'public') THEN
            ALTER TABLE public.threads ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
        END IF;
    END IF;
END $$;

-- Ensure proper permissions
GRANT ALL ON TABLE public.projects TO authenticated, service_role;
GRANT ALL ON TABLE public.threads TO authenticated, service_role;
GRANT ALL ON TABLE public.messages TO authenticated, service_role;
GRANT ALL ON TABLE public.agents TO authenticated, service_role;
GRANT ALL ON TABLE public.agent_versions TO authenticated, service_role;

-- Enable RLS
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_versions ENABLE ROW LEVEL SECURITY;

-- Drop and recreate policies for projects to ensure they work
DROP POLICY IF EXISTS "Users can view projects in their accounts" ON public.projects;
DROP POLICY IF EXISTS "Users can insert projects in their accounts" ON public.projects;
DROP POLICY IF EXISTS "Users can update projects in their accounts" ON public.projects;
DROP POLICY IF EXISTS "Users can delete projects in their accounts" ON public.projects;

-- Create comprehensive policies for projects
CREATE POLICY "Users can view projects in their accounts" ON public.projects
    FOR SELECT TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can insert projects in their accounts" ON public.projects
    FOR INSERT TO authenticated WITH CHECK (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can update projects in their accounts" ON public.projects
    FOR UPDATE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can delete projects in their accounts" ON public.projects
    FOR DELETE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

-- Create timestamp triggers for all tables
DROP TRIGGER IF EXISTS set_timestamps_projects ON public.projects;
CREATE TRIGGER set_timestamps_projects BEFORE INSERT OR UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

DROP TRIGGER IF EXISTS set_timestamps_threads ON public.threads;
CREATE TRIGGER set_timestamps_threads BEFORE INSERT OR UPDATE ON public.threads
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

DROP TRIGGER IF EXISTS set_timestamps_messages ON public.messages;
CREATE TRIGGER set_timestamps_messages BEFORE INSERT OR UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

-- Create some sample projects if none exist
INSERT INTO public.projects (account_id, name, description, status)
SELECT
    id,
    'Welcome Project',
    'Your first project to get started with Suna',
    'active'
FROM basejump.accounts
WHERE id NOT IN (SELECT DISTINCT account_id FROM public.projects WHERE account_id IS NOT NULL)
ON CONFLICT DO NOTHING;

-- Create some sample threads if none exist
INSERT INTO public.threads (account_id, project_id, title, status)
SELECT
    a.id,
    p.id,
    'Welcome Chat',
    'active'
FROM basejump.accounts a
JOIN public.projects p ON p.account_id = a.id
WHERE NOT EXISTS (
    SELECT 1 FROM public.threads t
    WHERE t.account_id = a.id AND t.project_id = p.id
)
ON CONFLICT DO NOTHING;
