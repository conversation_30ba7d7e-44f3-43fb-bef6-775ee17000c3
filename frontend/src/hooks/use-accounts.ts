import useSWR, { SWRConfiguration } from 'swr';
import { createClient } from '@/lib/supabase/client';
import { GetAccountsResponse } from '@usebasejump/shared';

export const useAccounts = (options?: SWRConfiguration) => {
  const supabaseClient = createClient();
  return useSWR<GetAccountsResponse>(
    !!supabaseClient && ['accounts'],
    async () => {
      try {
        const { data, error } = await supabaseClient.rpc('get_accounts');

        if (error) {
          throw new Error(error.message);
        }

        return data;
      } catch (error) {
        console.log('get_accounts function not available, using fallback');
        // Fallback: get user data directly and create a personal account
        try {
          const { data: { user } } = await supabaseClient.auth.getUser();
          if (user) {
            return [{
              account_id: user.id,
              name: user.user_metadata?.name || user.email,
              email: user.email,
              personal_account: true,
              account_role: 'owner',
              is_primary_owner: true,
              slug: null,
              created_at: user.created_at,
              updated_at: user.updated_at
            }];
          }
        } catch (fallbackError) {
          console.error('Failed to get user data:', fallbackError);
        }

        return [];
      }
    },
    options,
  );
};
