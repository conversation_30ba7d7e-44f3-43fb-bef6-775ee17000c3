'use client';

import { createQueryHook } from '@/hooks/use-query';
import { checkApiHealth } from '@/lib/api';
import { healthKeys } from '../files/keys';

export const useApiHealth = createQueryHook(
  healthKeys.api(),
  checkApiHealth,
  {
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    refetchOnMount: false, // Only refetch if stale
    refetchOnReconnect: true, // Refetch when connection is restored
    retry: (failureCount, error) => {
      // Don't retry on network errors to prevent spam
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        return false;
      }
      // Limit retries to 1 for other errors
      return failureCount < 1;
    },
    retryDelay: 5000, // Wait 5 seconds between retries
  }
);