// Polyfill for Promise.withResolvers for Node.js < 22
if (typeof Promise.withResolvers === 'undefined') {
  (Promise as any).withResolvers = function<T>() {
    let resolve: (value: T | PromiseLike<T>) => void;
    let reject: (reason?: any) => void;
    const promise = new Promise<T>((res, rej) => {
      resolve = res;
      reject = rej;
    });
    return { promise, resolve: resolve!, reject: reject! };
  };
}

import { ThemeProvider } from '@/components/home/<USER>';
import { siteConfig } from '@/lib/site';
import type { Metadata, Viewport } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import { Toaster } from '@/components/ui/sonner';
import { Analytics } from '@vercel/analytics/react';
import { GoogleAnalytics } from '@next/third-parties/google';
import { SpeedInsights } from '@vercel/speed-insights/next';
import Script from 'next/script';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const viewport: Viewport = {
  themeColor: 'black',
};

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  description:
    'Your co-founder who gets things done. AI Co-Founder helps you accomplish real-world tasks with ease through natural conversation, becoming your digital companion for business strategy, research, and everyday challenges.',
  keywords: [
    'AI',
    'artificial intelligence',
    'browser automation',
    'web scraping',
    'file management',
    'AI assistant',
    'open source',
    'research',
    'data analysis',
  ],
  authors: [{ name: 'AI Co-Founder Team', url: 'http://localhost:3000' }],
  creator: 'AI Co-Founder Team',
  publisher: 'AI Co-Founder Team',
  category: 'Technology',
  applicationName: 'AI Co-Founder',
  formatDetection: {
    telephone: false,
    email: false,
    address: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    title: 'AI Co-Founder - AI Assistant',
    description:
      'AI Co-Founder is an AI assistant that helps you accomplish real-world tasks with ease through natural conversation.',
    url: siteConfig.url,
    siteName: 'AI Co-Founder',
    images: [
      {
        url: '/banner.png',
        width: 1200,
        height: 630,
        alt: 'AI Co-Founder - AI Assistant',
        type: 'image/png',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Co-Founder - AI Assistant',
    description:
      'AI Co-Founder is an AI assistant that helps you accomplish real-world tasks with ease through natural conversation.',
    creator: '@aicxo',
    site: '@aicxo',
    images: [
      {
        url: '/banner.png',
        width: 1200,
        height: 630,
        alt: 'AI Co-Founder - AI Assistant',
      },
    ],
  },
  icons: {
    icon: [{ url: '/favicon.svg', type: 'image/svg+xml' }],
    shortcut: '/favicon.svg',
  },
  // manifest: "/manifest.json",
  alternates: {
    canonical: siteConfig.url,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Google Tag Manager */}
        <Script id="google-tag-manager" strategy="afterInteractive">
          {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-PCHSN4M2');`}
        </Script>
        {process.env.NEXT_PUBLIC_TOLT_REFERRAL_ID && (
          <Script async src="https://cdn.tolt.io/tolt.js" data-tolt={process.env.NEXT_PUBLIC_TOLT_REFERRAL_ID}></Script>
        )}

        {/* Google Analytics */}
        <Script async src="https://www.googletagmanager.com/gtag/js?id=G-Y3QQPVLHDC" />
        <Script id="google-analytics">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-Y3QQPVLHDC');
          `}
        </Script>
      </head>

      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased font-sans min-h-screen`}
        suppressHydrationWarning={true}
      >
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-PCHSN4M2"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          />
        </noscript>
        {/* End Google Tag Manager (noscript) */}

        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          <Providers>
            {children}
            <Toaster />
          </Providers>
          {/* Only load Vercel Analytics if VERCEL environment is detected */}
          {process.env.VERCEL && <Analytics />}
          <GoogleAnalytics gaId="G-Y3QQPVLHDC" />
          {process.env.VERCEL && <SpeedInsights />}
        </ThemeProvider>

        {/* Suppress MetaMask console errors */}
        <Script id="suppress-metamask-errors" strategy="afterInteractive">
          {`
            // Suppress MetaMask-related console errors
            const originalError = console.error;
            console.error = function(...args) {
              const message = args.join(' ');
              if (
                message.includes('MetaMask') ||
                message.includes('inpage.js') ||
                message.includes('Failed to connect to MetaMask') ||
                message.includes('MetaMask extension not found')
              ) {
                return; // Suppress MetaMask errors
              }
              originalError.apply(console, args);
            };
          `}
        </Script>
      </body>
    </html>
  );
}
