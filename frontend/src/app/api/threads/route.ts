import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return empty threads for development
    return NextResponse.json(
      {
        threads: [],
        total: 0,
        page: 1,
        limit: 20
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Threads fetch error:', error);
    return NextResponse.json(
      { 
        threads: [],
        error: 'Failed to fetch threads'
      },
      { status: 200 } // Return 200 to prevent errors
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Create a new thread
    const newThread = {
      id: `thread_${Date.now()}`,
      title: body.title || 'New Thread',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      message_count: 0
    };
    
    return NextResponse.json(newThread, { status: 201 });
  } catch (error) {
    console.error('Thread creation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create thread'
      },
      { status: 500 }
    );
  }
}
