import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return feature flag status for agent marketplace
    return NextResponse.json(
      {
        enabled: true,
        feature: 'agent_marketplace',
        description: 'Enables the agent marketplace functionality'
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Feature flag error:', error);
    return NextResponse.json(
      { 
        enabled: true, // Default to enabled for development
        error: 'Failed to fetch feature flag'
      },
      { status: 200 } // Return 200 to prevent errors
    );
  }
}
