import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return a default subscription for development
    return NextResponse.json(
      {
        plan: 'free',
        status: 'active',
        usage: {
          current: 0,
          limit: 1000
        },
        features: {
          custom_agents: true,
          advanced_models: false,
          priority_support: false
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Billing subscription error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch subscription',
        plan: 'free',
        status: 'active'
      },
      { status: 200 } // Return 200 to prevent errors
    );
  }
}
