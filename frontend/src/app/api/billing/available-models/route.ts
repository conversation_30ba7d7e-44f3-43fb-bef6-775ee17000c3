import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return available models for development
    return NextResponse.json(
      {
        models: [
          {
            id: 'gemini/gemini-2.0-flash-exp',
            display_name: 'Gemini 2.0 Flash (Experimental)',
            short_name: 'Gemini 2.0',
            requires_subscription: false,
            is_available: true,
            input_cost_per_million_tokens: 0.075,
            output_cost_per_million_tokens: 0.30,
            max_tokens: 8192
          },
          {
            id: 'gpt-4o',
            display_name: 'GPT-4o',
            short_name: 'GPT-4o',
            requires_subscription: true,
            is_available: true,
            input_cost_per_million_tokens: 2.50,
            output_cost_per_million_tokens: 10.00,
            max_tokens: 4096
          },
          {
            id: 'claude-3-5-sonnet-20241022',
            display_name: 'Claude 3.5 Sonnet',
            short_name: 'Claude 3.5',
            requires_subscription: true,
            is_available: true,
            input_cost_per_million_tokens: 3.00,
            output_cost_per_million_tokens: 15.00,
            max_tokens: 8192
          }
        ],
        subscription_tier: 'free',
        total_models: 3
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Available models error:', error);
    return NextResponse.json(
      { 
        models: [],
        error: 'Failed to fetch available models'
      },
      { status: 200 } // Return 200 to prevent errors
    );
  }
}
