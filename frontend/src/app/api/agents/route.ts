import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return default agents for development
    return NextResponse.json(
      {
        agents: [
          {
            id: 'coo',
            name: 'COO',
            description: 'General purpose AI assistant',
            avatar: null,
            is_default: true,
            created_at: new Date().toISOString()
          },
          {
            id: 'code-assistant',
            name: 'Code Assistant',
            description: 'Specialized in coding and development tasks',
            avatar: null,
            is_default: false,
            created_at: new Date().toISOString()
          }
        ]
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Agents fetch error:', error);
    return NextResponse.json(
      { 
        agents: [],
        error: 'Failed to fetch agents'
      },
      { status: 200 } // Return 200 to prevent errors
    );
  }
}
