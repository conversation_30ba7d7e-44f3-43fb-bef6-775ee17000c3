import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    return NextResponse.json(
      {
        status: 'ok',
        health: 'healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        services: {
          api: 'healthy',
          database: 'healthy',
          cache: 'healthy'
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      {
        status: 'error',
        health: 'unhealthy',
        message: 'Health check failed'
      },
      { status: 500 }
    );
  }
}
