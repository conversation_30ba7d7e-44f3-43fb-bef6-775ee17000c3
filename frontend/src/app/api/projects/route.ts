import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return empty projects for development
    return NextResponse.json(
      {
        projects: [],
        total: 0,
        page: 1,
        limit: 20
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Projects fetch error:', error);
    return NextResponse.json(
      { 
        projects: [],
        error: 'Failed to fetch projects'
      },
      { status: 200 } // Return 200 to prevent errors
    );
  }
}
