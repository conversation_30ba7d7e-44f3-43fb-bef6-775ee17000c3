import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ threadId: string }> }
) {
  try {
    const { threadId } = await params;
    
    // Handle demo threads
    if (threadId.startsWith('demo-thread-')) {
      return NextResponse.json(
        {
          id: threadId,
          title: 'Demo Conversation',
          status: 'demo_mode',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          messages: [
            {
              id: `msg-${Date.now()}`,
              role: 'assistant',
              content: 'This is a demo conversation. The backend service is being configured for production. Your message was received successfully!',
              timestamp: new Date().toISOString()
            }
          ],
          demo: true
        },
        { status: 200 }
      );
    }
    
    // For non-demo threads, return not found
    return NextResponse.json(
      { 
        error: 'Thread not found',
        message: 'The requested thread does not exist or is not accessible'
      },
      { status: 404 }
    );
  } catch (error) {
    console.error('Thread fetch error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch thread',
        message: 'An error occurred while fetching the thread'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ threadId: string }> }
) {
  try {
    const { threadId } = await params;
    const body = await request.json();
    
    // Handle demo threads
    if (threadId.startsWith('demo-thread-')) {
      return NextResponse.json(
        {
          id: `msg-${Date.now()}`,
          thread_id: threadId,
          role: 'assistant',
          content: `Thank you for your message: "${body.message || 'Hello!'}". This is a demo response while the backend is being configured. Your authentication and frontend are working correctly!`,
          timestamp: new Date().toISOString(),
          demo: true
        },
        { status: 200 }
      );
    }
    
    // For non-demo threads, return not found
    return NextResponse.json(
      { 
        error: 'Thread not found',
        message: 'Cannot post to this thread'
      },
      { status: 404 }
    );
  } catch (error) {
    console.error('Thread post error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to post to thread',
        message: 'An error occurred while posting to the thread'
      },
      { status: 500 }
    );
  }
}
