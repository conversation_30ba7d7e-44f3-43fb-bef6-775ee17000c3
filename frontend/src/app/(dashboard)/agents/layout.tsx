import { agentPlaygroundFlagFrontend } from '@/flags';
import { isFlagEnabled } from '@/lib/feature-flags';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Agent Conversation | AI Co-Founder',
  description: 'Interactive agent conversation powered by AI Co-Founder',
  openGraph: {
    title: 'Agent Conversation | AI Co-Founder',
    description: 'Interactive agent conversation powered by AI Co-Founder',
    type: 'website',
  },
};

export default async function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
