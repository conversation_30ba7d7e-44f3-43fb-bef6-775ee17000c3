'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useThreadsByProject } from '@/hooks/react-query/threads/use-thread-queries';
import { useProjectQuery } from '@/hooks/react-query/threads/use-project';
import { ThreadSkeleton } from '@/components/thread/content/ThreadSkeleton';
import { createThread } from '@/lib/api';
import { toast } from 'sonner';

interface ProjectPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function ProjectPage({ params }: ProjectPageProps) {
  const unwrappedParams = React.use(params);
  const { projectId } = unwrappedParams;
  const router = useRouter();
  
  const projectQuery = useProjectQuery(projectId);
  const threadsQuery = useThreadsByProject(projectId);

  useEffect(() => {
    async function handleRedirect() {
      // Wait for both queries to complete
      if (projectQuery.isLoading || threadsQuery.isLoading) {
        return;
      }

      // If there's an error with the project, redirect to dashboard
      if (projectQuery.isError) {
        console.error('Project not found, redirecting to dashboard');
        router.replace('/dashboard');
        return;
      }

      // If there are existing threads, redirect to the first one
      if (threadsQuery.data && threadsQuery.data.length > 0) {
        const firstThread = threadsQuery.data[0];
        console.log(`Redirecting to first thread: ${firstThread.id}`);
        router.replace(`/projects/${projectId}/thread/${firstThread.id}`);
        return;
      }

      // If no threads exist, create a new one
      if (threadsQuery.data && threadsQuery.data.length === 0) {
        try {
          console.log('No threads found, creating new thread for project:', projectId);
          const newThread = await createThread(projectId);

          if (newThread && newThread.id) {
            console.log(`Created new thread: ${newThread.id}`);
            router.replace(`/projects/${projectId}/thread/${newThread.id}`);
          } else {
            throw new Error('Failed to create thread');
          }
        } catch (error) {
          console.error('Error creating thread:', error);
          toast.error('Failed to create new thread');
          router.replace('/dashboard');
        }
      }
    }

    handleRedirect();
  }, [
    projectQuery.isLoading,
    projectQuery.isError,
    threadsQuery.isLoading,
    threadsQuery.data,
    projectId,
    router,
  ]);

  // Show loading skeleton while redirecting
  return <ThreadSkeleton isSidePanelOpen={false} />;
}
