'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useThreadsByProject } from '@/hooks/react-query/threads/use-thread-queries';
import { useProjectQuery } from '@/hooks/react-query/threads/use-project';
import { ThreadSkeleton } from '@/components/thread/content/ThreadSkeleton';
import { createThread } from '@/lib/api';
import { toast } from 'sonner';

interface ProjectPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function ProjectPage({ params }: ProjectPageProps) {
  const unwrappedParams = React.use(params);
  const { projectId } = unwrappedParams;
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);

  const projectQuery = useProjectQuery(projectId);
  const threadsQuery = useThreadsByProject(projectId);

  useEffect(() => {
    async function handleRedirect() {
      // Prevent multiple redirects
      if (hasRedirected) {
        return;
      }

      // Wait for both queries to complete
      if (projectQuery.isLoading || threadsQuery.isLoading) {
        console.log('Waiting for queries to complete...', {
          projectLoading: projectQuery.isLoading,
          threadsLoading: threadsQuery.isLoading
        });
        return;
      }

      console.log('Queries completed:', {
        projectError: projectQuery.isError,
        threadsData: threadsQuery.data,
        threadsLength: threadsQuery.data?.length
      });

      // If there's an error with the project, redirect to dashboard
      if (projectQuery.isError) {
        console.error('Project not found, redirecting to dashboard');
        setHasRedirected(true);
        router.replace('/dashboard');
        return;
      }

      // If there are existing threads, redirect to the first one
      if (threadsQuery.data && threadsQuery.data.length > 0) {
        const firstThread = threadsQuery.data[0];
        const threadId = firstThread.id || firstThread.thread_id;
        console.log(`Redirecting to first thread: ${threadId}`);
        setHasRedirected(true);
        router.replace(`/projects/${projectId}/thread/${threadId}`);
        return;
      }

      // If no threads exist, create a new one
      if (threadsQuery.data && threadsQuery.data.length === 0) {
        try {
          console.log('No threads found, creating new thread for project:', projectId);
          setHasRedirected(true);
          const newThread = await createThread(projectId);

          if (newThread && (newThread.id || newThread.thread_id)) {
            const threadId = newThread.id || newThread.thread_id;
            console.log(`Created new thread: ${threadId}`);
            router.replace(`/projects/${projectId}/thread/${threadId}`);
          } else {
            throw new Error('Failed to create thread - no ID returned');
          }
        } catch (error) {
          console.error('Error creating thread:', error);
          toast.error('Failed to create new thread');
          router.replace('/dashboard');
        }
      }
    }

    handleRedirect();
  }, [
    projectQuery.isLoading,
    projectQuery.isError,
    threadsQuery.isLoading,
    threadsQuery.data,
    projectId,
    router,
    hasRedirected,
  ]);

  // Show loading skeleton while redirecting
  return <ThreadSkeleton isSidePanelOpen={false} />;
}
