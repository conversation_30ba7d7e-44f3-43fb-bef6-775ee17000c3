import EditPersonalAccountName from '@/components/basejump/edit-personal-account-name';
import { createClient } from '@/lib/supabase/server';

export default async function PersonalAccountSettingsPage() {
  const supabaseClient = await createClient();

  let personalAccount = null;
  try {
    const { data } = await supabaseClient.rpc('get_personal_account');
    personalAccount = data;
  } catch (error) {
    console.log('get_personal_account function not available, using fallback');
    // Fallback: get user data directly
    try {
      const { data: { user } } = await supabaseClient.auth.getUser();
      if (user) {
        personalAccount = {
          account_id: user.id,
          name: user.user_metadata?.name || user.email,
          email: user.email,
          personal_account: true,
          account_role: 'owner'
        };
      }
    } catch (fallbackError) {
      console.error('Failed to get user data:', fallbackError);
    }
  }

  // Handle case where personal account is not found
  if (!personalAccount) {
    return (
      <div className="rounded-xl border shadow-sm bg-card p-6">
        <h2 className="text-xl font-semibold mb-4">Account Settings</h2>
        <div className="p-4 mb-4 bg-muted/30 border border-border rounded-lg text-center">
          <p className="text-sm text-muted-foreground">
            Unable to load account information. Please try refreshing the page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <EditPersonalAccountName account={personalAccount} />
    </div>
  );
}
