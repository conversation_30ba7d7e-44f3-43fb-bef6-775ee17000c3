import { createClient } from '@/lib/supabase/server';
import UsageLogs from '@/components/billing/usage-logs';

export default async function UsageLogsPage() {
  const supabaseClient = await createClient();
  const { data: personalAccount } = await supabaseClient.rpc(
    'get_personal_account',
  );

  // Handle case where personal account is not found
  if (!personalAccount || !personalAccount.account_id) {
    return (
      <div className="space-y-6">
        <div className="rounded-xl border shadow-sm bg-card p-6">
          <h2 className="text-xl font-semibold mb-4">Usage Logs</h2>
          <div className="p-4 mb-4 bg-muted/30 border border-border rounded-lg text-center">
            <p className="text-sm text-muted-foreground">
              Unable to load account information. Please try refreshing the page.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <UsageLogs accountId={personalAccount.account_id} />
    </div>
  );
}
