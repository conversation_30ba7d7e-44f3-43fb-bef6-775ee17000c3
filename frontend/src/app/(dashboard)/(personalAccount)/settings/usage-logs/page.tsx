import { createClient } from '@/lib/supabase/server';
import UsageLogs from '@/components/billing/usage-logs';

export default async function UsageLogsPage() {
  const supabaseClient = await createClient();

  let personalAccount = null;

  // Always try the fallback approach first since RPC functions may not be available
  try {
    const { data: { user } } = await supabaseClient.auth.getUser();
    if (user) {
      personalAccount = {
        account_id: user.id,
        name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
        email: user.email,
        personal_account: true,
        account_role: 'owner'
      };
    }
  } catch (fallbackError) {
    console.error('Failed to get user data:', fallbackError);

    // Try RPC as secondary option
    try {
      const { data } = await supabaseClient.rpc('get_personal_account');
      personalAccount = data;
    } catch (rpcError) {
      console.log('get_personal_account function not available');
    }
  }

  // Handle case where personal account is not found
  if (!personalAccount || !personalAccount.account_id) {
    return (
      <div className="space-y-6">
        <div className="rounded-xl border shadow-sm bg-card p-6">
          <h2 className="text-xl font-semibold mb-4">Usage Logs</h2>
          <div className="p-4 mb-4 bg-muted/30 border border-border rounded-lg text-center">
            <p className="text-sm text-muted-foreground">
              Please sign in to view your usage logs.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <UsageLogs accountId={personalAccount.account_id} />
    </div>
  );
}
