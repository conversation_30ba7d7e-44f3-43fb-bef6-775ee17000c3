import { createClient } from '@/lib/supabase/server';
import AccountBillingStatus from '@/components/billing/account-billing-status';
import { PricingSection } from '@/components/home/<USER>/pricing-section';

const returnUrl = process.env.NEXT_PUBLIC_URL as string;

export default async function PersonalAccountBillingPage() {
  const supabaseClient = await createClient();

  let personalAccount = null;

  // Always try the fallback approach first since RPC functions may not be available
  try {
    const { data: { user } } = await supabaseClient.auth.getUser();
    if (user) {
      personalAccount = {
        account_id: user.id,
        name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
        email: user.email,
        personal_account: true,
        account_role: 'owner'
      };
    }
  } catch (fallbackError) {
    console.error('Failed to get user data:', fallbackError);

    // Try RPC as secondary option
    try {
      const { data } = await supabaseClient.rpc('get_personal_account');
      personalAccount = data;
    } catch (rpcError) {
      console.log('get_personal_account function not available');
    }
  }

  // Handle case where personal account is not found
  if (!personalAccount || !personalAccount.account_id) {
    return (
      <div className="rounded-xl border shadow-sm bg-card p-6">
        <h2 className="text-xl font-semibold mb-4">Billing Status</h2>
        <div className="p-4 mb-4 bg-muted/30 border border-border rounded-lg text-center">
          <p className="text-sm text-muted-foreground">
            Please sign in to view your billing information.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Billing Status Section */}
      <div>
        <AccountBillingStatus
          accountId={personalAccount.account_id}
          returnUrl={`${returnUrl}/settings/billing`}
        />
      </div>

      {/* Pricing Plans Section */}
      <div className="border-t pt-8">
        <div className="mb-6">
          <h2 className="text-2xl font-semibold mb-2">Pricing Plans</h2>
          <p className="text-muted-foreground">
            Choose the plan that best fits your needs. You can upgrade or downgrade at any time.
          </p>
        </div>
        <PricingSection />
      </div>
    </div>
  );
}
