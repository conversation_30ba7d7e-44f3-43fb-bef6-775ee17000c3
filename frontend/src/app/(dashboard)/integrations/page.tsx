'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Plus, 
  Zap, 
  Store, 
  Server, 
  Settings, 
  ExternalLink,
  Bot,
  Sparkles
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { PipedreamRegistry } from '@/components/agents/pipedream/pipedream-registry';
import { CustomMCPDialog } from '@/components/agents/mcp/custom-mcp-dialog';

export default function IntegrationsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('browse');
  const [showRegistryDialog, setShowRegistryDialog] = useState(false);
  const [showCustomDialog, setShowCustomDialog] = useState(false);
  const [isConnecting, setIsConnecting] = useState<string | null>(null);

  const handleConnect = async (integrationName: string) => {
    setIsConnecting(integrationName);
    try {
      // Map integration names to provider IDs
      const providerMap: Record<string, string> = {
        'Slack': 'slack',
        'Discord': 'discord',
        'GitHub': 'github',
        'Notion': 'notion',
        'Google Sheets': 'google',
        'Telegram': 'telegram'
      };

      const providerId = providerMap[integrationName];
      if (!providerId) {
        console.log(`Integration ${integrationName} not yet implemented`);
        return;
      }

      // For OAuth providers, initiate OAuth flow
      const response = await fetch('/api/integrations/install', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: providerId,
          agent_id: 'default' // You might want to get this from context
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.install_url) {
          // Redirect to OAuth provider
          window.location.href = data.install_url;
        }
      } else {
        console.error('Failed to initiate integration:', await response.text());
      }
    } catch (error) {
      console.error('Error connecting integration:', error);
    } finally {
      setIsConnecting(null);
    }
  };

  // Tool logo components
  const getToolLogo = (name: string) => {
    const logoMap: Record<string, React.ReactNode> = {
      'Notion': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">N</span>
        </div>
      ),
      'Google Sheets': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">GS</span>
        </div>
      ),
      'Slack': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">S</span>
        </div>
      ),
      'GitHub': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">GH</span>
        </div>
      ),
      'Telegram': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">T</span>
        </div>
      ),
    };
    return logoMap[name] || (
      <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
        <span className="text-white font-bold text-sm">{name.charAt(0)}</span>
      </div>
    );
  };

  const popularIntegrations = [
    {
      name: 'Notion',
      description: 'Connect to your Notion workspace for document management',
      icon: getToolLogo('Notion'),
      category: 'Productivity',
      status: 'available'
    },
    {
      name: 'Google Sheets',
      description: 'Access and manipulate Google Sheets data',
      icon: getToolLogo('Google Sheets'),
      category: 'Data',
      status: 'available'
    },
    {
      name: 'Slack',
      description: 'Send messages and interact with Slack channels',
      icon: getToolLogo('Slack'),
      category: 'Communication',
      status: 'available'
    },
    {
      name: 'GitHub',
      description: 'Manage repositories, issues, and pull requests',
      icon: getToolLogo('GitHub'),
      category: 'Developer Tools',
      status: 'available'
    },
    {
      name: 'Telegram',
      description: 'Send and receive Telegram messages',
      icon: getToolLogo('Telegram'),
      category: 'Communication',
      status: 'available'
    },
    {
      name: 'OpenAI',
      description: 'Access OpenAI GPT models and APIs',
      icon: getToolLogo('OpenAI'),
      category: 'AI',
      status: 'available'
    },
    {
      name: 'Discord',
      description: 'Send messages and manage Discord servers',
      icon: getToolLogo('Discord'),
      category: 'Communication',
      status: 'available'
    },
    {
      name: 'Trello',
      description: 'Manage boards, cards, and team collaboration',
      icon: getToolLogo('Trello'),
      category: 'Productivity',
      status: 'available'
    },
    {
      name: 'Airtable',
      description: 'Access and manipulate Airtable databases',
      icon: getToolLogo('Airtable'),
      category: 'Data',
      status: 'available'
    },
    {
      name: 'Gmail',
      description: 'Send and receive emails through Gmail',
      icon: getToolLogo('Gmail'),
      category: 'Communication',
      status: 'available'
    },
    {
      name: 'Google Drive',
      description: 'Access and manage Google Drive files',
      icon: getToolLogo('Google Drive'),
      category: 'Storage',
      status: 'available'
    },
    {
      name: 'Dropbox',
      description: 'Access and manage Dropbox files',
      icon: getToolLogo('Dropbox'),
      category: 'Storage',
      status: 'available'
    },
    {
      name: 'Salesforce',
      description: 'Manage CRM data and customer relationships',
      icon: '☁️',
      category: 'CRM',
      status: 'available'
    },
    {
      name: 'HubSpot',
      description: 'Access HubSpot CRM and marketing tools',
      icon: '🎯',
      category: 'CRM',
      status: 'available'
    },
    {
      name: 'Stripe',
      description: 'Process payments and manage subscriptions',
      icon: '💳',
      category: 'Finance',
      status: 'available'
    },
    {
      name: 'Shopify',
      description: 'Manage e-commerce store and orders',
      icon: '🛒',
      category: 'E-commerce',
      status: 'available'
    },
    {
      name: 'Zoom',
      description: 'Schedule and manage Zoom meetings',
      icon: '📹',
      category: 'Communication',
      status: 'available'
    },
    {
      name: 'Calendly',
      description: 'Schedule appointments and manage availability',
      icon: '📅',
      category: 'Scheduling',
      status: 'available'
    },
    {
      name: 'Jira',
      description: 'Manage issues and project tracking',
      icon: '🎫',
      category: 'Project Management',
      status: 'available'
    },
    {
      name: 'Asana',
      description: 'Manage tasks and team projects',
      icon: '✅',
      category: 'Project Management',
      status: 'available'
    },
    {
      name: 'Monday.com',
      description: 'Manage workflows and team collaboration',
      icon: '📊',
      category: 'Project Management',
      status: 'available'
    },
    {
      name: 'Linear',
      description: 'Track issues and manage development workflow',
      icon: '📐',
      category: 'Developer Tools',
      status: 'available'
    },
    {
      name: 'Figma',
      description: 'Access and manage design files',
      icon: '🎨',
      category: 'Design',
      status: 'available'
    },
    {
      name: 'Twitter/X',
      description: 'Post tweets and manage social media',
      icon: '🐦',
      category: 'Social Media',
      status: 'available'
    }
  ];

  const filteredIntegrations = popularIntegrations.filter(integration =>
    integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    integration.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleToolsSelected = (profileId: string, selectedTools: string[], appName: string, appSlug: string) => {
    // Handle the integration setup
    console.log('Integration configured:', { profileId, selectedTools, appName, appSlug });
    setShowRegistryDialog(false);
  };

  const handleSaveCustomMCP = (mcpData: any) => {
    // Handle custom MCP save
    console.log('Custom MCP saved:', mcpData);
    setShowCustomDialog(false);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Integrations</h1>
        <p className="text-muted-foreground">
          Connect your agents to external services and tools through MCP servers and Pipedream integrations
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="browse" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            Browse
          </TabsTrigger>
          <TabsTrigger value="configured" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configured
          </TabsTrigger>
          <TabsTrigger value="custom" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            Custom
          </TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="mt-6">
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search integrations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={() => setShowRegistryDialog(true)} className="flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  Browse Apps
                </Button>
                <Button variant="outline" onClick={() => setShowCustomDialog(true)} className="flex items-center gap-2">
                  <Server className="h-4 w-4" />
                  Add Custom MCP
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredIntegrations.map((integration, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex-shrink-0">{integration.icon}</div>
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <Badge variant="secondary" className="text-xs mt-1" style={{ backgroundColor: '#3c3e3e', color: 'white' }}>
                            {integration.category}
                          </Badge>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {integration.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <CardDescription className="mb-4">
                      {integration.description}
                    </CardDescription>
                    <Button
                      size="sm"
                      className="w-full"
                      style={{ backgroundColor: '#3c3e3e', color: 'white' }}
                      onClick={() => handleConnect(integration.name)}
                      disabled={isConnecting === integration.name}
                    >
                      {isConnecting === integration.name ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Connecting...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4 mr-2" />
                          Connect
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="configured" className="mt-6">
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <Zap className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No integrations configured</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Configure integrations through your agents to see them here. Each agent can have its own set of integrations.
            </p>
            <Button onClick={() => setActiveTab('browse')}>
              Browse Integrations
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="custom" className="mt-6">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Custom MCP Servers
                </CardTitle>
                <CardDescription>
                  Add custom Model Context Protocol (MCP) servers to extend your agents with specialized tools and capabilities.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
                    <Server className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h4 className="text-sm font-medium mb-2">No custom MCPs configured</h4>
                  <p className="text-sm text-muted-foreground mb-4 max-w-sm mx-auto">
                    Add custom MCP servers to connect to specialized tools and services
                  </p>
                  <Button onClick={() => setShowCustomDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Custom MCP
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ExternalLink className="h-5 w-5" />
                  Learn More
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div>
                    <p className="font-medium">MCP Documentation</p>
                    <p className="text-sm text-muted-foreground">Learn how to create and configure MCP servers</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open('https://modelcontextprotocol.io/docs', '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div>
                    <p className="font-medium">Pipedream Integration Guide</p>
                    <p className="text-sm text-muted-foreground">Connect to 2000+ apps through Pipedream</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open('https://pipedream.com/docs', '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <Dialog open={showRegistryDialog} onOpenChange={setShowRegistryDialog}>
        <DialogContent className="p-0 max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="sr-only">
            <DialogTitle>Select Integration</DialogTitle>
          </DialogHeader>
          <PipedreamRegistry onToolsSelected={handleToolsSelected} />
        </DialogContent>
      </Dialog>

      <CustomMCPDialog
        open={showCustomDialog}
        onOpenChange={setShowCustomDialog}
        onSave={handleSaveCustomMCP}
      />
    </div>
  );
}
