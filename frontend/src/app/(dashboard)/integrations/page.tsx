'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import {
  Search,
  Plus,
  Zap,
  Store,
  Server,
  Settings,
  ExternalLink,
  Bot,
  Sparkles,
  Loader2,
  CheckCircle,
  Clock
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { PipedreamRegistry } from '@/components/agents/pipedream/pipedream-registry';
import { CustomMCPDialog } from '@/components/agents/mcp/custom-mcp-dialog';
import { useQuery } from '@tanstack/react-query';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

interface Integration {
  id: string;
  name: string;
  provider: string;
  status: 'available' | 'coming_soon' | 'beta';
  description: string;
  category: string;
  logo: string;
  color: string;
  features: string[];
  setup_time: string;
  popularity: number;
}

// Fetch integrations from API
const fetchIntegrations = async (): Promise<{ integrations: Integration[] }> => {
  const response = await fetch(`${API_URL}/integrations`);
  if (!response.ok) {
    throw new Error('Failed to fetch integrations');
  }
  return response.json();
};

export default function IntegrationsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('browse');
  const [showRegistryDialog, setShowRegistryDialog] = useState(false);
  const [showCustomDialog, setShowCustomDialog] = useState(false);
  const [isConnecting, setIsConnecting] = useState<string | null>(null);

  // Fetch integrations data
  const { data: integrationsData, isLoading, error } = useQuery({
    queryKey: ['integrations'],
    queryFn: fetchIntegrations,
  });

  const handleConnect = async (integrationName: string) => {
    setIsConnecting(integrationName);
    try {
      // Map integration names to provider IDs
      const providerMap: Record<string, string> = {
        'Slack': 'slack',
        'Discord': 'discord',
        'GitHub': 'github',
        'Notion': 'notion',
        'Google Sheets': 'google',
        'Telegram': 'telegram'
      };

      const providerId = providerMap[integrationName];
      if (!providerId) {
        console.log(`Integration ${integrationName} not yet implemented`);
        return;
      }

      // For OAuth providers, initiate OAuth flow
      const response = await fetch('/api/integrations/install', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: providerId,
          agent_id: 'default' // You might want to get this from context
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.install_url) {
          // Open OAuth provider in new tab/window
          window.open(data.install_url, '_blank', 'noopener,noreferrer');
        } else {
          console.error('No install URL provided:', data);
        }
      } else {
        const errorText = await response.text();
        console.error('Failed to initiate integration:', errorText);
        alert(`Failed to connect ${integrationName}. Please try again.`);
      }
    } catch (error) {
      console.error('Error connecting integration:', error);
    } finally {
      setIsConnecting(null);
    }
  };

  // Tool logo components
  const getToolLogo = (name: string) => {
    const logoMap: Record<string, React.ReactNode> = {
      'Notion': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">N</span>
        </div>
      ),
      'Google Sheets': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">GS</span>
        </div>
      ),
      'Slack': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">S</span>
        </div>
      ),
      'GitHub': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">GH</span>
        </div>
      ),
      'Telegram': (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
          <span className="text-white font-bold text-sm">T</span>
        </div>
      ),
    };
    return logoMap[name] || (
      <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#3c3e3e' }}>
        <span className="text-white font-bold text-sm">{name.charAt(0)}</span>
      </div>
    );
  };

  // Get integrations from API or fallback to empty array
  const apiIntegrations = integrationsData?.integrations || [];

  // Render integration logo
  const renderIntegrationLogo = (integration: Integration) => {
    if (integration.logo.startsWith('http')) {
      return (
        <div className="w-8 h-8 rounded-lg flex items-center justify-center p-1" style={{ backgroundColor: integration.color + '20' }}>
          <img
            src={integration.logo}
            alt={integration.name}
            className="w-6 h-6"
            style={{ filter: `brightness(0) saturate(100%) invert(1)` }}
          />
        </div>
      );
    }
    return (
      <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: integration.color }}>
        <span className="text-white font-bold text-sm">{integration.name.charAt(0)}</span>
      </div>
    );
  };

  // Get status badge variant and color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return {
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800',
          icon: <CheckCircle className="h-3 w-3 mr-1" />
        };
      case 'coming_soon':
        return {
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-800',
          icon: <Clock className="h-3 w-3 mr-1" />
        };
      case 'beta':
        return {
          variant: 'outline' as const,
          className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
          icon: <Sparkles className="h-3 w-3 mr-1" />
        };
      default:
        return { variant: 'secondary' as const, className: '', icon: null };
    }
  };

  // Filter integrations based on search query
  const filteredIntegrations = apiIntegrations.filter(integration =>
    integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    integration.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    integration.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleToolsSelected = (profileId: string, selectedTools: string[], appName: string, appSlug: string) => {
    // Handle the integration setup
    console.log('Integration configured:', { profileId, selectedTools, appName, appSlug });
    setShowRegistryDialog(false);
  };

  const handleSaveCustomMCP = (mcpData: any) => {
    // Handle custom MCP save
    console.log('Custom MCP saved:', mcpData);
    setShowCustomDialog(false);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Integrations</h1>
        <p className="text-muted-foreground">
          Connect your agents to external services and tools through MCP servers and Pipedream integrations
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="browse" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            Browse
          </TabsTrigger>
          <TabsTrigger value="configured" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configured
          </TabsTrigger>
          <TabsTrigger value="custom" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            Custom
          </TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="mt-6">
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search integrations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={() => setShowRegistryDialog(true)} className="flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  Browse Apps
                </Button>
                <Button variant="outline" onClick={() => setShowCustomDialog(true)} className="flex items-center gap-2">
                  <Server className="h-4 w-4" />
                  Add Custom MCP
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">Loading integrations...</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                  <ExternalLink className="h-8 w-8 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Failed to load integrations</h3>
                <p className="text-muted-foreground mb-6">
                  There was an error loading the integration catalog. Please try again.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredIntegrations.map((integration) => {
                  const statusBadge = getStatusBadge(integration.status);
                  return (
                    <Card key={integration.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0">
                              {renderIntegrationLogo(integration)}
                            </div>
                            <div>
                              <CardTitle className="text-lg">{integration.name}</CardTitle>
                              <Badge variant="secondary" className="text-xs mt-1" style={{ backgroundColor: '#3c3e3e', color: 'white' }}>
                                {integration.category}
                              </Badge>
                            </div>
                          </div>
                          <Badge
                            variant={statusBadge.variant}
                            className={`text-xs ${statusBadge.className}`}
                          >
                            {statusBadge.icon}
                            {integration.status === 'coming_soon' ? 'Coming Soon' :
                             integration.status === 'beta' ? 'Beta' : 'Available'}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <CardDescription className="mb-4">
                          {integration.description}
                        </CardDescription>

                        {integration.features && integration.features.length > 0 && (
                          <div className="mb-4">
                            <div className="flex flex-wrap gap-1">
                              {integration.features.slice(0, 3).map((feature, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                              {integration.features.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{integration.features.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="flex items-center justify-between mb-4 text-xs text-muted-foreground">
                          <span>Setup: {integration.setup_time}</span>
                          <span>★ {integration.popularity}% popular</span>
                        </div>

                        <Button
                          size="sm"
                          className={`w-full ${
                            integration.status === 'available'
                              ? 'bg-green-600 hover:bg-green-700 text-white'
                              : 'bg-gray-400 text-gray-700 cursor-not-allowed'
                          }`}
                          onClick={() => integration.status === 'available' && handleConnect(integration.name)}
                          disabled={isConnecting === integration.name || integration.status !== 'available'}
                        >
                          {isConnecting === integration.name ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Connecting...
                            </>
                          ) : integration.status === 'available' ? (
                            <>
                              <Plus className="h-4 w-4 mr-2" />
                              Connect
                            </>
                          ) : integration.status === 'coming_soon' ? (
                            <>
                              <Clock className="h-4 w-4 mr-2" />
                              Coming Soon
                            </>
                          ) : (
                            <>
                              <Sparkles className="h-4 w-4 mr-2" />
                              Beta Access
                            </>
                          )}
                        </Button>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="configured" className="mt-6">
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <Zap className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No integrations configured</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Configure integrations through your agents to see them here. Each agent can have its own set of integrations.
            </p>
            <Button onClick={() => setActiveTab('browse')}>
              Browse Integrations
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="custom" className="mt-6">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Custom MCP Servers
                </CardTitle>
                <CardDescription>
                  Add custom Model Context Protocol (MCP) servers to extend your agents with specialized tools and capabilities.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
                    <Server className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h4 className="text-sm font-medium mb-2">No custom MCPs configured</h4>
                  <p className="text-sm text-muted-foreground mb-4 max-w-sm mx-auto">
                    Add custom MCP servers to connect to specialized tools and services
                  </p>
                  <Button onClick={() => setShowCustomDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Custom MCP
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ExternalLink className="h-5 w-5" />
                  Learn More
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div>
                    <p className="font-medium">MCP Documentation</p>
                    <p className="text-sm text-muted-foreground">Learn how to create and configure MCP servers</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open('https://modelcontextprotocol.io/docs', '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div>
                    <p className="font-medium">Pipedream Integration Guide</p>
                    <p className="text-sm text-muted-foreground">Connect to 2000+ apps through Pipedream</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open('https://pipedream.com/docs', '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <Dialog open={showRegistryDialog} onOpenChange={setShowRegistryDialog}>
        <DialogContent className="p-0 max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="sr-only">
            <DialogTitle>Select Integration</DialogTitle>
          </DialogHeader>
          <PipedreamRegistry onToolsSelected={handleToolsSelected} />
        </DialogContent>
      </Dialog>

      <CustomMCPDialog
        open={showCustomDialog}
        onOpenChange={setShowCustomDialog}
        onSave={handleSaveCustomMCP}
      />
    </div>
  );
}
