'use client';

import { useEffect, useState } from 'react';
// import { FAQSection } from "@/components/sections/faq-section";
import { HeroSection } from '@/components/home/<USER>/hero-section';
import { ModalProviders } from '@/providers/modal-providers';
import Link from 'next/link';

export default function Home() {
  return (
    <>
      <ModalProviders />


      <main className="relative flex flex-col items-center justify-center min-h-screen w-full">
        <div className="w-full">
          <HeroSection />
          {/* <CompanyShowcase /> */}
          {/* <BentoSection /> */}
          {/* <QuoteSection /> */}
          {/* <FeatureSection /> */}
          {/* <GrowthSection /> */}
          {/* <TestimonialSection /> */}
          {/* <FAQSection /> */}
        </div>

        {/* Legal Footer with Glass Effect */}
        <footer className="w-full py-8 px-6 glass-panel border-t border-white/20 dark:border-white/10">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-muted-foreground">
              By messaging AI Co-Founder, you agree to our{' '}
              <Link href="/terms" className="text-primary hover:underline font-medium">
                Terms
              </Link>{' '}
              and have read our{' '}
              <Link href="/privacy" className="text-primary hover:underline font-medium">
                Privacy Policy
              </Link>
              .{' '}
              <Link href="/cookies" className="text-primary hover:underline font-medium">
                See Cookie Preferences
              </Link>
              .
            </p>
          </div>
        </footer>
      </main>
    </>
  );
}
