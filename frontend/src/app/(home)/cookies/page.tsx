'use client';

import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Shield, BarChart3, Setting<PERSON> } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function CookiesPage() {
  const [preferences, setPreferences] = useState({
    essential: true, // Always enabled
    analytics: true,
    functional: true,
    marketing: false
  });

  const handlePreferenceChange = (category: keyof typeof preferences, enabled: boolean) => {
    if (category === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [category]: enabled
    }));
  };

  const savePreferences = () => {
    // In a real implementation, you would save these preferences
    // to localStorage, cookies, or send to your backend
    localStorage.setItem('cookiePreferences', JSON.stringify(preferences));
    alert('<PERSON><PERSON> preferences saved successfully!');
  };

  const acceptAll = () => {
    setPreferences({
      essential: true,
      analytics: true,
      functional: true,
      marketing: true
    });
  };

  const rejectAll = () => {
    setPreferences({
      essential: true, // Essential cookies cannot be disabled
      analytics: false,
      functional: false,
      marketing: false
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-6 py-12 max-w-4xl">
        <div className="mb-8">
          <Link 
            href="/" 
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors mb-6"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Home
          </Link>
          <h1 className="text-4xl font-bold mb-4 flex items-center gap-3">
            <Cookie className="h-8 w-8" />
            Cookie Preferences
          </h1>
          <p className="text-muted-foreground">
            Manage your cookie preferences and learn about how we use cookies to improve your experience.
          </p>
        </div>

        <div className="space-y-8">
          <section>
            <h2 className="text-2xl font-semibold mb-4">What are cookies?</h2>
            <p className="text-muted-foreground leading-relaxed">
              Cookies are small text files that are stored on your device when you visit our website. 
              They help us provide you with a better experience by remembering your preferences, 
              analyzing how you use our service, and providing personalized content.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-6">Cookie Categories</h2>
            
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Shield className="h-5 w-5 text-green-600" />
                      <div>
                        <CardTitle>Essential Cookies</CardTitle>
                        <CardDescription>Required for the website to function properly</CardDescription>
                      </div>
                    </div>
                    <Switch 
                      checked={preferences.essential} 
                      disabled={true}
                      aria-label="Essential cookies (always enabled)"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    These cookies are necessary for the website to function and cannot be switched off. 
                    They are usually only set in response to actions made by you which amount to a request for services.
                  </p>
                  <div className="text-xs text-muted-foreground">
                    <strong>Examples:</strong> Authentication, security, session management
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <BarChart3 className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle>Analytics Cookies</CardTitle>
                        <CardDescription>Help us understand how visitors interact with our website</CardDescription>
                      </div>
                    </div>
                    <Switch 
                      checked={preferences.analytics} 
                      onCheckedChange={(checked) => handlePreferenceChange('analytics', checked)}
                      aria-label="Analytics cookies"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    These cookies allow us to count visits and traffic sources so we can measure and improve 
                    the performance of our site. They help us know which pages are most popular and see how visitors move around the site.
                  </p>
                  <div className="text-xs text-muted-foreground">
                    <strong>Examples:</strong> Google Analytics, usage statistics, performance monitoring
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Settings className="h-5 w-5 text-purple-600" />
                      <div>
                        <CardTitle>Functional Cookies</CardTitle>
                        <CardDescription>Enable enhanced functionality and personalization</CardDescription>
                      </div>
                    </div>
                    <Switch 
                      checked={preferences.functional} 
                      onCheckedChange={(checked) => handlePreferenceChange('functional', checked)}
                      aria-label="Functional cookies"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    These cookies enable the website to provide enhanced functionality and personalization. 
                    They may be set by us or by third party providers whose services we have added to our pages.
                  </p>
                  <div className="text-xs text-muted-foreground">
                    <strong>Examples:</strong> Language preferences, theme settings, chat widgets
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Cookie className="h-5 w-5 text-orange-600" />
                      <div>
                        <CardTitle>Marketing Cookies</CardTitle>
                        <CardDescription>Used to deliver relevant advertisements</CardDescription>
                      </div>
                    </div>
                    <Switch 
                      checked={preferences.marketing} 
                      onCheckedChange={(checked) => handlePreferenceChange('marketing', checked)}
                      aria-label="Marketing cookies"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    These cookies may be set through our site by our advertising partners. 
                    They may be used to build a profile of your interests and show you relevant adverts on other sites.
                  </p>
                  <div className="text-xs text-muted-foreground">
                    <strong>Examples:</strong> Advertising networks, social media pixels, remarketing
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Cookie Management</h2>
            <div className="bg-muted/50 rounded-lg p-6 space-y-4">
              <p className="text-muted-foreground">
                You can manage your cookie preferences using the controls above, or through your browser settings. 
                Note that disabling certain cookies may affect the functionality of our website.
              </p>
              
              <div className="flex flex-wrap gap-3">
                <Button onClick={acceptAll} className="flex-1 sm:flex-none">
                  Accept All
                </Button>
                <Button onClick={rejectAll} variant="outline" className="flex-1 sm:flex-none">
                  Reject All (except essential)
                </Button>
                <Button onClick={savePreferences} variant="secondary" className="flex-1 sm:flex-none">
                  Save Preferences
                </Button>
              </div>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Browser Settings</h2>
            <p className="text-muted-foreground leading-relaxed mb-4">
              You can also control cookies through your browser settings. Here are links to cookie management 
              instructions for popular browsers:
            </p>
            <ul className="list-disc list-inside text-muted-foreground space-y-2 ml-4">
              <li><a href="https://support.google.com/chrome/answer/95647" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Google Chrome</a></li>
              <li><a href="https://support.mozilla.org/en-US/kb/enhanced-tracking-protection-firefox-desktop" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Mozilla Firefox</a></li>
              <li><a href="https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471/mac" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Safari</a></li>
              <li><a href="https://support.microsoft.com/en-us/microsoft-edge/delete-cookies-in-microsoft-edge-63947406-40ac-c3b8-57b9-2a946a29ae09" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Microsoft Edge</a></li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
            <p className="text-muted-foreground leading-relaxed">
              If you have any questions about our use of cookies or this Cookie Policy, please contact <NAME_EMAIL>.
            </p>
          </section>
        </div>

        <div className="mt-12 pt-8 border-t border-border">
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
            <Link 
              href="/" 
              className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Home
            </Link>
            <div className="flex gap-4 text-sm">
              <Link href="/terms" className="text-primary hover:underline">
                Terms and Conditions
              </Link>
              <Link href="/privacy" className="text-primary hover:underline">
                Privacy Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
