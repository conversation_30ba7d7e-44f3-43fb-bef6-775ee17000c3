'use client';

import { useEffect, useState } from 'react';
import { Loader2, Server, RefreshCw, AlertCircle, Clock, Wrench } from 'lucide-react';
import { useApiHealth } from '@/hooks/react-query';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { isLocalMode } from '@/lib/config';

export function MaintenancePage() {
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  
  const { data: healthData, isLoading: isCheckingHealth, refetch } = useApiHealth();

  const checkHealth = async () => {
    try {
      const result = await refetch();
      if (result.data) {
        window.location.reload();
      }
    } catch (error) {
      console.error('API health check failed:', error);
    } finally {
      setLastChecked(new Date());
    }
  };

  useEffect(() => {
    setLastChecked(new Date());
  }, []);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background image with overlay */}
      <div className="absolute inset-0 overflow-hidden">
        <img
          src="/holo.png"
          alt=""
          className="absolute inset-0 w-full h-full object-cover opacity-5"
          onError={(e) => {
            e.currentTarget.style.display = 'none';
          }}
        />
        <div className="absolute inset-0 bg-black/50"></div>
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="w-full max-w-2xl relative z-10">
        <Card className="border border-gray-700 shadow-2xl backdrop-blur-xl bg-gray-900/80">
          <CardContent className="p-8 md:p-12">
            <div className="text-center space-y-8">
              {/* Logo/Icon Section */}
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <div className="absolute inset-0 bg-white/20 rounded-full blur-lg opacity-30 animate-pulse"></div>
                  <div className="relative p-6 rounded-full bg-white/10 shadow-lg border border-white/20">
                    <img
                      src="/kortix-symbol.svg"
                      alt="AI Co-Founder"
                      className="h-12 w-12 text-white"
                      onError={(e) => {
                        // Fallback to wrench icon if image fails to load
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                    <Wrench className="h-12 w-12 text-white hidden" />
                  </div>
                </div>
              </div>

              {/* Status Badge */}
              <div className="flex justify-center">
                <Badge variant="outline" className="px-4 py-2 bg-orange-900/30 border-orange-700 text-orange-200 font-medium text-sm">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  System Under Maintenance
                </Badge>
              </div>

              {/* Main Content */}
              <div className="space-y-6">
                <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-white">
                  AI Co-Founder is Upgrading
                </h1>
                <p className="text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
                  {isLocalMode() ? (
                    "The backend server appears to be offline. Please ensure your backend server is running and try again."
                  ) : (
                    "We're enhancing our AI capabilities and infrastructure to serve you better. Your digital co-founder will return shortly with improved performance and new features."
                  )}
                </p>
              </div>

              {/* Status Card */}
              <div className="grid grid-cols-1 gap-4 mt-8">
                <Card className="bg-gray-900/50 border-gray-700 shadow-sm backdrop-blur-sm">
                  <CardContent className="p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      <div className="h-3 w-3 bg-orange-500 rounded-full mr-3 animate-pulse shadow-lg"></div>
                      <span className="font-semibold text-orange-300 text-lg">Services Offline</span>
                    </div>
                    <p className="text-gray-400">AI Co-Founder services are temporarily unavailable.</p>
                  </CardContent>
                </Card>
              </div>

              {/* Action Button */}
              <div className="space-y-6 pt-6">
                <Button
                  onClick={checkHealth}
                  disabled={isCheckingHealth}
                  size="lg"
                  className="w-full md:w-auto px-8 py-3 bg-white text-black hover:bg-gray-200 font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  {isCheckingHealth ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin mr-2" />
                      Checking Status...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-5 w-5 mr-2" />
                      Check System Status
                    </>
                  )}
                </Button>

                {lastChecked && (
                  <div className="flex items-center justify-center text-sm text-gray-400">
                    <Clock className="h-4 w-4 mr-2" />
                    Last checked: {lastChecked.toLocaleTimeString()}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="pt-6 border-t border-gray-700">
                <p className="text-gray-400">
                  {isLocalMode() ? (
                    "Need help? Check the documentation for setup instructions."
                  ) : (
                    "Thank you for your patience. Your AI Co-Founder will be back online soon with enhanced capabilities."
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
