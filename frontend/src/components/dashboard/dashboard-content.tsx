'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Menu } from 'lucide-react';
import {
  ChatInput,
  ChatInputHandles,
} from '@/components/thread/chat-input/chat-input';
import {
  BillingError,
} from '@/lib/api';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useBillingError } from '@/hooks/useBillingError';
import { BillingErrorAlert } from '@/components/billing/usage-limit-alert';
import { useAccounts } from '@/hooks/use-accounts';

import { useInitiateAgentWithInvalidation } from '@/hooks/react-query/dashboard/use-initiate-agent';
import { ModalProviders } from '@/providers/modal-providers';
import { useAgents } from '@/hooks/react-query/agents/use-agents';
import { cn } from '@/lib/utils';
import { useModal } from '@/hooks/use-modal-store';
import { ensureUserReadyForMessaging } from '@/lib/user-onboarding';
import { toast } from 'sonner';
import { Examples } from './examples';
import { useThreadQuery } from '@/hooks/react-query/threads/use-threads';
import { normalizeFilenameToNFC } from '@/lib/utils/unicode';
import { useAuth } from '@/components/AuthProvider';

const PENDING_PROMPT_KEY = 'pendingAgentPrompt';

export function DashboardContent() {
  const [inputValue, setInputValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [autoSubmit, setAutoSubmit] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState<string | undefined>();
  const [initiatedThreadId, setInitiatedThreadId] = useState<string | null>(null);
  const { billingError, clearBillingError } = useBillingError();
  const router = useRouter();
  const searchParams = useSearchParams();
  const isMobile = useIsMobile();
  const { setOpenMobile } = useSidebar();
  const { data: accounts } = useAccounts();
  const personalAccount = accounts?.find((account) => account.personal_account);
  const chatInputRef = useRef<ChatInputHandles>(null);
  const initiateAgentMutation = useInitiateAgentWithInvalidation();
  const { onOpen } = useModal();
  const { user, isLoading } = useAuth();

  // Fetch agents to get the selected agent's name
  const { data: agentsResponse } = useAgents({
    limit: 100,
    sort_by: 'name',
    sort_order: 'asc'
  });

  const agents = agentsResponse?.agents || [];



  const threadQuery = useThreadQuery(initiatedThreadId || '');

  useEffect(() => {
    const agentIdFromUrl = searchParams.get('agent_id');
    if (agentIdFromUrl && agentIdFromUrl !== selectedAgentId) {
      setSelectedAgentId(agentIdFromUrl);
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('agent_id');
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });
    }
  }, [searchParams, selectedAgentId, router]);

  useEffect(() => {
    if (threadQuery.data && initiatedThreadId) {
      const thread = threadQuery.data;
      console.log('Thread data received:', thread);
      if (thread.project_id) {
        router.push(`/projects/${thread.project_id}/thread/${initiatedThreadId}`);
      } else {
        router.push(`/agents/${initiatedThreadId}`);
      }
      setInitiatedThreadId(null);
    }
  }, [threadQuery.data, initiatedThreadId, router]);



  const handleSubmit = useCallback(async (
    message: string,
    options?: {
      model_name?: string;
      enable_thinking?: boolean;
      reasoning_effort?: string;
      stream?: boolean;
      enable_context_manager?: boolean;
    },
  ) => {
    if (
      (!message.trim() && !chatInputRef.current?.getPendingFiles().length) ||
      isSubmitting
    )
      return;

    setIsSubmitting(true);

    try {
      // Ensure user is ready for messaging (has projects, etc.)
      const readinessCheck = await ensureUserReadyForMessaging();

      if (!readinessCheck.ready) {
        toast.error(readinessCheck.error || 'Failed to prepare your account. Please try again.');
        return;
      }

      if (readinessCheck.projectId) {
        toast.success('Welcome! We\'ve set up your first project for you.');
      }

      const files = chatInputRef.current?.getPendingFiles() || [];
      localStorage.removeItem(PENDING_PROMPT_KEY);

      const formData = new FormData();
      formData.append('prompt', message);

      // Add selected agent if one is chosen
      if (selectedAgentId) {
        formData.append('agent_id', selectedAgentId);
      }

      files.forEach((file) => {
        const normalizedName = normalizeFilenameToNFC(file.name);
        formData.append('files', file, normalizedName);
      });

      if (options?.model_name) formData.append('model_name', options.model_name);
      formData.append('enable_thinking', String(options?.enable_thinking ?? false));
      formData.append('reasoning_effort', options?.reasoning_effort ?? 'low');
      formData.append('stream', String(options?.stream ?? true));
      formData.append('enable_context_manager', String(options?.enable_context_manager ?? false));

      const result = await initiateAgentMutation.mutateAsync(formData);

      if (result.thread_id) {
        setInitiatedThreadId(result.thread_id);

        // Show success message with details about what was created
        if (result.entities_created) {
          toast.success(`Successfully created: Project "${result.entities_created.project.name}", Agent, and Thread!`);
        } else {
          toast.success('Agent created successfully!');
        }

        // Navigate to the agent thread after a brief delay to show the success message
        setTimeout(() => {
          router.push(`/agents/${result.thread_id}`);
        }, 1500);
      } else {
        throw new Error('Agent initiation did not return a thread_id.');
      }
      chatInputRef.current?.clearPendingFiles();
    } catch (error: any) {
      console.error('Error during submission process:', error);
      if (error instanceof BillingError) {
        console.log('Handling BillingError:', error.detail);
        onOpen("paymentRequiredDialog");
      } else {
        // Provide user-friendly error messages
        let errorMessage = 'Failed to start conversation. Please try again.';

        if (error.message?.includes('Authentication')) {
          errorMessage = 'Please sign in again to continue.';
        } else if (error.message?.includes('Network') || error.message?.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message?.includes('Server error')) {
          errorMessage = 'Server is temporarily unavailable. Please try again in a moment.';
        }

        toast.error(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [isSubmitting, router, selectedAgentId]);

  useEffect(() => {
    const timer = setTimeout(() => {
      const pendingPrompt = localStorage.getItem(PENDING_PROMPT_KEY);

      if (pendingPrompt && pendingPrompt.trim()) {
        const trimmedPrompt = pendingPrompt.trim();
        setInputValue(trimmedPrompt);

        // Use a small delay to ensure state is updated
        setTimeout(() => {
          setAutoSubmit(true);
        }, 100);
      }
    }, 500); // Initial delay to ensure components are ready

    return () => clearTimeout(timer);
  }, [user, isLoading]);

  useEffect(() => {
    if (autoSubmit && inputValue && inputValue.trim() && !isSubmitting) {
      // Submit immediately without delay
      const submitMessage = async () => {
        try {
          await handleSubmit(inputValue.trim());
          setAutoSubmit(false);
          // Clear the localStorage after successful auto-submit
          localStorage.removeItem(PENDING_PROMPT_KEY);
        } catch (error) {
          console.error('Auto-submit failed:', error);
          setAutoSubmit(false);
        }
      };

      submitMessage();
    }
  }, [autoSubmit, inputValue, isSubmitting, handleSubmit]);

  return (
    <>
      <ModalProviders />
      <div className="flex flex-col h-screen w-full">
        {isMobile && (
          <div className="absolute top-4 left-4 z-10">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setOpenMobile(true)}
                >
                  <Menu className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Open menu</TooltipContent>
            </Tooltip>
          </div>
        )}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[650px] max-w-[90%]">
          <div className="flex flex-col items-center text-center w-full">
            {/* <div className="flex items-center gap-1">
              <h1 className="tracking-tight text-4xl text-muted-foreground leading-tight">
                Hey, I am
              </h1>
              <h1 className="ml-1 tracking-tight text-4xl font-semibold leading-tight text-primary">
                {displayName}
                {agentAvatar && (
                  <span className="text-muted-foreground ml-2">
                    {agentAvatar}
                  </span>
                )}
              </h1>
            </div> */}
            <p className="tracking-tight text-3xl font-normal text-muted-foreground/80 mt-2">
              {autoSubmit ? "Processing your message..." : inputValue ? "Ready to submit your message" : "What would you like to do today?"}
            </p>



            {inputValue && !autoSubmit && !isSubmitting && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                  Message from homepage: "{inputValue}"
                </p>
                <button
                  onClick={() => handleSubmit(inputValue)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Submit Message
                </button>
              </div>
            )}
          </div>
          <div className={cn(
            "w-full mb-2",
            "max-w-full",
            "sm:max-w-3xl"
          )}>
            <ChatInput
              ref={chatInputRef}
              onSubmit={handleSubmit}
              loading={isSubmitting}
              placeholder="Describe what you need help with..."
              value={inputValue}
              onChange={setInputValue}
              hideAttachments={false}
              selectedAgentId={selectedAgentId}
              onAgentSelect={setSelectedAgentId}
              enableAdvancedConfig={true} // ⚠️ PERMANENT FIX - DO NOT CHANGE TO FALSE ⚠️
              onConfigureAgent={(agentId) => router.push(`/agents/config/${agentId}`)}
            />
          </div>
          <Examples onSelectPrompt={setInputValue} />
        </div>
        <BillingErrorAlert
          message={billingError?.message}
          currentUsage={billingError?.currentUsage}
          limit={billingError?.limit}
          accountId={personalAccount?.account_id}
          onDismiss={clearBillingError}
          isOpen={!!billingError}
        />
      </div>
    </>
  );
}
