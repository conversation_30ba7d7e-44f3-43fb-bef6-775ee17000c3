'use client';

import * as React from 'react';
import Link from 'next/link';
import { Menu, Store, Plus, Zap, Shield, Bot, Plug } from 'lucide-react';

import { NavAgents } from '@/components/sidebar/nav-agents';
import { NavUserWithTeams } from '@/components/sidebar/nav-user-with-teams';
import { AICoFounderLogo } from '@/components/sidebar/kortix-logo';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenuButton,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { Badge } from '../ui/badge';
import { cn } from '@/lib/utils';
import { usePathname, useRouter } from 'next/navigation';
import { useFeatureFlags } from '@/lib/feature-flags';
import { useCreateProject } from '@/hooks/react-query/sidebar/use-project-mutations';
import { useCreateThread } from '@/hooks/react-query/threads/use-thread-mutations';
import { toast } from 'sonner';

export function SidebarLeft({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { state, setOpen, setOpenMobile } = useSidebar();
  const isMobile = useIsMobile();
  const router = useRouter();
  const createProjectMutation = useCreateProject();
  const createThreadMutation = useCreateThread();
  const [user, setUser] = useState<{
    name: string;
    email: string;
    avatar: string;
  }>({
    name: 'Loading...',
    email: '<EMAIL>',
    avatar: '',
  });

  const [isSuperuser, setIsSuperuser] = useState(false);

  const pathname = usePathname();
  const { flags, loading: flagsLoading } = useFeatureFlags(['custom_agents', 'agent_marketplace']);
  const customAgentsEnabled = flags.custom_agents;
  const marketplaceEnabled = flags.agent_marketplace;

  const handleNewTask = async () => {
    try {
      // Create a new project
      const project = await createProjectMutation.mutateAsync({
        name: 'New Task',
        description: 'A new task created from the sidebar'
      });

      // Create a thread for the project
      const thread = await createThreadMutation.mutateAsync({
        projectId: project.id
      });

      // Navigate to the new thread
      router.push(`/projects/${project.id}/thread/${thread.id}`);

      toast.success('New task created successfully!');
    } catch (error: any) {
      console.error('Error creating new task:', error);
      toast.error('Failed to create new task. Please try again.');
    }
  };

  useEffect(() => {
    const fetchUserData = async () => {
      const supabase = createClient();
      const { data } = await supabase.auth.getUser();

      if (data.user) {
        setUser({
          name:
            data.user.user_metadata?.name ||
            data.user.email?.split('@')[0] ||
            'User',
          email: data.user.email || '',
          avatar: data.user.user_metadata?.avatar_url || '',
        });

        // Check if user is a superuser using the backend API
        try {
          const { data: session } = await supabase.auth.getSession();
          if (session.session?.access_token) {
            const response = await fetch('/api/admin/verify', {
              headers: {
                'Authorization': `Bearer ${session.session.access_token}`,
              },
            });

            if (response.ok) {
              const result = await response.json();
              setIsSuperuser(result.is_superuser || false);
            }
          }
        } catch (error) {
          // User is not a superuser or API call failed
          setIsSuperuser(false);
        }
      }
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'b') {
        event.preventDefault();
        setOpen(!state.startsWith('expanded'));
        window.dispatchEvent(
          new CustomEvent('sidebar-left-toggled', {
            detail: { expanded: !state.startsWith('expanded') },
          }),
        );
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state, setOpen]);

  return (
    <Sidebar
      collapsible="icon"
      className="border-r-0 glass-sidebar [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
      {...props}
    >
      <SidebarHeader className="px-2 py-2">
        <div className="flex h-[40px] items-center px-1 relative">
          <Link href="/dashboard">
            <AICoFounderLogo />
          </Link>
          {state !== 'collapsed' && (
            <div className="ml-2 transition-all duration-200 ease-in-out whitespace-nowrap">
            </div>
          )}
          <div className="ml-auto flex items-center gap-2">
            {state !== 'collapsed' && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <SidebarTrigger className="h-8 w-8" />
                </TooltipTrigger>
                <TooltipContent>Toggle sidebar (CMD+B)</TooltipContent>
              </Tooltip>
            )}
            {isMobile && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => setOpenMobile(true)}
                    className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent"
                  >
                    <Menu className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Open menu</TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent className="[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
        <SidebarGroup>
          <SidebarMenuButton
            onClick={handleNewTask}
            disabled={createProjectMutation.isPending || createThreadMutation.isPending}
            className={cn(
              'cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors',
              {
                'text-white dark:text-white font-medium border border-gray-600 dark:border-gray-600': pathname === '/dashboard',
                'opacity-50 cursor-not-allowed': createProjectMutation.isPending || createThreadMutation.isPending
              }
            )}
            style={pathname === '/dashboard' ? { backgroundColor: '#3c3e3e' } : {}}
          >
            <Plus className="h-4 w-4 mr-1" />
            <span className="flex items-center justify-between w-full">
              {createProjectMutation.isPending || createThreadMutation.isPending ? 'Creating...' : 'New Task'}
            </span>
          </SidebarMenuButton>
          {/* {!flagsLoading && marketplaceEnabled && (
            <Link href="/marketplace">
              <SidebarMenuButton className={cn({
                'bg-accent text-accent-foreground font-medium': pathname === '/marketplace',
              })}>
                <Store className="h-4 w-4 mr-2" />
                <span className="flex items-center justify-between w-full">
                  Marketplace
                </span>
              </SidebarMenuButton>
            </Link>
          )} */}

          {!flagsLoading && customAgentsEnabled && (
            <Link href="/agents" prefetch={true}>
              <SidebarMenuButton className={cn({
                'bg-accent text-accent-foreground font-medium': pathname.startsWith('/agents'),
              })}>
                <Bot className="h-4 w-4 mr-1" />
                <span className="flex items-center justify-between w-full">
                  Agents
                </span>
              </SidebarMenuButton>
            </Link>
          )}

          <Link href="/integrations" prefetch={true}>
            <SidebarMenuButton className={cn({
              'bg-accent text-accent-foreground font-medium': pathname.startsWith('/integrations'),
            })}>
              <Plug className="h-4 w-4 mr-1" />
              <span className="flex items-center justify-between w-full">
                Integrations
              </span>
            </SidebarMenuButton>
          </Link>

          {isSuperuser && (
            <Link href="/admin" prefetch={true}>
              <SidebarMenuButton className={cn({
                'bg-accent text-accent-foreground font-medium': pathname === '/admin',
              })}>
                <Shield className="h-4 w-4 mr-1" />
                <span className="flex items-center justify-between w-full">
                  Admin Dashboard
                  <Badge variant="secondary" className="text-xs">SUPER</Badge>
                </span>
              </SidebarMenuButton>
            </Link>
          )}

        </SidebarGroup>
        <NavAgents />
      </SidebarContent>
      <SidebarFooter>
        {state === 'collapsed' && (
          <div className="mt-2 flex justify-center">
            <Tooltip>
              <TooltipTrigger asChild>
                <SidebarTrigger className="h-8 w-8" />
              </TooltipTrigger>
              <TooltipContent>Expand sidebar (CMD+B)</TooltipContent>
            </Tooltip>
          </div>
        )}
        <NavUserWithTeams user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
