import { createClient } from '@/lib/supabase/client';
import { createProject, getProjects } from '@/lib/api';

export interface OnboardingStatus {
  hasProjects: boolean;
  hasDefaultProject: boolean;
  isNewUser: boolean;
  needsOnboarding: boolean;
}

/**
 * Check if a user needs onboarding and ensure they have a default project
 */
export async function checkUserOnboardingStatus(): Promise<OnboardingStatus> {
  try {
    const supabase = createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    // Get user's projects via API
    try {
      const projects = await getProjects();
      const hasProjects = projects && projects.length > 0;
      const hasDefaultProject = hasProjects && projects.some(p => p.name === 'My First Project' || p.name === 'Default Project');

      // Consider user new if they have no projects or account was created recently
      const userCreatedAt = new Date(user.created_at);
      const isRecentUser = Date.now() - userCreatedAt.getTime() < 24 * 60 * 60 * 1000; // 24 hours
      const isNewUser = !hasProjects || isRecentUser;

      return {
        hasProjects,
        hasDefaultProject,
        isNewUser,
        needsOnboarding: !hasProjects
      };
    } catch (apiError) {
      console.error('Error fetching projects via API:', apiError);
      // Fallback: assume user needs onboarding if we can't fetch projects
      return {
        hasProjects: false,
        hasDefaultProject: false,
        isNewUser: true,
        needsOnboarding: true
      };
    }
  } catch (error) {
    console.error('Error checking onboarding status:', error);
    return {
      hasProjects: false,
      hasDefaultProject: false,
      isNewUser: true,
      needsOnboarding: true
    };
  }
}

/**
 * Create a default project for new users
 */
export async function createDefaultProject(): Promise<string | null> {
  try {
    const supabase = createClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    // Create default project
    const project = await createProject({
      name: 'My First Project',
      description: 'Welcome to AI Co-Founder! This is your first project where you can start building with your AI assistant.'
    }, user.id);

    console.log('Created default project for new user:', project.id);
    return project.id;
  } catch (error) {
    console.error('Error creating default project:', error);
    return null;
  }
}

/**
 * Complete user onboarding by ensuring they have a default project
 */
export async function completeUserOnboarding(): Promise<{ success: boolean; projectId?: string; error?: string }> {
  try {
    const status = await checkUserOnboardingStatus();
    
    if (!status.needsOnboarding) {
      return { success: true };
    }

    const projectId = await createDefaultProject();
    
    if (!projectId) {
      return { 
        success: false, 
        error: 'Failed to create default project' 
      };
    }

    return { 
      success: true, 
      projectId 
    };
  } catch (error) {
    console.error('Error completing user onboarding:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Ensure user has proper setup before allowing message submission
 */
export async function ensureUserReadyForMessaging(): Promise<{ ready: boolean; error?: string; projectId?: string }> {
  try {
    const status = await checkUserOnboardingStatus();
    
    if (status.needsOnboarding) {
      console.log('User needs onboarding, creating default project...');
      const result = await completeUserOnboarding();
      
      if (!result.success) {
        return {
          ready: false,
          error: result.error || 'Failed to complete user setup'
        };
      }
      
      return {
        ready: true,
        projectId: result.projectId
      };
    }

    return { ready: true };
  } catch (error) {
    console.error('Error ensuring user ready for messaging:', error);
    return {
      ready: false,
      error: error instanceof Error ? error.message : 'Setup failed'
    };
  }
}
