# Netlify Redirects Configuration for AI Co-Founder

# API Routes - Redirect all /api/* requests to Netlify functions
/api/* /.netlify/functions/api/:splat 200

# Health check endpoint
/health /.netlify/functions/api/health 200

# Feature flags endpoints
/feature-flags/* /.netlify/functions/api/feature-flags/:splat 200

# Billing endpoints
/billing/* /.netlify/functions/api/billing/:splat 200

# Admin endpoints
/admin/* /.netlify/functions/api/admin/:splat 200

# Projects and threads endpoints
/projects /.netlify/functions/api/projects 200
/threads /.netlify/functions/api/threads 200

# SPA fallback - serve index.html for all other routes
/* /index.html 200
