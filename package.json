{"name": "ultimate-co-founder", "version": "1.0.0", "description": "Ultimate Co-Founder - AI-powered business co-founder application", "private": true, "scripts": {"dev": "cd frontend && npm run dev", "build": "cd frontend && npm install && npm run build", "start": "cd frontend && npm start", "lint": "cd frontend && npm run lint", "format": "cd frontend && npm run format", "format:check": "cd frontend && npm run format:check", "install-frontend": "cd frontend && npm install", "install-backend": "cd backend && uv sync", "setup": "npm run install-frontend && npm run install-backend", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && uv run start_server.py", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "test": "cd frontend && npm test", "clean": "rm -rf frontend/node_modules frontend/.next backend/.venv", "clean:install": "npm run clean && npm run setup"}, "repository": {"type": "git", "url": "https://github.com/your-username/ultimate-co-founder.git"}, "keywords": ["ai", "co-founder", "business", "automation", "nextjs", "<PERSON><PERSON><PERSON>", "supabase"], "author": "Ultimate Co-Founder Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["frontend"], "devDependencies": {"concurrently": "^8.2.2"}, "netlify": {"build": {"base": "frontend", "publish": "frontend/.next", "command": "npm run build"}}}