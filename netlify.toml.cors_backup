[build]
  base = "frontend"
  publish = ".next"
  command = "npm ci && npm run build"

[build.environment]
  NODE_VERSION = "18"
  # Completely disable mise/rtx
  MISE_DISABLE = "1"
  MISE_SKIP = "1"
  RTX_DISABLE = "1"
  RTX_SKIP = "1"

  # Supabase configuration
  NEXT_PUBLIC_SUPABASE_URL = "https://pldcxtmyivlpueddnuml.supabase.co"
  NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.yrYWj-hbqdS3nGTFBA1YvhAtKoY8BOVY1_OXA4_gAtA"

  # Application configuration
  NEXT_PUBLIC_ENV_MODE = "production"
  NEXT_PUBLIC_URL = "https://aicofounder.site"
  NEXT_PUBLIC_BACKEND_URL = "https://api.ai-co-founder.com/api"

# Netlify Next.js plugin for SSR support
[[plugins]]
  package = "@netlify/plugin-nextjs"

# API routes handled by Next.js SSR
# No redirects needed - Next.js handles routing

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Environment variables (these should be set in Netlify dashboard)
# NEXT_PUBLIC_SUPABASE_URL = "your-supabase-url"
# NEXT_PUBLIC_SUPABASE_ANON_KEY = "your-supabase-anon-key"
# SUPABASE_SERVICE_ROLE_KEY = "your-service-role-key"

# Functions configuration (if using Netlify Functions)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Dev server configuration
[dev]
  command = "npm run dev"
  port = 3000
  autoLaunch = false
