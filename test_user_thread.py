#!/usr/bin/env python3
"""
Test the specific thread ID that the user is having issues with
"""

import requests
import json

def test_user_thread():
    """Test the specific thread that the user reported"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing User's Specific Thread Issue")
    print("=" * 50)
    
    # Use the exact thread ID from the user's console error
    user_thread_id = "74de9da2-6029-46b4-86db-8308b1b5984e"
    
    print(f"\nTesting thread: {user_thread_id}")
    print("This is the exact thread ID from the user's console error.")
    
    # Test the GET thread endpoint
    print(f"\n1. Testing GET /api/thread/{user_thread_id}")
    
    try:
        response = requests.get(
            f"{base_url}/api/thread/{user_thread_id}",
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:300]}...")
        
        if response.status_code == 500:
            print("   ❌ STILL FAILING: 500 Internal Server Error")
            if "column" in response.text.lower():
                print("   ❌ Still has database column error")
            elif "schema" in response.text.lower():
                print("   ❌ Still has database schema error")
            print(f"   Full error: {response.text}")
        elif response.status_code == 403:
            print("   ✅ PROGRESS: 403 Forbidden (database queries working, auth issue)")
            print("   This means the database schema issues are FIXED!")
            print("   The 403 error is expected - user needs proper authentication")
        elif response.status_code == 401:
            print("   ✅ PROGRESS: 401 Unauthorized (database queries working)")
            print("   This means the database schema issues are FIXED!")
        elif response.status_code == 404:
            print("   ✅ PROGRESS: 404 Not Found (database queries working)")
            print("   This means the database schema issues are FIXED!")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 ANALYSIS:")
    print("• 500 = Database schema error (BAD)")
    print("• 403 = Forbidden - auth working, database working (GOOD)")
    print("• 401 = Unauthorized - database working (GOOD)")
    print("• 404 = Not found - database working (GOOD)")
    print("")
    print("If we get 403/401/404, the database schema is FIXED!")
    print("The user just needs proper authentication in the frontend.")

if __name__ == "__main__":
    test_user_thread()
