#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the messages table schema by adding the missing 'type' column
"""

import os
import sys
import asyncio
from supabase import create_client, Client

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def fix_database_schema():
    # Load environment variables
    from dotenv import load_dotenv
    
    # Try different paths for .env file
    env_paths = [
        'backend/.env',
        '.env',
        os.path.join(os.path.dirname(__file__), 'backend', '.env'),
        os.path.join(os.path.dirname(__file__), '.env')
    ]
    
    env_loaded = False
    for env_path in env_paths:
        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"📁 Loaded environment from: {env_path}")
            env_loaded = True
            break
    
    if not env_loaded:
        print("⚠️  No .env file found, trying environment variables...")
    
    # Initialize Supabase client
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not url or not key:
        print("❌ Error: Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY")
        return False
    
    supabase: Client = create_client(url, key)
    
    try:
        print("🔧 Fixing messages table schema...")
        
        # Read the SQL migration file
        sql_file_paths = [
            'fix_messages_type_column.sql',
            '../fix_messages_type_column.sql',
            os.path.join(os.path.dirname(__file__), 'fix_messages_type_column.sql'),
            os.path.join(os.path.dirname(__file__), '..', 'fix_messages_type_column.sql')
        ]

        sql_content = None
        for sql_path in sql_file_paths:
            if os.path.exists(sql_path):
                with open(sql_path, 'r') as f:
                    sql_content = f.read()
                print(f"📄 Found SQL file at: {sql_path}")
                break

        if not sql_content:
            print("❌ Could not find fix_messages_type_column.sql file")
            return False
        
        # Execute the migration using individual SQL commands
        print("📝 Executing database migration...")

        # Split the SQL into individual commands and execute them
        sql_commands = [
            # Add type column if it doesn't exist (rename from role if needed)
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'messages'
                    AND column_name = 'type'
                    AND table_schema = 'public'
                ) THEN
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns
                        WHERE table_name = 'messages'
                        AND column_name = 'role'
                        AND table_schema = 'public'
                    ) THEN
                        ALTER TABLE messages RENAME COLUMN role TO type;
                        RAISE NOTICE 'Renamed role column to type';
                    ELSE
                        ALTER TABLE messages ADD COLUMN type TEXT NOT NULL DEFAULT 'user';
                        RAISE NOTICE 'Added type column';
                    END IF;
                END IF;
            END $$;
            """,

            # Ensure is_llm_message column exists
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'messages'
                    AND column_name = 'is_llm_message'
                    AND table_schema = 'public'
                ) THEN
                    ALTER TABLE messages ADD COLUMN is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;
                    RAISE NOTICE 'Added is_llm_message column';
                END IF;
            END $$;
            """,

            # Create indexes
            "CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);",
            "CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);"
        ]

        for i, command in enumerate(sql_commands):
            try:
                print(f"📝 Executing command {i+1}/{len(sql_commands)}...")
                result = supabase.rpc('exec', {'sql': command.strip()}).execute()
                print(f"✅ Command {i+1} executed successfully")
            except Exception as cmd_error:
                print(f"⚠️  Command {i+1} failed: {str(cmd_error)}")
                # Try alternative method for this command
                continue
        
        if result.data:
            print("✅ Database migration executed successfully!")
            print("📊 Migration results:")
            for row in result.data:
                print(f"  - {row}")
        else:
            print("✅ Database migration completed (no output)")
        
        # Verify the schema by checking if type column exists
        print("\n🔍 Verifying schema...")
        schema_check = supabase.table('messages').select('*').limit(1).execute()
        
        if schema_check.data is not None:
            print("✅ Messages table is accessible")
            
            # Try to insert a test message to verify schema
            test_thread_id = "00000000-0000-0000-0000-000000000000"  # Dummy UUID for test
            
            # First check if we have any threads
            threads_check = supabase.table('threads').select('thread_id').limit(1).execute()
            if threads_check.data:
                test_thread_id = threads_check.data[0]['thread_id']
                print(f"📋 Using existing thread for test: {test_thread_id}")
            else:
                print("⚠️  No threads found, skipping insert test")
                return True
            
            # Try a test insert (we'll delete it immediately)
            try:
                test_insert = supabase.table('messages').insert({
                    'thread_id': test_thread_id,
                    'type': 'test',
                    'content': {'text': 'test message'},
                    'is_llm_message': False,
                    'metadata': {}
                }).execute()
                
                if test_insert.data:
                    test_message_id = test_insert.data[0]['message_id']
                    print("✅ Test message inserted successfully")
                    
                    # Delete the test message
                    supabase.table('messages').delete().eq('message_id', test_message_id).execute()
                    print("🗑️  Test message cleaned up")
                
            except Exception as e:
                print(f"⚠️  Test insert failed: {str(e)}")
                print("🔧 This might indicate remaining schema issues")
        
        print("\n🎉 Database schema fix completed!")
        print("🔄 Please restart the backend server to refresh the schema cache")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing database schema: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_database_schema())
    sys.exit(0 if success else 1)
