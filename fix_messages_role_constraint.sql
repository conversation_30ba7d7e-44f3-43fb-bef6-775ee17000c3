-- Fix messages table role constraint to allow all roles used by the application
-- This fixes the "messages_role_check" constraint violation error

BEGIN;

-- First, let's check the current constraint
SELECT conname, consrc 
FROM pg_constraint 
WHERE conrelid = 'public.messages'::regclass 
AND conname = 'messages_role_check';

-- Drop the existing constraint
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_role_check;

-- Add the new constraint that includes all roles used by the application
ALTER TABLE public.messages ADD CONSTRAINT messages_role_check 
CHECK (role IN (
    'user', 
    'assistant', 
    'system', 
    'tool', 
    'status', 
    'assistant_response_end',
    'assistant_response_start',
    'tool_result',
    'error'
));

-- Also check if we have a 'type' column with similar constraint and fix it
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_type_check;

-- Add constraint for type column if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.messages ADD CONSTRAINT messages_type_check 
        CHECK (type IN (
            'user', 
            'assistant', 
            'system', 
            'tool', 
            'status', 
            'assistant_response_end',
            'assistant_response_start',
            'tool_result',
            'error'
        ));
    END IF;
END $$;

-- Verify the changes
SELECT 
    conname, 
    consrc 
FROM pg_constraint 
WHERE conrelid = 'public.messages'::regclass 
AND conname LIKE '%role%' OR conname LIKE '%type%';

-- Show current table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'messages' 
AND table_schema = 'public'
ORDER BY ordinal_position;

COMMIT;

-- Test the fix by trying to insert a message with the problematic role
-- This should now work without constraint violation
INSERT INTO public.messages (
    role, 
    content, 
    thread_id, 
    metadata
) VALUES (
    'assistant_response_end',
    '{"test": "constraint fix"}',
    '00000000-0000-0000-0000-000000000000',  -- dummy thread_id for test
    '{"test": true}'::jsonb
) ON CONFLICT DO NOTHING;

-- Clean up the test record
DELETE FROM public.messages 
WHERE role = 'assistant_response_end' 
AND content = '{"test": "constraint fix"}';

SELECT 'Messages role constraint fix completed successfully!' as status;
