-- SIMPLE WORKING FIX FOR AGENTS API - FINAL VERSION
-- This creates agents with proper API compatibility using existing table structure

-- First, ensure the account exists in basejump.accounts
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Safe cleanup that respects foreign key constraints
UPDATE public.agents SET current_version_id = NULL WHERE current_version_id IS NOT NULL;
DELETE FROM public.agent_versions;
DELETE FROM public.agents;

-- Create agents with required agent_id field
INSERT INTO public.agents (
    agent_id,
    account_id,
    name,
    description,
    role
) VALUES
(
    gen_random_uuid(),
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    '<PERSON><PERSON>',
    'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
    'assistant'
),
(
    gen_random_uuid(),
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Code Assistant',
    'A specialized AI assistant for coding, debugging, and technical development tasks.',
    'assistant'
),
(
    gen_random_uuid(),
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Marketing Advisor',
    'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
    'assistant'
);

-- Now create agent versions using the actual agent IDs from the database
DO $$
DECLARE
    suna_agent record;
    code_agent record;
    marketing_agent record;
    suna_version_id uuid;
    code_version_id uuid;
    marketing_version_id uuid;
BEGIN
    -- Get the actual agent records that were just created
    SELECT * INTO suna_agent FROM public.agents WHERE name = 'Suna' AND account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    SELECT * INTO code_agent FROM public.agents WHERE name = 'Code Assistant' AND account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    SELECT * INTO marketing_agent FROM public.agents WHERE name = 'Marketing Advisor' AND account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;

    RAISE NOTICE 'Found agents - Suna: %, Code: %, Marketing: %', suna_agent.agent_id, code_agent.agent_id, marketing_agent.agent_id;

    -- Create agent versions using the actual agent_id values with proper UUID casting
    INSERT INTO public.agent_versions (
        agent_id,
        version_number,
        name,
        system_prompt
    ) VALUES
    (
        suna_agent.agent_id::uuid,
        1,
        'Suna v1.0',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.'
    ),
    (
        code_agent.agent_id::uuid,
        1,
        'Code Assistant v1.0',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.'
    ),
    (
        marketing_agent.agent_id::uuid,
        1,
        'Marketing Advisor v1.0',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.'
    );

    -- Get the version IDs that were just created
    SELECT version_id INTO suna_version_id FROM public.agent_versions WHERE agent_id = suna_agent.agent_id AND version_number = 1;
    SELECT version_id INTO code_version_id FROM public.agent_versions WHERE agent_id = code_agent.agent_id AND version_number = 1;
    SELECT version_id INTO marketing_version_id FROM public.agent_versions WHERE agent_id = marketing_agent.agent_id AND version_number = 1;

    -- Update agents to reference their current versions
    UPDATE public.agents SET current_version_id = suna_version_id WHERE agent_id = suna_agent.agent_id;
    UPDATE public.agents SET current_version_id = code_version_id WHERE agent_id = code_agent.agent_id;
    UPDATE public.agents SET current_version_id = marketing_version_id WHERE agent_id = marketing_agent.agent_id;

    RAISE NOTICE 'Successfully created 3 agents with versions';
    RAISE NOTICE 'Suna: agent_id=%, version_id=%', suna_agent.agent_id, suna_version_id;
    RAISE NOTICE 'Code: agent_id=%, version_id=%', code_agent.agent_id, code_version_id;
    RAISE NOTICE 'Marketing: agent_id=%, version_id=%', marketing_agent.agent_id, marketing_version_id;
END $$;

-- Add the system_prompt column to agents table for API compatibility
DO $$
BEGIN
    -- Add system_prompt column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'system_prompt' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN system_prompt text;
    END IF;
    
    -- Add other API-required columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_default' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_default boolean DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_public' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_public boolean DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'configured_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN configured_mcps jsonb DEFAULT '[]'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'custom_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN custom_mcps jsonb DEFAULT '[]'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'agentpress_tools' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN agentpress_tools jsonb DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- Update agents with system_prompt from their versions for API compatibility
UPDATE public.agents 
SET 
    system_prompt = av.system_prompt,
    is_default = CASE WHEN agents.name = 'Suna' THEN true ELSE false END,
    is_public = false,
    configured_mcps = '[]'::jsonb,
    custom_mcps = '[]'::jsonb,
    agentpress_tools = '{}'::jsonb
FROM public.agent_versions av 
WHERE agents.current_version_id = av.version_id;

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';
