# Production Redirect Fix

## Problem
When creating a new account in production, users were being redirected to `localhost:3000` instead of staying on the production domain `https://ai-co-founder.netlify.app`.

## Root Cause
The issue was caused by incorrect environment configuration:

1. **Frontend Environment**: `NEXT_PUBLIC_ENV_MODE` was set to `LOCAL` instead of `PRODUCTION`
2. **Frontend URLs**: `NEXT_PUBLIC_URL` was pointing to `http://localhost:3000`
3. **Backend URLs**: `NEXT_PUBLIC_BACKEND_URL` was pointing to local development server
4. **Supabase Auth**: `site_url` was configured for localhost instead of production domain

## Solution Applied

### 1. Frontend Environment Configuration
Updated `frontend/.env.local` with production values:
```bash
NEXT_PUBLIC_SUPABASE_URL=https://pldcxtmyivlpueddnuml.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_BACKEND_URL=https://api.ai-co-founder.com/api
NEXT_PUBLIC_URL=https://ai-co-founder.netlify.app
NEXT_PUBLIC_ENV_MODE=PRODUCTION
```

### 2. Backend Environment Configuration
Updated `backend/.env`:
```bash
ENV_MODE=production
```

### 3. Supabase Authentication Configuration
Updated `backend/supabase/config.toml`:
```toml
site_url = "https://ai-co-founder.netlify.app"
additional_redirect_urls = ["https://ai-co-founder.netlify.app", "http://localhost:3000"]
```

## Files Modified

1. `frontend/.env.local` - Updated to production configuration
2. `backend/.env` - Changed ENV_MODE from local to production
3. `backend/supabase/config.toml` - Updated site_url to production domain
4. `frontend/.env.production` - Created production environment template

## Backup Files Created

The fix script automatically created backups:
- `frontend/.env.local.backup`
- `backend/.env.backup`
- `backend/supabase/config.toml.backup`

## How to Deploy the Fix

### Option 1: Using Netlify (Recommended)
1. Commit and push the changes to your repository
2. Netlify will automatically deploy the updated configuration
3. The `netlify.toml` file already contains the correct production environment variables

### Option 2: Manual Environment Variable Update
If you prefer to manage environment variables through Netlify dashboard:

1. Go to your Netlify site dashboard
2. Navigate to "Site settings" > "Environment variables"
3. Update the following variables:
   ```
   NEXT_PUBLIC_ENV_MODE=PRODUCTION
   NEXT_PUBLIC_URL=https://ai-co-founder.netlify.app
   NEXT_PUBLIC_BACKEND_URL=https://api.ai-co-founder.com/api
   ```
4. Trigger a new deployment

## Testing the Fix

1. Deploy the application to production
2. Navigate to your production URL: `https://ai-co-founder.netlify.app`
3. Try creating a new account
4. Verify that after account creation, you remain on the production domain
5. Check that email confirmation links also point to the production domain

## Switching Back to Local Development

If you need to switch back to local development, run:
```bash
python fix_production_redirect.py --local
```

This will restore the local development configuration:
- `NEXT_PUBLIC_ENV_MODE=LOCAL`
- `NEXT_PUBLIC_URL=http://localhost:3000`
- `NEXT_PUBLIC_BACKEND_URL=http://127.0.0.1:8000/api`

## Environment Management Scripts

Two scripts have been created to help manage environments:

1. **`fix_production_redirect.py`** - Main fix script
   - `python fix_production_redirect.py` - Apply production fix
   - `python fix_production_redirect.py --local` - Switch to local development

2. **`switch_environment.py`** - Advanced environment switcher (if needed)
   - `python switch_environment.py production` - Switch to production
   - `python switch_environment.py local` - Switch to local
   - `python switch_environment.py status` - Show current environment

## Verification Checklist

- [ ] Frontend environment uses production URLs
- [ ] Backend environment mode is set to production
- [ ] Supabase auth configuration points to production domain
- [ ] Application deployed to production
- [ ] Account creation tested and stays on production domain
- [ ] Email confirmation links point to production domain

## Additional Notes

- The fix maintains backward compatibility with local development
- Both localhost and production URLs are included in Supabase's allowed redirect URLs
- Environment variables in `netlify.toml` will override local `.env` files during deployment
- Backup files are created automatically before making changes

## Support

If you encounter any issues after applying this fix:

1. Check the Netlify deployment logs for any errors
2. Verify that all environment variables are correctly set
3. Test the authentication flow in an incognito/private browser window
4. Check the browser's developer console for any JavaScript errors

The fix addresses the core issue of environment configuration mismatch that was causing production users to be redirected to localhost during account creation.
