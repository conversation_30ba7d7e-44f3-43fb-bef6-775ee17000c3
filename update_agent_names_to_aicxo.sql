-- Update agent names from "Sun<PERSON>" to "AICxO" in the database
-- This script updates all references to maintain consistency with the rebranding

-- Update agents table
UPDATE public.agents 
SET 
    name = '<PERSON>CxO',
    description = REPLACE(description, '<PERSON><PERSON>', 'AICxO'),
    system_prompt = REPLACE(system_prompt, '<PERSON><PERSON>', '<PERSON><PERSON>x<PERSON>')
WHERE name = '<PERSON>a' OR name LIKE '%Suna%';

-- Update agent_versions table
UPDATE public.agent_versions 
SET 
    name = REPLACE(name, '<PERSON><PERSON>', 'AICxO'),
    version_name = REPLACE(version_name, 'Sun<PERSON>', 'AICxO'),
    system_prompt = REPLACE(system_prompt, 'Suna', 'AICxO')
WHERE name LIKE '%Suna%' OR system_prompt LIKE '%Suna%';

-- Update any agent descriptions that mention "Suna Assistant"
UPDATE public.agents 
SET 
    name = REPLACE(name, 'Suna Assistant', 'AICxO Assistant'),
    description = REPLACE(description, 'Sun<PERSON> Assistant', 'AICxO Assistant'),
    description = REPLACE(description, 'powered by Sun<PERSON>', 'powered by AICxO')
WHERE description LIKE '%Suna%';

-- Update system prompts to use AICxO identity
UPDATE public.agents 
SET system_prompt = 'You are AICxO, an AI Assistant. You help with various tasks including business strategy, product development, market analysis, and general assistance. Be helpful, insightful, and supportive.'
WHERE system_prompt LIKE '%You are Suna%';

UPDATE public.agent_versions 
SET system_prompt = 'You are AICxO, an AI Assistant. You help with various tasks including business strategy, product development, market analysis, and general assistance. Be helpful, insightful, and supportive.'
WHERE system_prompt LIKE '%You are Suna%';

-- Verify the changes
SELECT name, description, system_prompt FROM public.agents WHERE name LIKE '%AICxO%' OR description LIKE '%AICxO%';
SELECT name, version_name, system_prompt FROM public.agent_versions WHERE name LIKE '%AICxO%' OR system_prompt LIKE '%AICxO%';
