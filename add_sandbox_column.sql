-- Add sandbox column to projects table
-- This column will store sandbox information as <PERSON><PERSON><PERSON>

ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS sandbox JSONB DEFAULT NULL;

-- Add a comment to document the column
COMMENT ON COLUMN projects.sandbox IS 'Stores sandbox configuration and metadata as JSON';

-- Create an index on the sandbox column for better performance when querying by sandbox ID
CREATE INDEX IF NOT EXISTS idx_projects_sandbox_id 
ON projects USING GIN ((sandbox->>'id'));

-- Verify the column was added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND column_name = 'sandbox';
