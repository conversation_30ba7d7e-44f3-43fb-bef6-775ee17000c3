# AI Co-Founder Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application environment (development, staging, production)
VITE_NODE_ENV=development

# Application version
VITE_APP_VERSION=1.0.0

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Backend API base URL
VITE_API_BASE_URL=http://127.0.0.1:8000

# API request timeout in milliseconds
VITE_API_TIMEOUT=30000

# Legacy API URL (for backward compatibility)
VITE_API_URL=http://localhost:8000

# =============================================================================
# AUTHENTICATION
# =============================================================================

# JWT secret for frontend validation
VITE_JWT_SECRET=your-jwt-secret-key

# =============================================================================
# AI SERVICES
# =============================================================================

# OpenAI API key for AI agents
VITE_OPENAI_API_KEY=sk-your-openai-key

# =============================================================================
# VIDEO CREATION SERVICE (TopView.ai)
# =============================================================================

# TopView.ai API credentials
# Get these from: https://topview.ai/dashboard/api
VITE_VIDEO_API_KEY=your_topview_api_key_here
VITE_VIDEO_UID=your_topview_uid_here

# Note: If these are not provided, the app will run in mock mode

# =============================================================================
# LIVEKIT CONFIGURATION (Real-time Video/Audio)
# =============================================================================

# LiveKit server URL
VITE_LIVEKIT_URL=wss://your-project.livekit.cloud

# LiveKit API credentials
VITE_LIVEKIT_API_KEY=your-livekit-api-key
VITE_LIVEKIT_API_SECRET=your-livekit-api-secret

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Bolt.new API key
VITE_BOLT_API_KEY=your-bolt-api-key

# Document Generator API key
VITE_DOCUMENT_GENERATOR_API_KEY=your-document-generator-key

# Tavus API key (legacy video service)
VITE_TAVUS_API_KEY=your-tavus-api-key

# Apollo API key
VITE_APOLLO_API_KEY=your-apollo-api-key