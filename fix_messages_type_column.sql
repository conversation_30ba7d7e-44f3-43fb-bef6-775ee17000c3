-- Fix messages table schema - ensure type column exists and is properly configured
-- This addresses the "Could not find the 'type' column of 'messages' in the schema cache" error

BEGIN;

-- First, let's check if we have a 'role' column instead of 'type'
-- If so, we'll rename it to 'type' to match what the application expects

-- Check current table structure and add missing columns
DO $$
BEGIN
    -- Add type column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'type'
        AND table_schema = 'public'
    ) THEN
        -- Check if we have a 'role' column that we can rename
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'messages' 
            AND column_name = 'role'
            AND table_schema = 'public'
        ) THEN
            -- Rename 'role' to 'type'
            ALTER TABLE messages RENAME COLUMN role TO type;
            RAISE NOTICE 'Renamed role column to type';
        ELSE
            -- Add type column from scratch
            ALTER TABLE messages ADD COLUMN type TEXT NOT NULL DEFAULT 'user';
            RAISE NOTICE 'Added type column';
        END IF;
    END IF;

    -- Ensure message_id column exists (some schemas use 'id' instead)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'message_id'
        AND table_schema = 'public'
    ) THEN
        -- Check if we have an 'id' column that we can rename
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'messages' 
            AND column_name = 'id'
            AND table_schema = 'public'
        ) THEN
            -- Rename 'id' to 'message_id'
            ALTER TABLE messages RENAME COLUMN id TO message_id;
            RAISE NOTICE 'Renamed id column to message_id';
        ELSE
            -- Add message_id column
            ALTER TABLE messages ADD COLUMN message_id UUID PRIMARY KEY DEFAULT gen_random_uuid();
            RAISE NOTICE 'Added message_id column';
        END IF;
    END IF;

    -- Ensure thread_id column exists and has proper reference
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'thread_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN thread_id UUID REFERENCES threads(thread_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added thread_id column';
    END IF;

    -- Ensure is_llm_message column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'is_llm_message'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;
        RAISE NOTICE 'Added is_llm_message column';
    END IF;

    -- Ensure content column is JSONB (not TEXT)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'content'
        AND data_type = 'text'
        AND table_schema = 'public'
    ) THEN
        -- Convert content from TEXT to JSONB
        ALTER TABLE messages ALTER COLUMN content TYPE JSONB USING content::jsonb;
        RAISE NOTICE 'Converted content column to JSONB';
    ELSIF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'content'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN content JSONB NOT NULL DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Added content column as JSONB';
    END IF;

    -- Ensure metadata column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'metadata'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN metadata JSONB DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Added metadata column';
    END IF;

    -- Ensure agent_id column exists (for agent tracking)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'agent_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN agent_id UUID REFERENCES agents(agent_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added agent_id column';
    END IF;

    -- Ensure agent_version_id column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'agent_version_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN agent_version_id UUID REFERENCES agent_versions(version_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added agent_version_id column';
    END IF;

    -- Ensure timestamps exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'created_at'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL;
        RAISE NOTICE 'Added created_at column';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'updated_at'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE messages ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL;
        RAISE NOTICE 'Added updated_at column';
    END IF;

END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_thread_id ON messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);
CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);
CREATE INDEX IF NOT EXISTS idx_messages_agent_id ON messages(agent_id);
CREATE INDEX IF NOT EXISTS idx_messages_agent_version_id ON messages(agent_version_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);

-- Update existing messages to have proper type values
UPDATE messages 
SET type = 'user' 
WHERE type IS NULL OR type = '';

UPDATE messages 
SET is_llm_message = false 
WHERE type = 'user' AND is_llm_message = true;

UPDATE messages 
SET is_llm_message = true 
WHERE type IN ('assistant', 'system', 'tool', 'status') AND is_llm_message = false;

-- Create or update the updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at if it doesn't exist
DROP TRIGGER IF EXISTS update_messages_updated_at ON messages;
CREATE TRIGGER update_messages_updated_at
    BEFORE UPDATE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Refresh the schema cache by notifying PostgREST
NOTIFY pgrst, 'reload schema';

COMMIT;

-- Display final table structure for verification
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'messages' 
AND table_schema = 'public'
ORDER BY ordinal_position;
