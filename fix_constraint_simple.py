#!/usr/bin/env python3
"""
Simple fix for the messages table role constraint using existing database connection
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.supabase import get_supabase_client
import asyncio

async def fix_role_constraint():
    """Fix the messages table role constraint"""
    
    print("🔧 Fixing messages table role constraint...")
    print("=" * 60)
    
    try:
        # Get the Supabase client using existing connection
        supabase = await get_supabase_client()
        
        print("📋 Executing SQL commands to fix role constraint...")
        
        # Drop the existing constraint
        drop_constraint_sql = """
        ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_role_check;
        """
        
        # Add the new constraint with all required roles
        add_constraint_sql = """
        ALTER TABLE public.messages ADD CONSTRAINT messages_role_check 
        CHECK (role IN (
            'user', 
            'assistant', 
            'system', 
            'tool', 
            'status', 
            'assistant_response_end',
            'assistant_response_start',
            'tool_result',
            'error'
        ));
        """
        
        # Execute the SQL commands using raw SQL
        print("   🗑️  Dropping old constraint...")
        result1 = supabase.rpc('exec_sql', {'sql': drop_constraint_sql}).execute()
        print("   ✅ Old constraint dropped")
        
        print("   ➕ Adding new constraint...")
        result2 = supabase.rpc('exec_sql', {'sql': add_constraint_sql}).execute()
        print("   ✅ New constraint added")
        
        # Test the fix
        print("\n🧪 Testing the constraint fix...")
        test_sql = """
        SELECT conname, consrc 
        FROM pg_constraint 
        WHERE conrelid = 'public.messages'::regclass 
        AND conname = 'messages_role_check';
        """
        
        test_result = supabase.rpc('exec_sql', {'sql': test_sql}).execute()
        if test_result.data:
            print("✅ Constraint verification successful!")
            print(f"   New constraint: {test_result.data}")
        
        print("\n🎉 Role constraint fix completed successfully!")
        print("   The application should now be able to save messages with all role types.")
        print("   Including: assistant_response_end, status, tool, etc.")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing role constraint: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Show manual fix instructions
        print("\n📝 MANUAL FIX REQUIRED:")
        print("   Please run the following SQL commands in your Supabase SQL editor:")
        print("   " + "=" * 70)
        print("   ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_role_check;")
        print("   ")
        print("   ALTER TABLE public.messages ADD CONSTRAINT messages_role_check")
        print("   CHECK (role IN (")
        print("       'user', 'assistant', 'system', 'tool', 'status',")
        print("       'assistant_response_end', 'assistant_response_start',")
        print("       'tool_result', 'error'")
        print("   ));")
        print("   " + "=" * 70)
        
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_role_constraint())
    if success:
        print("\n🚀 Ready to test! Try sending a message in the application.")
        print("   The 'assistant_response_end' role error should be resolved.")
    else:
        print("\n⚠️  Manual intervention required. See instructions above.")
        sys.exit(1)
