-- STEP 2: ADD FOREIGN KEY COLUMNS AND <PERSON><PERSON><PERSON>IONSHIPS
-- Run this AFTER step 1 completes successfully

-- Add account_id to projects table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        -- Update existing projects to have account_id
        UPDATE public.projects 
        SET account_id = (SELECT id FROM basejump.accounts LIMIT 1)
        WHERE account_id IS NULL;
    END IF;
END $$;

-- Add account_id and project_id to threads table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        -- Update existing threads to have account_id
        UPDATE public.threads 
        SET account_id = (SELECT id FROM basejump.accounts LIMIT 1)
        WHERE account_id IS NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE;
        -- Update existing threads to have project_id
        UPDATE public.threads 
        SET project_id = (SELECT id FROM public.projects LIMIT 1)
        WHERE project_id IS NULL;
    END IF;
END $$;

-- Add account_id and thread_id to messages table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.messages ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        -- Update existing messages to have account_id
        UPDATE public.messages 
        SET account_id = (SELECT id FROM basejump.accounts LIMIT 1)
        WHERE account_id IS NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'thread_id' AND table_schema = 'public') THEN
        ALTER TABLE public.messages ADD COLUMN thread_id uuid REFERENCES public.threads(id) ON DELETE CASCADE;
        -- Update existing messages to have thread_id
        UPDATE public.messages 
        SET thread_id = (SELECT id FROM public.threads LIMIT 1)
        WHERE thread_id IS NULL;
    END IF;
END $$;

-- Add agent_id to agent_versions table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_versions' AND column_name = 'agent_id' AND table_schema = 'public') THEN
        ALTER TABLE public.agent_versions ADD COLUMN agent_id uuid REFERENCES public.agents(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add account_id to agents table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        -- Update existing agents to have account_id
        UPDATE public.agents 
        SET account_id = (SELECT id FROM basejump.accounts LIMIT 1)
        WHERE account_id IS NULL;
    END IF;
END $$;

-- Add current_version_id to agents table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'current_version_id' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN current_version_id uuid REFERENCES public.agent_versions(id);
    END IF;
END $$;

-- Add unique constraint to agent_versions if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'agent_versions_agent_id_version_number_key' AND table_name = 'agent_versions') THEN
        ALTER TABLE public.agent_versions ADD CONSTRAINT agent_versions_agent_id_version_number_key UNIQUE(agent_id, version_number);
    END IF;
EXCEPTION
    WHEN duplicate_table THEN
        -- Constraint already exists, ignore
        NULL;
END $$;

-- Create initial versions for existing agents
INSERT INTO public.agent_versions (agent_id, version_number, name, description, instructions, system_prompt)
SELECT 
    id,
    1,
    name,
    description,
    instructions,
    instructions
FROM public.agents
WHERE id NOT IN (SELECT DISTINCT agent_id FROM public.agent_versions WHERE agent_id IS NOT NULL)
ON CONFLICT DO NOTHING;

-- Update agents to reference their current versions
UPDATE public.agents 
SET current_version_id = (
    SELECT id FROM public.agent_versions 
    WHERE agent_id = public.agents.id 
    AND version_number = 1 
    LIMIT 1
)
WHERE current_version_id IS NULL;

-- Update existing data with proper relationships
UPDATE public.threads 
SET account_id = (SELECT account_id FROM public.projects WHERE id = public.threads.project_id LIMIT 1)
WHERE account_id IS NULL AND project_id IS NOT NULL;

UPDATE public.messages 
SET account_id = (SELECT account_id FROM public.threads WHERE id = public.messages.thread_id LIMIT 1)
WHERE account_id IS NULL AND thread_id IS NOT NULL;
