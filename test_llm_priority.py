#!/usr/bin/env python3
"""
Test LLM priority: GROQ first, then Gemini fallback
"""

import requests
import json
import uuid
import time

def test_llm_priority():
    """Test that LLM uses GROQ first, then Gemini as fallback"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing LLM Priority: GROQ → Gemini")
    print("=" * 50)
    
    # Test 1: Test with GROQ model explicitly
    print("\n1. Testing GROQ Model Explicitly...")
    
    form_data = {
        'prompt': 'Hello! Please respond with exactly: "GROQ is working!" and nothing else.',
        'model_name': 'groq/llama-3.3-70b-versatile',
        'enable_thinking': 'false',
        'reasoning_effort': 'low',
        'stream': 'false',
        'enable_context_manager': 'false'
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/agent/initiate",
            data=form_data,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ GROQ model request successful")
            # Try to get some response content
            content = response.text[:200] if response.text else "No content"
            print(f"   Response preview: {content}...")
        else:
            print(f"   ❌ GROQ model request failed: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ GROQ model request error: {str(e)}")
    
    # Test 2: Test with Gemini model explicitly
    print("\n2. Testing Gemini Model Explicitly...")
    
    form_data['model_name'] = 'gemini/gemini-1.5-flash'
    form_data['prompt'] = 'Hello! Please respond with exactly: "Gemini is working!" and nothing else.'
    
    try:
        response = requests.post(
            f"{base_url}/api/agent/initiate",
            data=form_data,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Gemini model request successful")
            content = response.text[:200] if response.text else "No content"
            print(f"   Response preview: {content}...")
        else:
            print(f"   ❌ Gemini model request failed: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Gemini model request error: {str(e)}")
    
    # Test 3: Test default model (should use GROQ)
    print("\n3. Testing Default Model (should use GROQ)...")
    
    # Remove model_name to test default
    form_data_default = {
        'prompt': 'Hello! Please respond with exactly: "Default model is working!" and nothing else.',
        'enable_thinking': 'false',
        'reasoning_effort': 'low',
        'stream': 'false',
        'enable_context_manager': 'false'
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/agent/initiate",
            data=form_data_default,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Default model request successful")
            content = response.text[:200] if response.text else "No content"
            print(f"   Response preview: {content}...")
        else:
            print(f"   ❌ Default model request failed: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Default model request error: {str(e)}")
    
    # Test 4: Test model aliases
    print("\n4. Testing Model Aliases...")
    
    aliases_to_test = [
        ('groq-llama-3.3-70b', 'GROQ alias'),
        ('gemini-flash', 'Gemini alias'),
        ('claude-sonnet-4', 'Claude alias (should fallback)')
    ]
    
    for alias, description in aliases_to_test:
        print(f"\n   Testing {description}: {alias}")
        
        form_data_alias = {
            'prompt': f'Hello! Please respond with exactly: "{alias} alias is working!" and nothing else.',
            'model_name': alias,
            'enable_thinking': 'false',
            'reasoning_effort': 'low',
            'stream': 'false',
            'enable_context_manager': 'false'
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/agent/initiate",
                data=form_data_alias,
                timeout=30
            )
            
            print(f"     Status: {response.status_code}")
            if response.status_code == 200:
                print(f"     ✅ {description} successful")
                content = response.text[:150] if response.text else "No content"
                print(f"     Response preview: {content}...")
            else:
                print(f"     ❌ {description} failed: {response.text[:150]}")
                
        except Exception as e:
            print(f"     ❌ {description} error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 LLM Priority Test Complete!")
    print("\nNext steps:")
    print("1. Check the backend logs to see which models were actually used")
    print("2. Verify that GROQ is tried first, then Gemini as fallback")
    print("3. Test sending a message in the Suna UI to confirm end-to-end functionality")

if __name__ == "__main__":
    test_llm_priority()
