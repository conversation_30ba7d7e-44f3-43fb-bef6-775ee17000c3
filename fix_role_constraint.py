#!/usr/bin/env python3
"""
Fix the messages table role constraint to allow all roles used by the application
"""

import os
import sys
import asyncio
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

async def fix_role_constraint():
    """Fix the messages table role constraint"""
    
    # Get Supabase credentials
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required")
        return False
    
    print("🔧 Fixing messages table role constraint...")
    print("=" * 60)
    
    try:
        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # Read the SQL fix file
        with open('fix_messages_role_constraint.sql', 'r') as f:
            sql_commands = f.read()
        
        print("📋 Executing SQL commands to fix role constraint...")
        
        # Execute the SQL commands
        # Note: We'll execute this as raw SQL using the REST API
        result = supabase.rpc('exec_sql', {'sql': sql_commands}).execute()
        
        if result.data:
            print("✅ SQL commands executed successfully!")
            print(f"   Result: {result.data}")
        else:
            print("⚠️  SQL executed but no data returned")
        
        # Test the fix by checking the constraint
        print("\n🧪 Testing the constraint fix...")
        
        # Try to query the constraint
        constraint_check = supabase.rpc('exec_sql', {
            'sql': """
            SELECT conname, consrc 
            FROM pg_constraint 
            WHERE conrelid = 'public.messages'::regclass 
            AND conname = 'messages_role_check';
            """
        }).execute()
        
        if constraint_check.data:
            print("✅ Constraint check successful!")
            print(f"   Constraint: {constraint_check.data}")
        
        print("\n🎉 Role constraint fix completed successfully!")
        print("   The application should now be able to save messages with all role types.")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing role constraint: {e}")
        
        # Try alternative approach using direct SQL execution
        print("\n🔄 Trying alternative approach...")
        try:
            # Execute individual SQL commands
            commands = [
                "ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_role_check;",
                """ALTER TABLE public.messages ADD CONSTRAINT messages_role_check 
                   CHECK (role IN ('user', 'assistant', 'system', 'tool', 'status', 'assistant_response_end', 'assistant_response_start', 'tool_result', 'error'));"""
            ]
            
            for cmd in commands:
                result = supabase.rpc('exec_sql', {'sql': cmd}).execute()
                print(f"   ✅ Executed: {cmd[:50]}...")
            
            print("✅ Alternative approach successful!")
            return True
            
        except Exception as e2:
            print(f"❌ Alternative approach also failed: {e2}")
            
            # Manual approach - show the SQL that needs to be run
            print("\n📝 MANUAL FIX REQUIRED:")
            print("   Please run the following SQL commands manually in your Supabase SQL editor:")
            print("   " + "=" * 50)
            print("   ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_role_check;")
            print("   ALTER TABLE public.messages ADD CONSTRAINT messages_role_check")
            print("   CHECK (role IN ('user', 'assistant', 'system', 'tool', 'status', 'assistant_response_end', 'assistant_response_start', 'tool_result', 'error'));")
            print("   " + "=" * 50)
            
            return False

if __name__ == "__main__":
    success = asyncio.run(fix_role_constraint())
    if success:
        print("\n🚀 Ready to test! Try sending a message in the application.")
    else:
        print("\n⚠️  Manual intervention required. See instructions above.")
        sys.exit(1)
