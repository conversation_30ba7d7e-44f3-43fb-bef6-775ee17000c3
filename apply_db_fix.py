#!/usr/bin/env python3
"""
Apply database fix for is_llm_message column
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
from services.supabase import DBConnection

async def apply_database_fix():
    """Apply the database fix for is_llm_message column"""
    print("🔧 Applying Database Fix for is_llm_message Column")
    print("=" * 60)
    
    try:
        # Initialize database connection
        db_conn = DBConnection()
        client = await db_conn.client
        
        print("✅ Database connection established")
        
        # Check if column exists by trying to select it
        print("\n1. Checking if is_llm_message column exists...")
        try:
            result = await client.table('messages').select('id, is_llm_message').limit(1).execute()
            print("   ✅ is_llm_message column already exists!")
            print("   📊 Column check successful")
            return True
        except Exception as e:
            error_msg = str(e).lower()
            if "column" in error_msg and ("does not exist" in error_msg or "not found" in error_msg):
                print("   ❌ is_llm_message column is missing")
                print("   🔧 Need to add the column...")
            else:
                print(f"   ❓ Unexpected error: {str(e)}")
                print("   🔧 Proceeding with fix attempt...")
        
        # Apply the fix using SQL
        print("\n2. Applying database fix...")
        
        # Try using the rpc function to execute SQL
        sql_commands = [
            "ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;",
            "CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);",
            "CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);"
        ]
        
        for i, sql in enumerate(sql_commands, 1):
            try:
                print(f"   Executing command {i}/3...")
                # Try different approaches to execute SQL
                try:
                    # Method 1: Using rpc if available
                    await client.rpc('exec_sql', {'sql': sql}).execute()
                    print(f"   ✅ Command {i} executed successfully (rpc)")
                except Exception:
                    try:
                        # Method 2: Using postgrest directly
                        await client.postgrest.rpc('exec_sql', {'sql': sql}).execute()
                        print(f"   ✅ Command {i} executed successfully (postgrest)")
                    except Exception:
                        # Method 3: Log the SQL for manual execution
                        print(f"   ⚠️  Command {i} needs manual execution:")
                        print(f"      {sql}")
            except Exception as e:
                print(f"   ❌ Command {i} failed: {str(e)}")
        
        # Verify the fix
        print("\n3. Verifying the fix...")
        try:
            result = await client.table('messages').select('id, is_llm_message').limit(1).execute()
            print("   ✅ Verification successful! is_llm_message column is now available")
            
            # Update existing messages if any
            print("\n4. Updating existing messages...")
            try:
                # Update user messages to have is_llm_message = false
                user_update = await client.table('messages').update({'is_llm_message': False}).eq('type', 'user').execute()
                print(f"   ✅ Updated user messages: {len(user_update.data) if user_update.data else 0} rows")
                
                # Update assistant/system messages to have is_llm_message = true
                llm_update = await client.table('messages').update({'is_llm_message': True}).in_('type', ['assistant', 'system', 'tool']).execute()
                print(f"   ✅ Updated LLM messages: {len(llm_update.data) if llm_update.data else 0} rows")
                
            except Exception as e:
                print(f"   ⚠️  Could not update existing messages: {str(e)}")
                print("   💡 This is okay for new installations")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Verification failed: {str(e)}")
            print("\n💡 Manual Fix Required:")
            print("   Please run this SQL in your Supabase dashboard:")
            print("   ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;")
            return False
            
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        print("\n💡 Please check your Supabase configuration:")
        print("   - SUPABASE_URL")
        print("   - SUPABASE_SERVICE_ROLE_KEY or SUPABASE_ANON_KEY")
        return False

async def test_message_functionality():
    """Test if message creation works after the fix"""
    print("\n🧪 Testing Message Functionality")
    print("=" * 40)
    
    try:
        db_conn = DBConnection()
        client = await db_conn.client
        
        # Create a test thread first
        test_thread = {
            "title": "Database Test Thread",
            "account_id": "test-account",
            "metadata": {}
        }
        
        thread_result = await client.table('threads').insert(test_thread).execute()
        
        if not thread_result.data:
            print("   ❌ Could not create test thread")
            return False
            
        thread_id = thread_result.data[0]['id']
        print(f"   ✅ Test thread created: {thread_id}")
        
        # Create a test message
        test_message = {
            "content": "Test message for database verification",
            "type": "user",
            "thread_id": thread_id,
            "is_llm_message": False
        }
        
        message_result = await client.table('messages').insert(test_message).execute()
        
        if message_result.data:
            message_id = message_result.data[0]['id']
            print(f"   ✅ Test message created: {message_id}")
            
            # Clean up
            await client.table('messages').delete().eq('id', message_id).execute()
            await client.table('threads').delete().eq('id', thread_id).execute()
            print("   ✅ Test data cleaned up")
            
            return True
        else:
            print("   ❌ Could not create test message")
            return False
            
    except Exception as e:
        print(f"   ❌ Message test failed: {str(e)}")
        return False

async def main():
    """Main function"""
    print("🚀 Suna Database Fix Tool")
    print("=" * 60)
    
    # Apply the database fix
    fix_success = await apply_database_fix()
    
    if fix_success:
        # Test message functionality
        test_success = await test_message_functionality()
        
        if test_success:
            print("\n🎉 SUCCESS! Database is ready!")
            print("=" * 60)
            print("✅ is_llm_message column added")
            print("✅ Message creation working")
            print("✅ Database schema fixed")
            print("\n🚀 Next Steps:")
            print("1. Try sending a message in the Suna UI")
            print("2. Check backend logs to see GROQ → Gemini priority working")
            print("3. Verify that messages are being processed correctly")
        else:
            print("\n⚠️  Database fixed but message test failed")
            print("💡 Try sending a message in the UI anyway")
    else:
        print("\n❌ Database fix failed")
        print("💡 Manual intervention required - see instructions above")

if __name__ == "__main__":
    asyncio.run(main())
