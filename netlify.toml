[build]
  publish = "frontend/.next"
  command = "cd frontend && npm ci --legacy-peer-deps && npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--production=false"

  # Completely disable mise/rtx
  MISE_DISABLE = "1"
  MISE_SKIP = "1"
  RTX_DISABLE = "1"
  RTX_SKIP = "1"

  # Build optimizations
  NEXT_TELEMETRY_DISABLED = "1"
  DISABLE_ESLINT_PLUGIN = "true"

  # Memory optimizations
  NODE_OPTIONS = "--max-old-space-size=4096"

  # Supabase configuration
  NEXT_PUBLIC_SUPABASE_URL = "https://pldcxtmyivlpueddnuml.supabase.co"
  NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZGN4dG15aXZscHVlZGRudW1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4NTY5NTQsImV4cCI6MjA2NjQzMjk1NH0.yrYWj-hbqdS3nGTFBA1YvhAtKoY8BOVY1_OXA4_gAtA"

  # Supabase service role key for Netlify Functions (server-side)
  SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZGN4dG15aXZscHVlZGRudW1sIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDg1Njk1NCwiZXhwIjoyMDY2NDMyOTU0fQ.DLa5BTwIQ3hJmu-BoMADF7nF2CYSW1EeDJ-BCjLAIWE"

  # Application configuration
  NEXT_PUBLIC_ENV_MODE = "production"
  NEXT_PUBLIC_URL = "https://aicofounder.site"
  NEXT_PUBLIC_BACKEND_URL = "https://aicofounder.site/.netlify/functions/api"

# Netlify Next.js plugin for SSR support
[[plugins]]
  package = "@netlify/plugin-nextjs"

# API routes handled by Netlify Functions
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api/:splat"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Environment variables (these should be set in Netlify dashboard)
# NEXT_PUBLIC_SUPABASE_URL = "your-supabase-url"
# NEXT_PUBLIC_SUPABASE_ANON_KEY = "your-supabase-anon-key"
# SUPABASE_SERVICE_ROLE_KEY = "your-service-role-key"

# Functions configuration (if using Netlify Functions)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Dev server configuration
[dev]
  command = "npm run dev"
  port = 8888
  autoLaunch = false
