-- Fix missing agent_id column in messages table
-- This ensures the agent_id and agent_version_id columns exist and refreshes the schema cache

BEGIN;

-- Add agent_id column if it doesn't exist
ALTER TABLE messages ADD COLUMN IF NOT EXISTS agent_id UUID REFERENCES agents(agent_id) ON DELETE SET NULL;

-- Add agent_version_id column if it doesn't exist  
ALTER TABLE messages ADD COLUMN IF NOT EXISTS agent_version_id UUID REFERENCES agent_versions(version_id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_agent_id ON messages(agent_id);
CREATE INDEX IF NOT EXISTS idx_messages_agent_version_id ON messages(agent_version_id);

-- Add comments for documentation
COMMENT ON COLUMN messages.agent_id IS 'ID of the agent that generated this message. For user messages, this represents the agent that should respond to this message.';
COMMENT ON COLUMN messages.agent_version_id IS 'Specific version of the agent used for this message. This is the actual configuration that was active.';

-- Verify the columns exist
DO $$
DECLARE
    agent_id_exists boolean;
    agent_version_id_exists boolean;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'agent_id' 
        AND table_schema = 'public'
    ) INTO agent_id_exists;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'agent_version_id' 
        AND table_schema = 'public'
    ) INTO agent_version_id_exists;
    
    IF agent_id_exists THEN
        RAISE NOTICE 'agent_id column exists in messages table';
    ELSE
        RAISE NOTICE 'ERROR: agent_id column does NOT exist in messages table';
    END IF;
    
    IF agent_version_id_exists THEN
        RAISE NOTICE 'agent_version_id column exists in messages table';
    ELSE
        RAISE NOTICE 'ERROR: agent_version_id column does NOT exist in messages table';
    END IF;
END $$;

COMMIT;

-- Force refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';

-- Additional verification - show current messages table structure
DO $$
DECLARE
    col_record record;
BEGIN
    RAISE NOTICE 'Current messages table structure:';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'messages' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'messages.%: % (nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
END $$;
