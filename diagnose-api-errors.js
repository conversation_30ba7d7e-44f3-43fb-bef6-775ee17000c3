// API Error Diagnostic Script
// Run this in browser console on https://aicofounder.site to diagnose 404 errors

console.log('🔍 Diagnosing API Errors...');

const diagnoseApiErrors = async () => {
  const baseUrl = window.location.origin;
  
  console.log(`Base URL: ${baseUrl}`);
  console.log(`User Agent: ${navigator.userAgent}`);
  console.log(`Current URL: ${window.location.href}`);
  
  const tests = [
    // Test direct function access
    {
      name: 'Direct Function Test',
      url: '/.netlify/functions/test',
      description: 'Test if Netlify functions are working at all'
    },
    {
      name: 'Direct API Function',
      url: '/.netlify/functions/api',
      description: 'Test direct access to API function'
    },
    {
      name: 'API Function with Health',
      url: '/.netlify/functions/api?splat=health',
      description: 'Test API function with health parameter'
    },
    
    // Test redirected endpoints
    {
      name: 'Health Endpoint',
      url: '/api/health',
      description: 'Test health endpoint through redirect'
    },
    {
      name: 'Feature Flags',
      url: '/api/feature-flags/custom_agents',
      description: 'Test feature flags endpoint'
    },
    {
      name: 'Billing Subscription',
      url: '/api/billing/subscription',
      description: 'Test billing subscription endpoint'
    },
    {
      name: 'Billing Models',
      url: '/api/billing/available-models',
      description: 'Test billing models endpoint'
    }
  ];

  const results = [];

  for (const test of tests) {
    console.log(`\n🧪 ${test.name}: ${test.description}`);
    
    try {
      const startTime = performance.now();
      const response = await fetch(test.url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);
      
      console.log(`   URL: ${test.url}`);
      console.log(`   Status: ${response.status} ${response.statusText}`);
      console.log(`   Duration: ${duration}ms`);
      console.log(`   Headers:`, Object.fromEntries(response.headers.entries()));
      
      let responseData;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        try {
          responseData = await response.json();
          console.log(`   Response:`, responseData);
        } catch (e) {
          const text = await response.text();
          console.log(`   Response (text):`, text.substring(0, 200));
          responseData = { error: 'Failed to parse JSON', text: text.substring(0, 100) };
        }
      } else {
        const text = await response.text();
        console.log(`   Response (text):`, text.substring(0, 200));
        responseData = { text: text.substring(0, 100) };
      }
      
      results.push({
        ...test,
        status: response.status,
        statusText: response.statusText,
        duration,
        success: response.ok,
        data: responseData,
        headers: Object.fromEntries(response.headers.entries())
      });
      
      if (response.ok) {
        console.log(`   ✅ SUCCESS`);
      } else {
        console.log(`   ❌ FAILED`);
      }
      
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      results.push({
        ...test,
        status: 0,
        statusText: 'Network Error',
        success: false,
        error: error.message
      });
    }
  }

  // Summary
  console.log('\n📊 Diagnostic Summary:');
  console.log('======================');
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Successful: ${successful}/${tests.length}`);
  console.log(`❌ Failed: ${failed}/${tests.length}`);
  
  // Analyze patterns
  const netlifyFunctionTests = results.filter(r => r.url.includes('/.netlify/functions/'));
  const redirectTests = results.filter(r => r.url.startsWith('/api/'));
  
  console.log(`\n🔍 Analysis:`);
  console.log(`Netlify Functions: ${netlifyFunctionTests.filter(r => r.success).length}/${netlifyFunctionTests.length} working`);
  console.log(`API Redirects: ${redirectTests.filter(r => r.success).length}/${redirectTests.length} working`);
  
  if (netlifyFunctionTests.every(r => !r.success)) {
    console.log('❌ Netlify Functions are not working - deployment issue');
  } else if (redirectTests.every(r => !r.success)) {
    console.log('❌ Redirects are not working - _redirects file issue');
  } else {
    console.log('✅ Mixed results - check individual endpoints');
  }
  
  return results;
};

// Auto-run the diagnostic
diagnoseApiErrors().then(results => {
  console.log('\n🎯 Diagnostic completed!');
  console.log('Results stored in window.apiDiagnosticResults');
  window.apiDiagnosticResults = results;
});

// Export for manual use
window.diagnoseApiErrors = diagnoseApiErrors;
