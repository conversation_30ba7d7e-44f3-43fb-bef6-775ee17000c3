#!/usr/bin/env python3
"""
Test script to trigger sandbox initialization and see the detailed logs
"""
import asyncio
import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Add the backend directory to the path
sys.path.append('backend')

from agent.api import initialize_sandbox_for_project
from database.supabase import get_client

# Load environment variables
load_dotenv('backend/.env')

async def test_sandbox_init():
    """Test sandbox initialization with detailed logging"""
    
    # Use the project ID from the logs
    project_id = "4e78e1b7-2b13-4d26-87e1-54bd5fc2a73c"
    
    print(f"Testing sandbox initialization for project: {project_id}")
    
    try:
        # Get the database client
        client = await get_client()
        print(f"Got database client: {client}")
        
        # Try to initialize sandbox
        sandbox_id, sandbox = await initialize_sandbox_for_project(project_id, client)
        print(f"✅ Success! Sandbox ID: {sandbox_id}, Sandbox: {sandbox}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_sandbox_init())
