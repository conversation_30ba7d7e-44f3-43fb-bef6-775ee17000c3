#!/usr/bin/env python3
"""
<PERSON><PERSON>t to check the current messages table schema and fix it
"""

import os
import sys
from supabase import create_client, Client

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def check_and_fix_schema():
    # Load environment variables
    from dotenv import load_dotenv

    # Try different paths for .env file
    env_paths = [
        'backend/.env',
        '.env',
        os.path.join(os.path.dirname(__file__), 'backend', '.env'),
        os.path.join(os.path.dirname(__file__), '.env')
    ]

    env_loaded = False
    for env_path in env_paths:
        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"📁 Loaded environment from: {env_path}")
            env_loaded = True
            break

    if not env_loaded:
        print("⚠️  No .env file found, trying environment variables...")
    
    # Initialize Supabase client
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not url or not key:
        print("❌ Error: Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY")
        return False
    
    supabase: Client = create_client(url, key)
    
    try:
        print("🔍 Checking current messages table schema...")
        
        # Try to get a sample message to see the current schema
        try:
            sample_result = supabase.table('messages').select('*').limit(1).execute()
            print("✅ Messages table exists and is accessible")
            
            if sample_result.data:
                print("📋 Sample message structure:")
                sample_message = sample_result.data[0]
                for key, value in sample_message.items():
                    print(f"  - {key}: {type(value).__name__}")
                
                # Check if 'type' column exists
                if 'type' in sample_message:
                    print("✅ 'type' column exists")
                elif 'role' in sample_message:
                    print("⚠️  Found 'role' column instead of 'type' - need to rename")
                    # Try to rename role to type
                    try:
                        # This is a bit tricky with Supabase, let's try a different approach
                        print("🔧 Attempting to add 'type' column...")
                        
                        # First, let's try to add the type column
                        # We'll do this by creating a new message with the type field
                        test_data = {
                            'thread_id': sample_message.get('thread_id'),
                            'type': 'test',
                            'content': {'text': 'test'},
                            'is_llm_message': False
                        }
                        
                        # Try to insert with type column
                        test_insert = supabase.table('messages').insert(test_data).execute()
                        if test_insert.data:
                            print("✅ Successfully added message with 'type' column")
                            # Clean up test message
                            test_id = test_insert.data[0].get('message_id') or test_insert.data[0].get('id')
                            if test_id:
                                supabase.table('messages').delete().eq('message_id', test_id).execute()
                                print("🗑️  Cleaned up test message")
                        
                    except Exception as e:
                        print(f"❌ Failed to add type column: {str(e)}")
                        
                        # The error might give us clues about what's missing
                        error_str = str(e)
                        if "type" in error_str and "column" in error_str:
                            print("🔧 The 'type' column is definitely missing from the database")
                            print("📝 You need to run a database migration to add this column")
                            print("\n🛠️  Manual fix required:")
                            print("1. Go to your Supabase dashboard")
                            print("2. Open the SQL Editor")
                            print("3. Run this command:")
                            print("   ALTER TABLE messages ADD COLUMN type TEXT NOT NULL DEFAULT 'user';")
                            print("4. Also run:")
                            print("   CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);")
                            return False
                else:
                    print("❌ Neither 'type' nor 'role' column found")
                
                # Check if 'is_llm_message' column exists
                if 'is_llm_message' not in sample_message:
                    print("⚠️  'is_llm_message' column is missing")
                    print("📝 You need to add this column too:")
                    print("   ALTER TABLE messages ADD COLUMN is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;")
                else:
                    print("✅ 'is_llm_message' column exists")
                    
            else:
                print("📭 Messages table is empty - schema check inconclusive")
                
                # Try to insert a test message to see what fails
                print("🧪 Testing message insertion...")
                try:
                    test_data = {
                        'type': 'test',
                        'content': {'text': 'test'},
                        'is_llm_message': False
                    }
                    
                    # This will fail if columns are missing, giving us the exact error
                    test_result = supabase.table('messages').insert(test_data).execute()
                    print("✅ Test insertion successful - schema seems correct")
                    
                    # Clean up
                    if test_result.data:
                        test_id = test_result.data[0].get('message_id') or test_result.data[0].get('id')
                        if test_id:
                            supabase.table('messages').delete().eq('message_id', test_id).execute()
                    
                except Exception as e:
                    print(f"❌ Test insertion failed: {str(e)}")
                    error_str = str(e)
                    
                    if "'type' column" in error_str:
                        print("🔧 Confirmed: 'type' column is missing")
                    if "'is_llm_message'" in error_str:
                        print("🔧 Confirmed: 'is_llm_message' column is missing")
                    
                    print("\n🛠️  Required SQL commands to fix:")
                    print("ALTER TABLE messages ADD COLUMN IF NOT EXISTS type TEXT NOT NULL DEFAULT 'user';")
                    print("ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;")
                    print("CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);")
                    print("CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);")
                    
                    return False
                
        except Exception as e:
            print(f"❌ Error accessing messages table: {str(e)}")
            return False
        
        print("\n🎉 Schema check completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking schema: {str(e)}")
        return False

if __name__ == "__main__":
    success = check_and_fix_schema()
    sys.exit(0 if success else 1)
