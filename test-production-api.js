// Production API Test Script
// Run this in the browser console on https://aicofounder.site

console.log('🧪 Testing Production API Endpoints...');

const testEndpoints = async () => {
  const baseUrl = 'https://aicofounder.site';
  const endpoints = [
    { name: 'Health Check', url: '/api/health' },
    { name: 'Feature Flags - Custom Agents', url: '/api/feature-flags/custom_agents' },
    { name: 'Feature Flags - Agent Marketplace', url: '/api/feature-flags/agent_marketplace' },
    { name: 'Billing - Available Models', url: '/api/billing/available-models' },
    { name: 'Billing - Subscription', url: '/api/billing/subscription' },
    { name: 'Projects', url: '/api/projects' },
    { name: 'Threads', url: '/api/threads' },
    { name: 'Admin Verify', url: '/api/admin/verify' }
  ];

  const results = [];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint.name}...`);
      const response = await fetch(`${baseUrl}${endpoint.url}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${endpoint.name}: SUCCESS`, data);
        results.push({ ...endpoint, status: 'SUCCESS', data });
      } else {
        const data = await response.json().catch(() => ({ error: 'Failed to parse response' }));
        console.log(`❌ ${endpoint.name}: FAILED (${response.status})`, data);
        results.push({ ...endpoint, status: 'FAILED', error: data, statusCode: response.status });
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ERROR`, error.message);
      results.push({ ...endpoint, status: 'ERROR', error: error.message });
    }
  }

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const successful = results.filter(r => r.status === 'SUCCESS').length;
  const failed = results.filter(r => r.status !== 'SUCCESS').length;
  
  console.log(`✅ Successful: ${successful}/${endpoints.length}`);
  console.log(`❌ Failed: ${failed}/${endpoints.length}`);
  
  if (failed > 0) {
    console.log('\n🔍 Failed Endpoints:');
    results.filter(r => r.status !== 'SUCCESS').forEach(result => {
      console.log(`- ${result.name}: ${result.error?.message || result.error || result.statusCode}`);
    });
  }

  return results;
};

// Auto-run the test
testEndpoints().then(results => {
  console.log('\n🎯 Production API test completed!');
  if (results.every(r => r.status === 'SUCCESS')) {
    console.log('🎉 All API endpoints are working correctly!');
  } else {
    console.log('⚠️ Some endpoints need attention. Check the logs above.');
  }
});

// Export for manual testing
window.testProductionApi = testEndpoints;
