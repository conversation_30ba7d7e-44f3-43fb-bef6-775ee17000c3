<!DOCTYPE html>
<html>
<head>
    <title>API Test - Netlify Functions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Netlify Functions API Test</h1>
    <p>This page tests the migrated API endpoints to verify the 404 fixes.</p>
    
    <div id="results"></div>
    
    <button onclick="testAllEndpoints()">🚀 Test All Endpoints</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>
    
    <script>
        const results = document.getElementById('results');
        
        function addResult(title, success, data) {
            const div = document.createElement('div');
            div.className = `test ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <h3>${success ? '✅' : '❌'} ${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(div);
        }
        
        function clearResults() {
            results.innerHTML = '';
        }
        
        async function testEndpoint(name, url) {
            try {
                console.log(`Testing ${name}: ${url}`);
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`${name} (${response.status})`, true, data);
                } else {
                    addResult(`${name} (${response.status})`, false, data);
                }
            } catch (error) {
                addResult(`${name} (Error)`, false, { error: error.message });
            }
        }
        
        async function testAllEndpoints() {
            clearResults();
            
            addResult('Starting API Tests', true, { 
                timestamp: new Date().toISOString(),
                baseUrl: window.location.origin 
            });
            
            // Test all endpoints
            const endpoints = [
                ['Health Check', '/api/health'],
                ['Feature Flags - Custom Agents', '/api/feature-flags/custom_agents'],
                ['Feature Flags - Agent Marketplace', '/api/feature-flags/agent_marketplace'],
                ['Billing - Available Models', '/api/billing/available-models'],
                ['Billing - Subscription', '/api/billing/subscription'],
                ['Projects', '/api/projects'],
                ['Threads', '/api/threads']
            ];
            
            for (const [name, url] of endpoints) {
                await testEndpoint(name, url);
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            addResult('All Tests Complete', true, { 
                timestamp: new Date().toISOString(),
                note: 'Check results above for any failures'
            });
        }
        
        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(testAllEndpoints, 1000);
        });
    </script>
</body>
</html>
