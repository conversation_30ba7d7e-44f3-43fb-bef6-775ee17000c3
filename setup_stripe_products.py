#!/usr/bin/env python3
"""
Stripe Product Setup Script for AI Co-Founder
This script creates all the subscription products and prices in your Stripe account.

Usage:
1. Install stripe: pip install stripe
2. Set your Stripe secret key as an environment variable: export STRIPE_SECRET_KEY="sk_test_..."
3. Run: python setup_stripe_products.py

Or run with key directly (less secure):
python setup_stripe_products.py --api-key "sk_test_..."
"""

import stripe
import os
import argparse
import sys

def setup_products(api_key):
    """Set up all subscription products and prices in Stripe."""
    stripe.api_key = api_key
    
    products = [
        # Monthly Plans
        {
            "name": "Free - Monthly",
            "description": "$5 free AI tokens included\nPublic projects\nBasic Models\nCommunity support",
            "price": 0,
            "interval": "month",
            "metadata": {
                "plan_type": "free",
                "billing_cycle": "monthly",
                "ai_credits": "5",
                "features": "public_projects,basic_models,community_support"
            }
        },
        {
            "name": "Plus - Monthly", 
            "description": "$20 AI token credits/month\nPrivate projects\nPremium AI Models\nCommunity support",
            "price": 2000,  # $20.00
            "interval": "month",
            "metadata": {
                "plan_type": "plus",
                "billing_cycle": "monthly", 
                "ai_credits": "20",
                "features": "private_projects,premium_models,community_support"
            }
        },
        {
            "name": "Pro - Monthly",
            "description": "$50 AI token credits/month\nPrivate projects\nPremium AI Models\nCommunity support", 
            "price": 5000,  # $50.00
            "interval": "month",
            "metadata": {
                "plan_type": "pro",
                "billing_cycle": "monthly",
                "ai_credits": "50", 
                "features": "private_projects,premium_models,community_support"
            }
        },
        {
            "name": "Ultra - Monthly",
            "description": "$200 AI token credits/month\nPrivate projects\nPremium AI Models\nPriority support",
            "price": 20000,  # $200.00
            "interval": "month",
            "metadata": {
                "plan_type": "ultra",
                "billing_cycle": "monthly",
                "ai_credits": "200",
                "features": "private_projects,premium_models,priority_support"
            }
        },
        # Annual Plans (with discounts)
        {
            "name": "Free - Annual",
            "description": "$5 free AI tokens included\nPublic projects\nBasic Models\nCommunity support",
            "price": 0,
            "interval": "year",
            "metadata": {
                "plan_type": "free",
                "billing_cycle": "annual",
                "ai_credits": "5",
                "features": "public_projects,basic_models,community_support"
            }
        },
        {
            "name": "Plus - Annual",
            "description": "$17/month billed yearly (15% off)\n$20 AI token credits/month\nPrivate projects\nPremium AI Models\nCommunity support",
            "price": 20400,  # $17/month × 12 = $204/year (15% discount)
            "interval": "year",
            "metadata": {
                "plan_type": "plus",
                "billing_cycle": "annual",
                "ai_credits": "20",
                "features": "private_projects,premium_models,community_support",
                "discount": "15"
            }
        },
        {
            "name": "Pro - Annual", 
            "description": "$43/month billed yearly (14% off)\n$50 AI token credits/month\nPrivate projects\nPremium AI Models\nCommunity support",
            "price": 51600,  # $43/month × 12 = $516/year (14% discount)
            "interval": "year",
            "metadata": {
                "plan_type": "pro",
                "billing_cycle": "annual",
                "ai_credits": "50",
                "features": "private_projects,premium_models,community_support",
                "discount": "14"
            }
        },
        {
            "name": "Ultra - Annual",
            "description": "$170/month billed yearly (15% off)\n$200 AI token credits/month\nPrivate projects\nPremium AI Models\nPriority support", 
            "price": 204000,  # $170/month × 12 = $2040/year (15% discount)
            "interval": "year",
            "metadata": {
                "plan_type": "ultra",
                "billing_cycle": "annual",
                "ai_credits": "200",
                "features": "private_projects,premium_models,priority_support",
                "discount": "15"
            }
        },
    ]

    created_products = []
    
    print("🚀 Setting up AI Co-Founder subscription products in Stripe...\n")
    
    for plan in products:
        try:
            # Create the product
            product = stripe.Product.create(
                name=plan["name"],
                description=plan["description"],
                metadata=plan["metadata"]
            )
            
            # Create the price
            if plan["price"] == 0:
                # For free plans, create a price but don't make it recurring
                price = stripe.Price.create(
                    unit_amount=plan["price"],
                    currency="usd",
                    product=product.id,
                    metadata=plan["metadata"]
                )
            else:
                # For paid plans, create recurring prices
                price = stripe.Price.create(
                    unit_amount=plan["price"],
                    currency="usd",
                    recurring={"interval": plan["interval"]},
                    product=product.id,
                    metadata=plan["metadata"]
                )
            
            created_products.append({
                "name": plan["name"],
                "product_id": product.id,
                "price_id": price.id,
                "amount": plan["price"],
                "interval": plan["interval"]
            })
            
            print(f"✅ Created: {plan['name']}")
            print(f"   Product ID: {product.id}")
            print(f"   Price ID: {price.id}")
            print(f"   Amount: ${plan['price']/100:.2f} {plan['interval']}ly")
            print()
            
        except stripe.error.StripeError as e:
            print(f"❌ Error creating {plan['name']}: {e}")
            continue
    
    print(f"\n🎉 Successfully created {len(created_products)} products!")
    print("\n📋 Summary of created products:")
    print("-" * 60)
    
    for product in created_products:
        print(f"{product['name']:<20} | {product['price_id']}")
    
    print("\n💡 Next steps:")
    print("1. Update your application's environment variables with these price IDs")
    print("2. Test the subscription flow in your application")
    print("3. When ready, switch to live mode with your live Stripe keys")
    
    return created_products

def main():
    parser = argparse.ArgumentParser(description='Set up Stripe products for AI Co-Founder')
    parser.add_argument('--api-key', help='Stripe secret API key (alternatively set STRIPE_SECRET_KEY env var)')
    args = parser.parse_args()
    
    # Get API key from argument or environment variable
    api_key = args.api_key or os.getenv('STRIPE_SECRET_KEY')
    
    if not api_key:
        print("❌ Error: Stripe API key not provided!")
        print("\nPlease either:")
        print("1. Set environment variable: export STRIPE_SECRET_KEY='sk_test_...'")
        print("2. Use --api-key argument: python setup_stripe_products.py --api-key 'sk_test_...'")
        sys.exit(1)
    
    if not api_key.startswith(('sk_test_', 'sk_live_')):
        print("❌ Error: Invalid Stripe API key format!")
        print("API key should start with 'sk_test_' or 'sk_live_'")
        sys.exit(1)
    
    # Warn if using live key
    if api_key.startswith('sk_live_'):
        response = input("⚠️  WARNING: You're using a LIVE Stripe key. This will create real products. Continue? (y/N): ")
        if response.lower() != 'y':
            print("Cancelled.")
            sys.exit(0)
    
    try:
        products = setup_products(api_key)
        print(f"\n✅ Setup complete! Created {len(products)} products in Stripe.")
        
    except stripe.error.AuthenticationError:
        print("❌ Error: Invalid Stripe API key!")
    except stripe.error.StripeError as e:
        print(f"❌ Stripe error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
