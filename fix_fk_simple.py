#!/usr/bin/env python3
"""
Simple script to fix the foreign key constraint issue
"""
import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_SERVICE_ROLE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
    print("Error: Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY")
    sys.exit(1)

# Create Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def analyze_issue():
    """Analyze the current issue"""
    print("=== Analyzing the Foreign Key Issue ===")
    
    # Create a test project to see what happens
    try:
        project_data = {
            "account_id": "cd4d9095-2924-414f-9609-f201507f965e",
            "name": "Analysis Test Project"
        }
        
        project_result = supabase.table('projects').insert(project_data).execute()
        if project_result.data:
            project = project_result.data[0]
            print(f"Created project: {project}")
            
            # Check what the project_id value is vs the id value
            project_id_value = project.get('project_id')
            id_value = project.get('id')
            
            print(f"Project 'id' column: {id_value}")
            print(f"Project 'project_id' column: {project_id_value}")
            
            # Now check if we can find this project using different queries
            print("\nTesting project lookups:")
            
            # Try to find by id
            try:
                by_id = supabase.table('projects').select('*').eq('id', id_value).execute()
                print(f"Found by 'id' column: {len(by_id.data)} records")
            except Exception as e:
                print(f"Error finding by 'id': {e}")
            
            # Try to find by project_id
            try:
                by_project_id = supabase.table('projects').select('*').eq('project_id', project_id_value).execute()
                print(f"Found by 'project_id' column: {len(by_project_id.data)} records")
            except Exception as e:
                print(f"Error finding by 'project_id': {e}")
            
            # The issue is likely that the FK constraint references the wrong column
            # Let's check what the FK constraint is actually looking for
            print(f"\nThe foreign key constraint is looking for project_id='{project_id_value}' in the projects table")
            print(f"But the actual primary key is 'id'='{id_value}'")
            print(f"This is why the FK constraint fails!")
            
            # Clean up
            supabase.table('projects').delete().eq('id', id_value).execute()
            print("Cleaned up test project")
            
            return id_value, project_id_value
            
    except Exception as e:
        print(f"Error in analysis: {e}")
        return None, None

def fix_the_issue():
    """Fix the issue by updating the backend code to use the correct column"""
    print("\n=== The Solution ===")
    print("The issue is that:")
    print("1. Projects table has 'id' as primary key")
    print("2. Projects table also has 'project_id' column (which gets a different UUID)")
    print("3. The FK constraint expects to find the 'project_id' value in the projects table")
    print("4. But we should be using the 'id' value for the FK relationship")
    print("")
    print("We need to update the backend code to:")
    print("1. Use the 'id' column value (not 'project_id') when creating threads")
    print("2. Or fix the FK constraint to reference the correct column")
    
def test_fix():
    """Test the fix by creating a project and thread correctly"""
    print("\n=== Testing the Fix ===")
    
    try:
        # Create a project
        project_data = {
            "account_id": "cd4d9095-2924-414f-9609-f201507f965e",
            "name": "Fix Test Project"
        }
        
        project_result = supabase.table('projects').insert(project_data).execute()
        if project_result.data:
            project = project_result.data[0]
            
            # Use the 'id' column (primary key) instead of 'project_id'
            actual_project_id = project.get('id')  # This is the real primary key
            
            print(f"Created project with id: {actual_project_id}")
            
            # Now try to create a thread using the correct project ID
            thread_data = {
                "project_id": actual_project_id,  # Use the actual primary key
                "account_id": "cd4d9095-2924-414f-9609-f201507f965e",
                "title": "Test Thread"
            }
            
            thread_result = supabase.table('threads').insert(thread_data).execute()
            if thread_result.data:
                thread = thread_result.data[0]
                thread_id = thread.get('id')
                print(f"Successfully created thread with id: {thread_id}")
                
                # Clean up
                supabase.table('threads').delete().eq('id', thread_id).execute()
                print("Cleaned up test thread")
            else:
                print("Failed to create thread")
                
            # Clean up project
            supabase.table('projects').delete().eq('id', actual_project_id).execute()
            print("Cleaned up test project")
            
            return True
            
    except Exception as e:
        print(f"Error in test: {e}")
        return False

if __name__ == "__main__":
    # Analyze the issue
    id_val, project_id_val = analyze_issue()
    
    # Explain the fix
    fix_the_issue()
    
    # Test the fix
    success = test_fix()
    
    if success:
        print("\n✅ Fix confirmed! The solution is to use the 'id' column instead of 'project_id' column.")
        print("The backend code needs to be updated to use project['id'] instead of project['project_id']")
    else:
        print("\n❌ Fix failed. Need to investigate further.")
