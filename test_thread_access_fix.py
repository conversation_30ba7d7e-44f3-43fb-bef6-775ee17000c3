#!/usr/bin/env python3
"""
Test the thread access fix - using 'id' instead of 'thread_id' column
"""

import requests
import json
import uuid

def test_thread_access_fix():
    """Test that the thread access fix works"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Thread Access Fix (id vs thread_id column)")
    print("=" * 65)
    
    # Use the thread ID from the backend logs
    test_thread_id = "051a08d7-38e7-43eb-a320-a80adbd69b2c"
    
    # Test 1: Get thread endpoint
    print(f"\n1. Testing GET Thread Endpoint for {test_thread_id}...")
    
    try:
        response = requests.get(
            f"{base_url}/api/thread/{test_thread_id}",
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ SUCCESS: Returns 401 (auth required) - no database error!")
        elif response.status_code == 500:
            print("   ❌ FAILED: Still returns 500 error")
            if "thread_id does not exist" in response.text.lower():
                print("   ❌ Still has 'thread_id column does not exist' error")
            elif "column" in response.text.lower():
                print("   ❌ Still has column-related error")
            print(f"   Full response: {response.text}")
        elif response.status_code == 404:
            print("   ✅ SUCCESS: Returns 404 (thread not found) - database query worked!")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    # Test 2: Message endpoint for the same thread
    print(f"\n2. Testing Message Endpoint for {test_thread_id}...")
    
    message_data = {
        "content": "Test message with thread access fix"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/thread/{test_thread_id}/messages",
            json=message_data,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ SUCCESS: Returns 401 (auth required) - no database error!")
        elif response.status_code == 500:
            print("   ❌ FAILED: Still returns 500 error")
            if "thread_id does not exist" in response.text.lower():
                print("   ❌ Still has 'thread_id column does not exist' error")
            elif "column" in response.text.lower():
                print("   ❌ Still has column-related error")
            print(f"   Full response: {response.text}")
        elif response.status_code == 404:
            print("   ✅ SUCCESS: Returns 404 (thread not found) - database query worked!")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    print("\n" + "=" * 65)
    print("🎯 FINAL SUMMARY:")
    print("If both tests show 401/404 instead of 500 (database error),")
    print("then the thread access schema issue has been resolved!")
    print("The frontend should now be able to access threads successfully.")

if __name__ == "__main__":
    test_thread_access_fix()
