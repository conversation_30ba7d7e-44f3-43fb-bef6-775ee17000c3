# Supabase Database Setup Guide for Suna

## Overview
You need to apply 33 migration files to set up your Supabase database. I'll guide you through this step by step.

## Step 1: Access Supabase SQL Editor

1. Go to: https://supabase.com/dashboard/project/pldcxtmyivlpueddnuml
2. Click "SQL Editor" in the left sidebar
3. Click "New query"

## Step 2: Apply Migrations in Order

### Migration 1: Basejump Setup
**File:** `20240414161707_basejump-setup.sql`

Copy and paste this SQL into the editor and run it:

```sql
-- Basejump Setup Migration
ALTER DEFAULT PRIVILEGES REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA PUBLIC REVOKE EXECUTE ON FUNCTIONS FROM anon, authenticated;

-- Create basejump schema
CREATE SCHEMA IF NOT EXISTS basejump;
GRANT USAGE ON SCHEMA basejump to authenticated;
GRANT USAGE ON SCHEMA basejump to service_role;

-- Create invitation type enum
DO $$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_type t JOIN pg_namespace n ON n.oid = t.typnamespace 
                  WHERE t.typname = 'invitation_type' AND n.nspname = 'basejump') THEN
        CREATE TYPE basejump.invitation_type AS ENUM ('one_time', '24_hour');
    end if;
end; $$;

-- Create config table
CREATE TABLE IF NOT EXISTS basejump.config (
    enable_team_accounts            boolean default true,
    enable_personal_account_billing boolean default true,
    enable_team_account_billing     boolean default true,
    billing_provider                text    default 'stripe'
);

-- Insert config row
INSERT INTO basejump.config (enable_team_accounts, enable_personal_account_billing, enable_team_account_billing)
VALUES (true, true, true) ON CONFLICT DO NOTHING;

-- Grant permissions
GRANT SELECT ON basejump.config TO authenticated, service_role;

-- Enable RLS
ALTER TABLE basejump.config ENABLE ROW LEVEL SECURITY;

-- Create policy
CREATE POLICY "Basejump settings can be read by authenticated users" ON basejump.config
    FOR SELECT TO authenticated USING (true);

-- Utility functions
CREATE OR REPLACE FUNCTION basejump.get_config() RETURNS json AS $$
DECLARE result RECORD;
BEGIN
    SELECT * from basejump.config limit 1 into result;
    return row_to_json(result);
END; $$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION basejump.get_config() TO authenticated, service_role;

CREATE OR REPLACE FUNCTION basejump.is_set(field_name text) RETURNS boolean AS $$
DECLARE result BOOLEAN;
BEGIN
    execute format('select %I from basejump.config limit 1', field_name) into result;
    return result;
END; $$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION basejump.is_set(text) TO authenticated;

-- Timestamp triggers
CREATE OR REPLACE FUNCTION basejump.trigger_set_timestamps() RETURNS TRIGGER AS $$
BEGIN
    if TG_OP = 'INSERT' then
        NEW.created_at = now();
        NEW.updated_at = now();
    else
        NEW.updated_at = now();
        NEW.created_at = OLD.created_at;
    end if;
    RETURN NEW;
END $$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION basejump.trigger_set_user_tracking() RETURNS TRIGGER AS $$
BEGIN
    if TG_OP = 'INSERT' then
        NEW.created_by = auth.uid();
        NEW.updated_by = auth.uid();
    else
        NEW.updated_by = auth.uid();
        NEW.created_by = OLD.created_by;
    end if;
    RETURN NEW;
END $$ LANGUAGE plpgsql;

-- Token generation
CREATE OR REPLACE FUNCTION basejump.generate_token(length int) RETURNS text AS $$
select regexp_replace(replace(
    replace(replace(replace(encode(gen_random_bytes(length)::bytea, 'base64'), '/', ''), '+', ''), '\', ''),
    '=', ''), E'[\\n\\r]+', '', 'g');
$$ LANGUAGE sql;

GRANT EXECUTE ON FUNCTION basejump.generate_token(int) TO authenticated;
```

**✅ Run this first, then continue to Migration 2**

### Migration 2: Basejump Accounts
**File:** `20240414161947_basejump-accounts.sql`

This migration is quite large. I'll provide you with a simplified version that includes the essential parts:

```sql
-- Account role enum
DO $$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_type t JOIN pg_namespace n ON n.oid = t.typnamespace 
                  WHERE t.typname = 'account_role' AND n.nspname = 'basejump') THEN
        CREATE TYPE basejump.account_role AS ENUM ('owner', 'member');
    end if;
end; $$;

-- Accounts table
CREATE TABLE IF NOT EXISTS basejump.accounts (
    id                    uuid unique                NOT NULL DEFAULT extensions.uuid_generate_v4(),
    primary_owner_user_id uuid references auth.users not null default auth.uid(),
    name                  text,
    slug                  text unique,
    personal_account      boolean                             default false not null,
    updated_at            timestamp with time zone,
    created_at            timestamp with time zone,
    created_by            uuid references auth.users,
    updated_by            uuid references auth.users,
    private_metadata      jsonb                               default '{}'::jsonb,
    public_metadata       jsonb                               default '{}'::jsonb,
    PRIMARY KEY (id)
);

-- Add constraint for slug
ALTER TABLE basejump.accounts
    ADD CONSTRAINT basejump_accounts_slug_null_if_personal_account_true CHECK (
        (personal_account = true AND slug is null) OR (personal_account = false AND slug is not null)
    );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE basejump.accounts TO authenticated, service_role;
```

**✅ Run this second**

## Step 3: Continue with Remaining Migrations

Due to the complexity and length of all migrations, I recommend using the automated approach instead. 

Would you like me to:
1. **Continue with manual setup** (will require many more steps)
2. **Switch to automated approach** using a script I can create
3. **Help you install Supabase CLI** to apply all migrations at once

## Quick Status Check

After running the first two migrations, you can test if the basic setup is working by refreshing your Suna frontend at http://localhost:3000 and checking if the errors have reduced.

## Next Steps

Let me know which approach you'd prefer, and I'll guide you through the rest of the setup!
