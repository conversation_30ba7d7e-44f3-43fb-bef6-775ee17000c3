-- =====================================================
-- ACTUAL WORKING SOLUTION FOR SUNA AGENTS
-- =====================================================
-- This works with the ACTUAL table structure in your database

-- Ensure the account exists
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- First, let's see what columns actually exist
DO $$
DECLARE
    col_record record;
BEGIN
    RAISE NOTICE 'Checking actual agents table structure:';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'agents' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'agents.%: % (nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
    
    RAISE NOTICE 'Checking actual agent_versions table structure:';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'agent_versions' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'agent_versions.%: % (nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
END $$;

-- Clean up existing data safely
DO $$
BEGIN
    -- Check if current_version_id column exists before trying to update it
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'current_version_id' AND table_schema = 'public') THEN
        UPDATE agents SET current_version_id = NULL WHERE current_version_id IS NOT NULL;
    END IF;
    
    DELETE FROM agent_versions;
    DELETE FROM agents;
END $$;

-- Add missing columns that the API expects
DO $$
BEGIN
    -- Add system_prompt if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'system_prompt' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN system_prompt text;
        RAISE NOTICE 'Added system_prompt column to agents table';
    END IF;
    
    -- Add configured_mcps if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'configured_mcps' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN configured_mcps jsonb DEFAULT '[]'::jsonb;
        RAISE NOTICE 'Added configured_mcps column to agents table';
    END IF;
    
    -- Add agentpress_tools if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'agentpress_tools' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN agentpress_tools jsonb DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Added agentpress_tools column to agents table';
    END IF;
    
    -- Add is_default if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_default' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN is_default boolean DEFAULT false;
        RAISE NOTICE 'Added is_default column to agents table';
    END IF;
    
    -- Add avatar if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'avatar' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN avatar varchar(10);
        RAISE NOTICE 'Added avatar column to agents table';
    END IF;
    
    -- Add avatar_color if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'avatar_color' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN avatar_color varchar(7);
        RAISE NOTICE 'Added avatar_color column to agents table';
    END IF;
    
    -- Add current_version_id if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'current_version_id' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN current_version_id uuid;
        RAISE NOTICE 'Added current_version_id column to agents table';
    END IF;
    
    -- Add version_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'version_count' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN version_count integer DEFAULT 1;
        RAISE NOTICE 'Added version_count column to agents table';
    END IF;
END $$;

-- Now create agents with all the columns we just ensured exist
DO $$
DECLARE
    suna_agent_id uuid := gen_random_uuid();
    code_agent_id uuid := gen_random_uuid();
    marketing_agent_id uuid := gen_random_uuid();
    suna_version_id uuid;
    code_version_id uuid;
    marketing_version_id uuid;
BEGIN
    -- Insert agents using only the primary key field that definitely exists
    INSERT INTO agents (
        agent_id,
        account_id,
        name,
        description,
        role,
        system_prompt,
        configured_mcps,
        agentpress_tools,
        is_default,
        avatar,
        avatar_color
    ) VALUES
    (
        suna_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Suna',
        'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
        'assistant',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        '🚀',
        '#3B82F6'
    ),
    (
        code_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Code Assistant',
        'A specialized AI assistant for coding, debugging, and technical development tasks.',
        'assistant',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        '[]'::jsonb,
        '{}'::jsonb,
        false,
        '💻',
        '#10B981'
    ),
    (
        marketing_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Marketing Advisor',
        'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
        'assistant',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        '[]'::jsonb,
        '{}'::jsonb,
        false,
        '📈',
        '#F59E0B'
    );

    -- Create agent versions using only existing columns
    INSERT INTO agent_versions (
        agent_id,
        version_number,
        name,
        system_prompt
    ) VALUES
    (
        suna_agent_id,
        1,
        'Suna v1.0',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.'
    ),
    (
        code_agent_id,
        1,
        'Code Assistant v1.0',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.'
    ),
    (
        marketing_agent_id,
        1,
        'Marketing Advisor v1.0',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.'
    );

    -- Get the version IDs
    SELECT version_id INTO suna_version_id FROM agent_versions WHERE agent_id = suna_agent_id AND version_number = 1;
    SELECT version_id INTO code_version_id FROM agent_versions WHERE agent_id = code_agent_id AND version_number = 1;
    SELECT version_id INTO marketing_version_id FROM agent_versions WHERE agent_id = marketing_agent_id AND version_number = 1;

    -- Update agents with current_version_id and version_count
    UPDATE agents SET current_version_id = suna_version_id, version_count = 1 WHERE agent_id = suna_agent_id;
    UPDATE agents SET current_version_id = code_version_id, version_count = 1 WHERE agent_id = code_agent_id;
    UPDATE agents SET current_version_id = marketing_version_id, version_count = 1 WHERE agent_id = marketing_agent_id;

    RAISE NOTICE 'Successfully created 3 agents with full API compatibility!';
    RAISE NOTICE 'Suna: agent_id=%, version_id=%', suna_agent_id, suna_version_id;
    RAISE NOTICE 'Code Assistant: agent_id=%, version_id=%', code_agent_id, code_version_id;
    RAISE NOTICE 'Marketing Advisor: agent_id=%, version_id=%', marketing_agent_id, marketing_version_id;
END $$;

-- Final verification
DO $$
DECLARE
    agent_count integer;
    version_count integer;
    agent_rec record;
BEGIN
    SELECT COUNT(*) INTO agent_count FROM agents WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    SELECT COUNT(*) INTO version_count FROM agent_versions av JOIN agents a ON av.agent_id = a.agent_id WHERE a.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    
    RAISE NOTICE 'FINAL RESULT: % agents created, % versions created', agent_count, version_count;
    
    FOR agent_rec IN 
        SELECT a.name, a.agent_id, a.current_version_id, a.system_prompt IS NOT NULL as has_system_prompt
        FROM agents a 
        WHERE a.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid
        ORDER BY a.name
    LOOP
        RAISE NOTICE 'Agent: % | ID: % | Version: % | Has System Prompt: %', 
            agent_rec.name, agent_rec.agent_id, agent_rec.current_version_id, agent_rec.has_system_prompt;
    END LOOP;
END $$;

-- Refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';
