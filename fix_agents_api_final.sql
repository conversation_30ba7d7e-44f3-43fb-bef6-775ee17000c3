-- FINAL FIX FOR AGENTS API COMPATIBILITY
-- This ensures agents have all required fields for the API to work

-- First, ensure the account exists in basejump.accounts
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Clean up any existing broken agents first
DELETE FROM public.agent_versions WHERE agent_id IN (
    SELECT agent_id FROM public.agents WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid
);
DELETE FROM public.agents WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;

-- Ensure all required columns exist in projects table
DO $$
BEGIN
    -- Add project_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN project_id uuid DEFAULT extensions.uuid_generate_v4();
        UPDATE public.projects SET project_id = id WHERE project_id IS NULL;
    END IF;
    
    -- Ensure account_id exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
    END IF;
    
    -- Ensure other required columns exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'status' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN status text DEFAULT 'active';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'metadata' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- Ensure threads table has all required columns
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Update existing data to use the correct account_id with proper UUID casting
UPDATE public.projects SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid WHERE account_id IS NULL;
UPDATE public.threads SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid WHERE account_id IS NULL;
UPDATE public.messages SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid WHERE account_id IS NULL;

-- Create sample data with the correct account_id and proper UUID casting
INSERT INTO public.projects (account_id, name, description, status)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Welcome Project',
    'Your first project to get started with Suna',
    'active'
) ON CONFLICT DO NOTHING;

-- Create sample threads
INSERT INTO public.threads (account_id, project_id, title, status)
SELECT 
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    p.id,
    'Welcome Chat',
    'active'
FROM public.projects p
WHERE p.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid
AND NOT EXISTS (
    SELECT 1 FROM public.threads t 
    WHERE t.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid AND t.project_id = p.id
);

-- Create agents with ALL required fields for API compatibility
DO $$
DECLARE
    suna_agent_id uuid := gen_random_uuid();
    code_agent_id uuid := gen_random_uuid();
    marketing_agent_id uuid := gen_random_uuid();
    suna_version_id uuid;
    code_version_id uuid;
    marketing_version_id uuid;
BEGIN
    -- Insert agents with all required fields
    INSERT INTO public.agents (
        agent_id, 
        account_id, 
        name, 
        description, 
        role,
        system_prompt,
        configured_mcps,
        custom_mcps,
        agentpress_tools,
        is_default,
        is_public,
        tags,
        created_at,
        updated_at
    ) VALUES 
    (
        suna_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Suna',
        'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
        'assistant',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        false,
        '["business", "strategy", "startup"]'::jsonb,
        now(),
        now()
    ),
    (
        code_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Code Assistant',
        'A specialized AI assistant for coding, debugging, and technical development tasks.',
        'assistant',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        false,
        false,
        '["coding", "programming", "development"]'::jsonb,
        now(),
        now()
    ),
    (
        marketing_agent_id,
        'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
        'Marketing Advisor',
        'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
        'assistant',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        false,
        false,
        '["marketing", "growth", "content"]'::jsonb,
        now(),
        now()
    );

    -- Create agent versions for each agent
    INSERT INTO public.agent_versions (
        agent_id, 
        version_number, 
        version_name, 
        system_prompt,
        configured_mcps,
        custom_mcps,
        agentpress_tools,
        is_active,
        created_at,
        updated_at
    ) VALUES 
    (
        suna_agent_id,
        1,
        'v1.0',
        'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        now(),
        now()
    ),
    (
        code_agent_id,
        1,
        'v1.0',
        'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        now(),
        now()
    ),
    (
        marketing_agent_id,
        1,
        'v1.0',
        'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        true,
        now(),
        now()
    )
    RETURNING version_id INTO suna_version_id;

    -- Get the version IDs for updating current_version_id
    SELECT version_id INTO suna_version_id FROM public.agent_versions WHERE agent_id = suna_agent_id AND version_number = 1;
    SELECT version_id INTO code_version_id FROM public.agent_versions WHERE agent_id = code_agent_id AND version_number = 1;
    SELECT version_id INTO marketing_version_id FROM public.agent_versions WHERE agent_id = marketing_agent_id AND version_number = 1;

    -- Update agents to reference their current versions
    UPDATE public.agents SET current_version_id = suna_version_id WHERE agent_id = suna_agent_id;
    UPDATE public.agents SET current_version_id = code_version_id WHERE agent_id = code_agent_id;
    UPDATE public.agents SET current_version_id = marketing_version_id WHERE agent_id = marketing_agent_id;

    RAISE NOTICE 'Created 3 agents with proper API compatibility';
END $$;

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';
