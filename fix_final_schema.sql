-- FINAL SCHEMA FIX FOR SUNA
-- This ensures all required columns exist and creates sample data

-- Refresh the schema cache first
NOTIFY pgrst, 'reload schema';

-- Ensure all required columns exist in projects table
DO $$
BEGIN
    -- Add project_id column if it doesn't exist (this might be needed for some queries)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN project_id uuid DEFAULT extensions.uuid_generate_v4();
        UPDATE public.projects SET project_id = id WHERE project_id IS NULL;
    END IF;
    
    -- Ensure account_id exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        UPDATE public.projects SET account_id = (SELECT id FROM basejump.accounts LIMIT 1) WHERE account_id IS NULL;
    END IF;
    
    -- Ensure other required columns exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'status' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN status text DEFAULT 'active';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'metadata' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- Ensure threads table has all required columns
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        UPDATE public.threads SET account_id = (SELECT id FROM basejump.accounts LIMIT 1) WHERE account_id IS NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE;
        UPDATE public.threads SET project_id = (SELECT id FROM public.projects LIMIT 1) WHERE project_id IS NULL;
    END IF;
END $$;

-- Ensure agents table has all required columns
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        UPDATE public.agents SET account_id = (SELECT id FROM basejump.accounts LIMIT 1) WHERE account_id IS NULL;
    END IF;
END $$;

-- Create sample data if none exists
INSERT INTO public.projects (account_id, name, description, status)
SELECT 
    id,
    'Welcome Project',
    'Your first project to get started with Suna',
    'active'
FROM basejump.accounts
WHERE id NOT IN (SELECT DISTINCT account_id FROM public.projects WHERE account_id IS NOT NULL)
ON CONFLICT DO NOTHING;

-- Create sample threads
INSERT INTO public.threads (account_id, project_id, title, status)
SELECT 
    a.id,
    p.id,
    'Welcome Chat',
    'active'
FROM basejump.accounts a
JOIN public.projects p ON p.account_id = a.id
WHERE NOT EXISTS (
    SELECT 1 FROM public.threads t 
    WHERE t.account_id = a.id AND t.project_id = p.id
)
ON CONFLICT DO NOTHING;

-- Create sample agents
INSERT INTO public.agents (account_id, name, description, instructions, created_by, updated_by)
SELECT 
    a.id,
    'Suna',
    'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
    'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
    a.primary_owner_user_id,
    a.primary_owner_user_id
FROM basejump.accounts a
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents ag WHERE ag.name = 'Suna' AND ag.account_id = a.id
)
UNION ALL
SELECT 
    a.id,
    'Code Assistant',
    'A specialized AI assistant for coding, debugging, and technical development tasks.',
    'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
    a.primary_owner_user_id,
    a.primary_owner_user_id
FROM basejump.accounts a
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents ag WHERE ag.name = 'Code Assistant' AND ag.account_id = a.id
)
UNION ALL
SELECT 
    a.id,
    'Marketing Advisor',
    'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
    'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
    a.primary_owner_user_id,
    a.primary_owner_user_id
FROM basejump.accounts a
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents ag WHERE ag.name = 'Marketing Advisor' AND ag.account_id = a.id
);

-- Create agent versions for the new agents
INSERT INTO public.agent_versions (agent_id, version_number, name, description, instructions, system_prompt)
SELECT 
    ag.id,
    1,
    ag.name,
    ag.description,
    ag.instructions,
    ag.instructions
FROM public.agents ag
WHERE ag.id NOT IN (SELECT DISTINCT agent_id FROM public.agent_versions WHERE agent_id IS NOT NULL);

-- Update agents to reference their current versions
UPDATE public.agents 
SET current_version_id = (
    SELECT av.id FROM public.agent_versions av
    WHERE av.agent_id = public.agents.id 
    AND av.version_number = 1 
    LIMIT 1
)
WHERE current_version_id IS NULL;

-- Refresh schema cache again to ensure all changes are recognized
NOTIFY pgrst, 'reload schema';
