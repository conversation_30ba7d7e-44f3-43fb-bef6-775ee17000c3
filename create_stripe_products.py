# -*- coding: utf-8 -*-
import stripe

# ⚠️ SECURITY: Replace with your NEW Stripe Secret Key after revoking the exposed one
stripe.api_key = 'YOUR_NEW_STRIPE_SECRET_KEY_HERE'

products = [
    # Monthly Plans
    {
        "name": "Free - Monthly",
        "description": "$5 free AI tokens included\nPublic projects\nBasic Models\nCommunity support",
        "price": 0,
        "interval": "month"
    },
    {
        "name": "Plus - Monthly",
        "description": "$20 AI token credits per month\nPrivate projects\nPremium AI Models\nCommunity support",
        "price": 2000,
        "interval": "month"
    },
    {
        "name": "Pro - Monthly",
        "description": "$50 AI token credits per month\nPrivate projects\nPremium AI Models\nCommunity support",
        "price": 5000,
        "interval": "month"
    },
    {
        "name": "Ultra - Monthly",
        "description": "$200 AI token credits per month\nPrivate projects\nPremium AI Models\nPriority support",
        "price": 20000,
        "interval": "month"
    },
    # Annual Plans
    {
        "name": "Free - Annual",
        "description": "$5 free AI tokens included\nPublic projects\nBasic Models\nCommunity support",
        "price": 0,
        "interval": "year"
    },
    {
        "name": "Plus - Annual",
        "description": "$20/month billed yearly\n$20 AI token credits per month\nPrivate projects\nPremium AI Models\nCommunity support",
        "price": 20400,  # $17/month x 12
        "interval": "year"
    },
    {
        "name": "Pro - Annual",
        "description": "$50/month billed yearly\n$50 AI token credits per month\nPrivate projects\nPremium AI Models\nCommunity support",
        "price": 51600,  # $43/month x 12
        "interval": "year"
    },
    {
        "name": "Ultra - Annual",
        "description": "$200/month billed yearly\n$200 AI token credits per month\nPrivate projects\nPremium AI Models\nPriority support",
        "price": 204000,  # $170/month x 12
        "interval": "year"
    },
]

print("🚀 Creating Stripe products...")
print("⚠️  WARNING: Using LIVE Stripe key - real products will be created!")
print()

created_products = []

for plan in products:
    try:
        # Create Product
        product = stripe.Product.create(
            name=plan["name"],
            description=plan["description"],
            metadata={
                "plan_type": plan["name"].split(" - ")[0].lower(),
                "billing_cycle": plan["interval"],
                "ai_credits": plan["description"].split("$")[1].split(" ")[0] if "$" in plan["description"] else "0"
            }
        )

        # Create Price object associated with the Product
        if plan["price"] == 0:
            # For free plans, don't set recurring
            price = stripe.Price.create(
                unit_amount=plan["price"],
                currency="usd",
                product=product.id,
                metadata={
                    "plan_type": plan["name"].split(" - ")[0].lower(),
                    "billing_cycle": plan["interval"]
                }
            )
        else:
            # For paid plans, set recurring
            price = stripe.Price.create(
                unit_amount=plan["price"],
                currency="usd",
                recurring={"interval": plan["interval"]},
                product=product.id,
                metadata={
                    "plan_type": plan["name"].split(" - ")[0].lower(),
                    "billing_cycle": plan["interval"]
                }
            )

        created_products.append({
            "name": plan["name"],
            "product_id": product.id,
            "price_id": price.id,
            "amount": plan["price"]
        })

        print(f"✅ Created: {plan['name']}")
        print(f"   Product ID: {product.id}")
        print(f"   Price ID: {price.id}")
        print(f"   Amount: ${plan['price']/100:.2f}")
        print()

    except stripe.error.StripeError as e:
        print(f"❌ Error creating {plan['name']}: {e}")
        continue

print(f"🎉 Successfully created {len(created_products)} products!")
print("\n📋 Price IDs for your environment variables:")
print("-" * 50)

for product in created_products:
    env_name = f"STRIPE_PRICE_{product['name'].upper().replace(' - ', '_').replace(' ', '_')}"
    print(f"{env_name}={product['price_id']}")

print("\n💡 Add these to your .env file or Netlify environment variables!")
