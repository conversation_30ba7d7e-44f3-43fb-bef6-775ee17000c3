#!/usr/bin/env python3
"""
Direct LLM test - bypasses authentication to test model functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
from services.llm import make_llm_api_call
from utils.config import config

async def test_llm_direct():
    """Test LLM functionality directly"""
    print("🧪 Direct LLM Test - GROQ → Gemini Priority")
    print("=" * 50)
    
    # Test 1: Test GROQ model directly
    print("\n1. Testing GROQ Model Directly...")
    try:
        response = await make_llm_api_call(
            model="groq/llama-3.3-70b-versatile",
            messages=[{"role": "user", "content": "Hello! Please respond with exactly: 'GROQ is working!' and nothing else."}],
            max_tokens=50,
            temperature=0.1
        )
        print(f"   ✅ GROQ Response: {response}")
    except Exception as e:
        print(f"   ❌ GROQ Error: {str(e)}")
    
    # Test 2: Test Gemini model directly
    print("\n2. Testing Gemini Model Directly...")
    try:
        response = await make_llm_api_call(
            model="gemini/gemini-1.5-flash",
            messages=[{"role": "user", "content": "Hello! Please respond with exactly: 'Gemini is working!' and nothing else."}],
            max_tokens=50,
            temperature=0.1
        )
        print(f"   ✅ Gemini Response: {response}")
    except Exception as e:
        print(f"   ❌ Gemini Error: {str(e)}")
    
    # Test 3: Test default model (should be GROQ)
    print("\n3. Testing Default Model...")
    try:
        default_model = config.MODEL_TO_USE
        print(f"   Default model configured: {default_model}")
        
        response = await make_llm_api_call(
            model=default_model,
            messages=[{"role": "user", "content": "Hello! Please respond with exactly: 'Default model working!' and nothing else."}],
            max_tokens=50,
            temperature=0.1
        )
        print(f"   ✅ Default Model Response: {response}")
    except Exception as e:
        print(f"   ❌ Default Model Error: {str(e)}")
    
    # Test 4: Test model aliases
    print("\n4. Testing Model Aliases...")
    from utils.constants import MODEL_NAME_ALIASES
    
    print(f"   Available aliases: {list(MODEL_NAME_ALIASES.keys())}")
    
    # Test GROQ alias
    if 'groq-llama-3.3-70b' in MODEL_NAME_ALIASES:
        try:
            resolved_model = MODEL_NAME_ALIASES['groq-llama-3.3-70b']
            print(f"   GROQ alias 'groq-llama-3.3-70b' resolves to: {resolved_model}")
            
            response = await make_llm_api_call(
                model=resolved_model,
                messages=[{"role": "user", "content": "Hello! Please respond with exactly: 'GROQ alias working!' and nothing else."}],
                max_tokens=50,
                temperature=0.1
            )
            print(f"   ✅ GROQ Alias Response: {response}")
        except Exception as e:
            print(f"   ❌ GROQ Alias Error: {str(e)}")
    
    # Test Gemini alias
    if 'gemini-flash' in MODEL_NAME_ALIASES:
        try:
            resolved_model = MODEL_NAME_ALIASES['gemini-flash']
            print(f"   Gemini alias 'gemini-flash' resolves to: {resolved_model}")
            
            response = await make_llm_api_call(
                model=resolved_model,
                messages=[{"role": "user", "content": "Hello! Please respond with exactly: 'Gemini alias working!' and nothing else."}],
                max_tokens=50,
                temperature=0.1
            )
            print(f"   ✅ Gemini Alias Response: {response}")
        except Exception as e:
            print(f"   ❌ Gemini Alias Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 Direct LLM Test Complete!")

if __name__ == "__main__":
    asyncio.run(test_llm_direct())
