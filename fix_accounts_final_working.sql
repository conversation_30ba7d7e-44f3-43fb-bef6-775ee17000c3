-- FINAL WORKING DATABASE FIX FOR ACCOUNTS
-- This handles the agent_id NOT NULL constraint properly

-- First, ensure the account exists in basejump.accounts
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

-- Ensure all required columns exist in projects table
DO $$
BEGIN
    -- Add project_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN project_id uuid DEFAULT extensions.uuid_generate_v4();
        UPDATE public.projects SET project_id = id WHERE project_id IS NULL;
    END IF;
    
    -- Ensure account_id exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
    END IF;
    
    -- Ensure other required columns exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'status' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN status text DEFAULT 'active';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'metadata' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- Ensure threads table has all required columns
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Update existing data to use the correct account_id with proper UUID casting
UPDATE public.projects SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid WHERE account_id IS NULL;
UPDATE public.threads SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid WHERE account_id IS NULL;
UPDATE public.agents SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid WHERE account_id IS NULL;
UPDATE public.messages SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid WHERE account_id IS NULL;

-- Create sample data with the correct account_id and proper UUID casting
INSERT INTO public.projects (account_id, name, description, status)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Welcome Project',
    'Your first project to get started with Suna',
    'active'
) ON CONFLICT DO NOTHING;

-- Create sample threads
INSERT INTO public.threads (account_id, project_id, title, status)
SELECT 
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    p.id,
    'Welcome Chat',
    'active'
FROM public.projects p
WHERE p.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid
AND NOT EXISTS (
    SELECT 1 FROM public.threads t 
    WHERE t.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid AND t.project_id = p.id
);

-- Create sample agents with EXPLICIT agent_id values
DO $$
DECLARE
    has_system_prompt boolean;
    has_instructions boolean;
    suna_agent_id uuid := gen_random_uuid();
    code_agent_id uuid := gen_random_uuid();
    marketing_agent_id uuid := gen_random_uuid();
BEGIN
    -- Check what prompt column exists in the agents table
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'agents' AND column_name = 'system_prompt' AND table_schema = 'public'
    ) INTO has_system_prompt;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'agents' AND column_name = 'instructions' AND table_schema = 'public'
    ) INTO has_instructions;
    
    -- Insert agents with explicit agent_id values and role
    IF has_system_prompt THEN
        -- Schema with system_prompt
        INSERT INTO public.agents (agent_id, account_id, name, description, system_prompt, role)
        VALUES
        (
            suna_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Suna',
            'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
            'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
            'assistant'
        ),
        (
            code_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Code Assistant',
            'A specialized AI assistant for coding, debugging, and technical development tasks.',
            'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
            'assistant'
        ),
        (
            marketing_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Marketing Advisor',
            'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
            'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
            'assistant'
        )
        ON CONFLICT (agent_id) DO NOTHING;
    ELSIF has_instructions THEN
        -- Schema with instructions
        INSERT INTO public.agents (agent_id, account_id, name, description, instructions, role)
        VALUES
        (
            suna_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Suna',
            'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
            'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
            'assistant'
        ),
        (
            code_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Code Assistant',
            'A specialized AI assistant for coding, debugging, and technical development tasks.',
            'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
            'assistant'
        ),
        (
            marketing_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Marketing Advisor',
            'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
            'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
            'assistant'
        )
        ON CONFLICT (agent_id) DO NOTHING;
    ELSE
        -- Fallback: just name, account_id, and role
        INSERT INTO public.agents (agent_id, account_id, name, description, role)
        VALUES
        (
            suna_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Suna',
            'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
            'assistant'
        ),
        (
            code_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Code Assistant',
            'A specialized AI assistant for coding, debugging, and technical development tasks.',
            'assistant'
        ),
        (
            marketing_agent_id,
            'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
            'Marketing Advisor',
            'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
            'assistant'
        )
        ON CONFLICT (agent_id) DO NOTHING;
    END IF;
    
    RAISE NOTICE 'Agents created with explicit agent_id values. System prompt: %, Instructions: %', has_system_prompt, has_instructions;
END $$;

-- Refresh schema cache again
NOTIFY pgrst, 'reload schema';
