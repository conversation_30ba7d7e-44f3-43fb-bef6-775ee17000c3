-- FIX MISSING AGENT_VERSIONS TABLE (SAFE VERSION)
-- Copy and paste this into your Supabase SQL Editor

-- Create agent_versions table
CREATE TABLE IF NOT EXISTS public.agent_versions (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    agent_id uuid REFERENCES public.agents(id) ON DELETE CASCADE,
    version_number integer NOT NULL DEFAULT 1,
    name text NOT NULL,
    description text,
    instructions text,
    system_prompt text,
    metadata jsonb DEFAULT '{}'::jsonb,
    is_current boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users,
    UNIQUE(agent_id, version_number)
);

-- Grant permissions
GRANT ALL ON TABLE public.agent_versions TO authenticated, service_role;
ALTER TABLE public.agent_versions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies first to avoid conflicts
DROP POLICY IF EXISTS "Users can view agent versions in their accounts" ON public.agent_versions;
DROP POLICY IF EXISTS "Users can insert agent versions in their accounts" ON public.agent_versions;
DROP POLICY IF EXISTS "Users can update agent versions in their accounts" ON public.agent_versions;
DROP POLICY IF EXISTS "Users can delete agent versions in their accounts" ON public.agent_versions;

-- Create policies for agent_versions
CREATE POLICY "Users can view agent versions in their accounts" ON public.agent_versions
    FOR SELECT TO authenticated USING (
        agent_id IN (
            SELECT id FROM public.agents 
            WHERE account_id IN (
                SELECT id FROM basejump.accounts 
                WHERE primary_owner_user_id = auth.uid()
                   OR id IN (
                       SELECT account_id FROM basejump.account_user 
                       WHERE user_id = auth.uid()
                   )
            )
        )
    );

CREATE POLICY "Users can insert agent versions in their accounts" ON public.agent_versions
    FOR INSERT TO authenticated WITH CHECK (
        agent_id IN (
            SELECT id FROM public.agents 
            WHERE account_id IN (
                SELECT id FROM basejump.accounts 
                WHERE primary_owner_user_id = auth.uid()
                   OR id IN (
                       SELECT account_id FROM basejump.account_user 
                       WHERE user_id = auth.uid()
                   )
            )
        )
    );

CREATE POLICY "Users can update agent versions in their accounts" ON public.agent_versions
    FOR UPDATE TO authenticated USING (
        agent_id IN (
            SELECT id FROM public.agents 
            WHERE account_id IN (
                SELECT id FROM basejump.accounts 
                WHERE primary_owner_user_id = auth.uid()
                   OR id IN (
                       SELECT account_id FROM basejump.account_user 
                       WHERE user_id = auth.uid()
                   )
            )
        )
    );

CREATE POLICY "Users can delete agent versions in their accounts" ON public.agent_versions
    FOR DELETE TO authenticated USING (
        agent_id IN (
            SELECT id FROM public.agents 
            WHERE account_id IN (
                SELECT id FROM basejump.accounts 
                WHERE primary_owner_user_id = auth.uid()
                   OR id IN (
                       SELECT account_id FROM basejump.account_user 
                       WHERE user_id = auth.uid()
                   )
            )
        )
    );

-- Add current_version_id column to agents table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'current_version_id' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN current_version_id uuid REFERENCES public.agent_versions(id);
    END IF;
END $$;

-- Apply timestamp triggers
DROP TRIGGER IF EXISTS set_timestamps_agent_versions ON public.agent_versions;
CREATE TRIGGER set_timestamps_agent_versions BEFORE INSERT OR UPDATE ON public.agent_versions
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

-- Create initial versions for existing agents
INSERT INTO public.agent_versions (agent_id, version_number, name, description, instructions, system_prompt)
SELECT 
    id,
    1,
    name,
    description,
    instructions,
    instructions
FROM public.agents
WHERE id NOT IN (SELECT DISTINCT agent_id FROM public.agent_versions WHERE agent_id IS NOT NULL)
ON CONFLICT DO NOTHING;

-- Update agents to reference their current versions
UPDATE public.agents 
SET current_version_id = (
    SELECT id FROM public.agent_versions 
    WHERE agent_id = public.agents.id 
    AND version_number = 1 
    LIMIT 1
)
WHERE current_version_id IS NULL;
