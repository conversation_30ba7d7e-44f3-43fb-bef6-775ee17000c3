-- FIX MISSING TABLES AND POLICIES
-- Copy and paste this into your Supabase SQL Editor

-- Fix projects table policies
DROP POLICY IF EXISTS "Users can view projects in their accounts" ON public.projects;
DROP POLICY IF EXISTS "Users can insert projects in their accounts" ON public.projects;
DROP POLICY IF EXISTS "Users can update projects in their accounts" ON public.projects;
DROP POLICY IF EXISTS "Users can delete projects in their accounts" ON public.projects;

-- Create comprehensive policies for projects
CREATE POLICY "Users can view projects in their accounts" ON public.projects
    FOR SELECT TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can insert projects in their accounts" ON public.projects
    FOR INSERT TO authenticated WITH CHECK (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can update projects in their accounts" ON public.projects
    FOR UPDATE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can delete projects in their accounts" ON public.projects
    FOR DELETE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

-- Fix threads table policies
DROP POLICY IF EXISTS "Users can view threads in their accounts" ON public.threads;
DROP POLICY IF EXISTS "Users can insert threads in their accounts" ON public.threads;
DROP POLICY IF EXISTS "Users can update threads in their accounts" ON public.threads;
DROP POLICY IF EXISTS "Users can delete threads in their accounts" ON public.threads;

-- Create comprehensive policies for threads
CREATE POLICY "Users can view threads in their accounts" ON public.threads
    FOR SELECT TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can insert threads in their accounts" ON public.threads
    FOR INSERT TO authenticated WITH CHECK (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can update threads in their accounts" ON public.threads
    FOR UPDATE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can delete threads in their accounts" ON public.threads
    FOR DELETE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

-- Add missing columns if they don't exist
DO $$ 
BEGIN
    -- Add missing columns to projects table
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'metadata') THEN
        ALTER TABLE public.projects ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add missing columns to threads table  
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'metadata') THEN
        ALTER TABLE public.threads ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'status') THEN
        ALTER TABLE public.threads ADD COLUMN status text DEFAULT 'active';
    END IF;
END $$;

-- Create messages table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.messages (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    thread_id uuid REFERENCES public.threads(id) ON DELETE CASCADE,
    account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    role text NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content text,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.messages TO authenticated, service_role;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Create policies for messages
CREATE POLICY "Users can view messages in their accounts" ON public.messages
    FOR SELECT TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can insert messages in their accounts" ON public.messages
    FOR INSERT TO authenticated WITH CHECK (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

-- Apply timestamp triggers to messages
CREATE TRIGGER set_timestamps_messages BEFORE INSERT OR UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

-- Create agents table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.agents (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    instructions text,
    is_default boolean DEFAULT false,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.agents TO authenticated, service_role;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;

-- Create policies for agents
CREATE POLICY "Users can view agents in their accounts" ON public.agents
    FOR SELECT TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can insert agents in their accounts" ON public.agents
    FOR INSERT TO authenticated WITH CHECK (
        account_id IN (
            SELECT id FROM basejump.accounts
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can update agents in their accounts" ON public.agents
    FOR UPDATE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can delete agents in their accounts" ON public.agents
    FOR DELETE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user
                   WHERE user_id = auth.uid()
               )
        )
    );

-- Apply timestamp triggers to agents
CREATE TRIGGER set_timestamps_agents BEFORE INSERT OR UPDATE ON public.agents
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

-- Insert default agents for existing accounts
INSERT INTO public.agents (account_id, name, description, instructions, is_default)
SELECT
    id,
    'Suna Assistant',
    'Your default AI assistant powered by Suna',
    'You are Suna, a helpful AI assistant. Be concise, accurate, and helpful in your responses.',
    true
FROM basejump.accounts
WHERE id NOT IN (SELECT DISTINCT account_id FROM public.agents WHERE is_default = true)
ON CONFLICT DO NOTHING;
