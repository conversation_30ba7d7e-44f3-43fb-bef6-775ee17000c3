#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Environment Switcher for AI Co-Founder Application

This script helps switch between local and production configurations
to fix the issue where new account creation redirects to localhost in production.

Usage:
    python switch_environment.py local      # Switch to local development
    python switch_environment.py production # Switch to production
    python switch_environment.py status     # Show current environment
"""

import os
import sys
import shutil
from pathlib import Path

def print_header():
    print("AI Co-Founder Environment Switcher")
    print("=" * 50)

def print_success(message):
    print("[SUCCESS] " + message)

def print_error(message):
    print("[ERROR] " + message)

def print_info(message):
    print("[INFO] " + message)

def backup_file(file_path):
    """Create a backup of the current file"""
    if os.path.exists(file_path):
        backup_path = file_path + ".backup"
        shutil.copy2(file_path, backup_path)
        print_info("Backed up " + file_path + " to " + backup_path)

def switch_to_local():
    """Switch to local development environment"""
    print_info("Switching to LOCAL environment...")
    
    # Frontend environment
    frontend_env_local = """# Generated by <PERSON><PERSON> install script

NEXT_PUBLIC_SUPABASE_URL=https://pldcxtmyivlpueddnuml.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZGN4dG15aXZscHVlZGRudW1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4NTY5NTQsImV4cCI6MjA2NjQzMjk1NH0.yrYWj-hbqdS3nGTFBA1YvhAtKoY8BOVY1_OXA4_gAtA
NEXT_PUBLIC_BACKEND_URL=http://127.0.0.1:8000/api
NEXT_PUBLIC_URL=http://localhost:3000
NEXT_PUBLIC_ENV_MODE=LOCAL
ADMIN_API_KEY=suna_admin_key_2025_secure_token_xyz789
"""
    
    # Write frontend environment
    frontend_env_path = "frontend/.env.local"
    backup_file(frontend_env_path)
    with open(frontend_env_path, 'w') as f:
        f.write(frontend_env_local)
    print_success("Updated " + frontend_env_path + " for LOCAL environment")
    
    # Update backend environment mode
    backend_env_path = "backend/.env"
    if os.path.exists(backend_env_path):
        backup_file(backend_env_path)
        with open(backend_env_path, 'r') as f:
            content = f.read()
        
        # Update ENV_MODE to local
        content = content.replace('ENV_MODE=production', 'ENV_MODE=local')
        content = content.replace('ENV_MODE=staging', 'ENV_MODE=local')
        
        with open(backend_env_path, 'w') as f:
            f.write(content)
        print_success(f"Updated {backend_env_path} for LOCAL environment")
    
    # Update Supabase config for local
    supabase_config_path = "backend/supabase/config.toml"
    if os.path.exists(supabase_config_path):
        backup_file(supabase_config_path)
        with open(supabase_config_path, 'r') as f:
            content = f.read()
        
        # Update site_url to localhost
        content = content.replace(
            'site_url = "https://ai-co-founder.netlify.app"',
            'site_url = "http://localhost:3000"'
        )
        
        with open(supabase_config_path, 'w') as f:
            f.write(content)
        print_success(f"Updated {supabase_config_path} for LOCAL environment")
    
    print_success("Successfully switched to LOCAL environment")
    print_info("Restart your development servers for changes to take effect")

def switch_to_production():
    """Switch to production environment"""
    print_info("Switching to PRODUCTION environment...")
    
    # Frontend environment
    frontend_env_production = """# Production Environment Variables
# This file contains the production configuration for the frontend

# REQUIRED - Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://pldcxtmyivlpueddnuml.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZGN4dG15aXZscHVlZGRudW1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4NTY5NTQsImV4cCI6MjA2NjQzMjk1NH0.yrYWj-hbqdS3nGTFBA1YvhAtKoY8BOVY1_OXA4_gAtA

# REQUIRED - Backend Configuration
NEXT_PUBLIC_BACKEND_URL=https://api.ai-co-founder.com/api

# REQUIRED - Site Configuration
NEXT_PUBLIC_URL=https://ai-co-founder.netlify.app

# REQUIRED - Environment Mode
NEXT_PUBLIC_ENV_MODE=PRODUCTION

# OPTIONAL - Admin Configuration
ADMIN_API_KEY=suna_admin_key_2025_secure_token_xyz789

# OPTIONAL - Performance Monitoring
NEXT_TELEMETRY_DISABLED=1

# OPTIONAL - Debug Mode (disabled in production)
NEXT_PUBLIC_DEBUG=false
DEBUG=false
"""
    
    # Write frontend environment
    frontend_env_path = "frontend/.env.local"
    backup_file(frontend_env_path)
    with open(frontend_env_path, 'w') as f:
        f.write(frontend_env_production)
    print_success(f"Updated {frontend_env_path} for PRODUCTION environment")
    
    # Update backend environment mode
    backend_env_path = "backend/.env"
    if os.path.exists(backend_env_path):
        backup_file(backend_env_path)
        with open(backend_env_path, 'r') as f:
            content = f.read()
        
        # Update ENV_MODE to production
        content = content.replace('ENV_MODE=local', 'ENV_MODE=production')
        content = content.replace('ENV_MODE=staging', 'ENV_MODE=production')
        
        with open(backend_env_path, 'w') as f:
            f.write(content)
        print_success(f"Updated {backend_env_path} for PRODUCTION environment")
    
    # Supabase config is already updated for production
    print_success("Supabase config already configured for production URLs")
    
    print_success("Successfully switched to PRODUCTION environment")
    print_info("Deploy your application for changes to take effect")

def show_status():
    """Show current environment status"""
    print_info("Current Environment Status:")
    print("-" * 30)
    
    # Check frontend environment
    frontend_env_path = "frontend/.env.local"
    if os.path.exists(frontend_env_path):
        with open(frontend_env_path, 'r') as f:
            content = f.read()
        
        if "NEXT_PUBLIC_ENV_MODE=LOCAL" in content:
            print(f"Frontend: LOCAL (localhost:3000)")
        elif "NEXT_PUBLIC_ENV_MODE=PRODUCTION" in content:
            print(f"Frontend: PRODUCTION (ai-co-founder.netlify.app)")
        else:
            print(f"Frontend: UNKNOWN")
    else:
        print(f"Frontend: No .env.local file found")
    
    # Check backend environment
    backend_env_path = "backend/.env"
    if os.path.exists(backend_env_path):
        with open(backend_env_path, 'r') as f:
            content = f.read()
        
        if "ENV_MODE=local" in content:
            print(f"Backend: LOCAL")
        elif "ENV_MODE=production" in content:
            print(f"Backend: PRODUCTION")
        else:
            print(f"Backend: UNKNOWN")
    else:
        print(f"Backend: No .env file found")
    
    # Check Supabase config
    supabase_config_path = "backend/supabase/config.toml"
    if os.path.exists(supabase_config_path):
        with open(supabase_config_path, 'r') as f:
            content = f.read()
        
        if 'site_url = "http://localhost:3000"' in content:
            print(f"Supabase: LOCAL (localhost:3000)")
        elif 'site_url = "https://ai-co-founder.netlify.app"' in content:
            print(f"Supabase: PRODUCTION (ai-co-founder.netlify.app)")
        else:
            print(f"Supabase: UNKNOWN")
    else:
        print(f"Supabase: No config.toml file found")

def main():
    print_header()
    
    if len(sys.argv) != 2:
        print_error("Usage: python switch_environment.py [local|production|status]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "local":
        switch_to_local()
    elif command == "production":
        switch_to_production()
    elif command == "status":
        show_status()
    else:
        print_error(f"Unknown command: {command}")
        print_error("Valid commands: local, production, status")
        sys.exit(1)

if __name__ == "__main__":
    main()
