#!/usr/bin/env python3
"""
Debug script to check project data in the database
"""
import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')

# Add the backend directory to the path
sys.path.append('backend')

from database.supabase import get_client

async def debug_project():
    """Debug project data"""
    
    # Use the project ID from the logs
    project_id = "4e78e1b7-2b13-4d26-87e1-54bd5fc2a73c"
    
    print(f"Debugging project: {project_id}")
    
    try:
        # Get the database client
        client = await get_client()
        print(f"✅ Got database client")
        
        # Try to get project data
        print(f"Looking up project {project_id} using 'id' column")
        project_result = await client.table('projects').select('*').eq('id', project_id).execute()
        
        print(f"Project lookup result: {project_result}")
        print(f"Project data: {project_result.data}")
        print(f"Project data length: {len(project_result.data) if project_result.data else 'None'}")
        
        if project_result.data and len(project_result.data) > 0:
            project_data = project_result.data[0]
            print(f"Retrieved project_data: {project_data}")
            print(f"Project data type: {type(project_data)}")
            
            # Check if sandbox column exists
            if 'sandbox' in project_data:
                print(f"✅ Sandbox column exists: {project_data['sandbox']}")
            else:
                print(f"❌ Sandbox column missing from project data")
                print(f"Available columns: {list(project_data.keys())}")
        else:
            print(f"❌ No project found with ID {project_id}")
            
            # Try to list all projects to see what's available
            all_projects = await client.table('projects').select('id, name').execute()
            print(f"Available projects: {all_projects.data}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_project())
