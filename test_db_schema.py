#!/usr/bin/env python3
"""
Quick test to check if the database schema is fixed
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
from services.supabase import DBConnection

async def test_db_schema():
    """Test if the is_llm_message column exists"""
    print("🔍 Testing Database Schema")
    print("=" * 40)
    
    try:
        db_conn = DBConnection()
        client = await db_conn.client
        
        # Try to select the is_llm_message column
        result = await client.table('messages').select('id, is_llm_message').limit(1).execute()
        
        print("✅ SUCCESS: is_llm_message column exists!")
        print("✅ Database schema is ready")
        print("\n🚀 You can now try sending a message in the Suna UI!")
        return True
        
    except Exception as e:
        error_msg = str(e).lower()
        if "column" in error_msg and ("does not exist" in error_msg or "not found" in error_msg):
            print("❌ FAILED: is_llm_message column is missing")
            print("\n💡 You need to apply the database migration:")
            print("1. Go to your Supabase dashboard")
            print("2. Open the SQL Editor")
            print("3. Run this SQL:")
            print("   ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;")
            return False
        else:
            print(f"❌ FAILED: Unexpected error: {str(e)}")
            return False

if __name__ == "__main__":
    asyncio.run(test_db_schema())
