#!/usr/bin/env python3
"""
Script to update agent names from "Suna" to "AICxO" in the database
"""

import os
import sys
from supabase import create_client, Client

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def update_agent_names():
    # Load environment variables
    from dotenv import load_dotenv

    # Try different paths for .env file
    env_paths = [
        'backend/.env',
        '.env',
        os.path.join(os.path.dirname(__file__), 'backend', '.env'),
        os.path.join(os.path.dirname(__file__), '.env')
    ]

    env_loaded = False
    for env_path in env_paths:
        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"📁 Loaded environment from: {env_path}")
            env_loaded = True
            break

    if not env_loaded:
        print("⚠️  No .env file found, trying environment variables...")
    
    # Initialize Supabase client
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not url or not key:
        print("Error: Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY")
        return False
    
    supabase: Client = create_client(url, key)
    
    try:
        print("🔄 Updating agent names from 'Suna' to 'AICxO'...")
        
        # Update agents table
        print("📝 Updating agents table...")
        agents_response = supabase.table('agents').select('*').like('name', '%Suna%').execute()
        
        for agent in agents_response.data:
            updated_data = {
                'name': agent['name'].replace('Suna', 'AICxO'),
                'description': agent['description'].replace('Suna', 'AICxO') if agent['description'] else None,
                'system_prompt': agent['system_prompt'].replace('Suna', 'AICxO') if agent['system_prompt'] else None
            }
            
            # Remove None values
            updated_data = {k: v for k, v in updated_data.items() if v is not None}
            
            result = supabase.table('agents').update(updated_data).eq('agent_id', agent['agent_id']).execute()
            print(f"✅ Updated agent: {agent['name']} -> {updated_data['name']}")
        
        # Update agent_versions table
        print("📝 Updating agent_versions table...")
        try:
            versions_response = supabase.table('agent_versions').select('*').or_('name.like.%Suna%,system_prompt.like.%Suna%').execute()

            for version in versions_response.data:
                updated_data = {}

                if version.get('name') and 'Suna' in version['name']:
                    updated_data['name'] = version['name'].replace('Suna', 'AICxO')

                if version.get('version_name') and 'Suna' in version['version_name']:
                    updated_data['version_name'] = version['version_name'].replace('Suna', 'AICxO')

                if version.get('system_prompt') and 'Suna' in version['system_prompt']:
                    updated_data['system_prompt'] = version['system_prompt'].replace('Suna', 'AICxO')

                if updated_data:
                    # Try different primary key fields
                    primary_key = version.get('version_id') or version.get('id')
                    if primary_key:
                        key_field = 'version_id' if 'version_id' in version else 'id'
                        result = supabase.table('agent_versions').update(updated_data).eq(key_field, primary_key).execute()
                        print(f"✅ Updated agent version: {version.get('name', 'Unknown')}")
                    else:
                        print(f"⚠️  Skipping version update - no primary key found: {version}")
        except Exception as e:
            print(f"⚠️  Error updating agent_versions: {str(e)}")
            print("📝 Continuing with other updates...")
        
        # Update system prompts to use AICxO identity
        print("📝 Updating system prompts...")
        new_prompt = 'You are AICxO, an AI Assistant. You help with various tasks including business strategy, product development, market analysis, and general assistance. Be helpful, insightful, and supportive.'
        
        # Update agents with old Suna prompts
        supabase.table('agents').update({
            'system_prompt': new_prompt
        }).like('system_prompt', '%You are Suna%').execute()
        
        # Update agent_versions with old Suna prompts
        supabase.table('agent_versions').update({
            'system_prompt': new_prompt
        }).like('system_prompt', '%You are Suna%').execute()
        
        print("✅ Updated system prompts")
        
        # Verify the changes
        print("\n🔍 Verifying changes...")
        updated_agents = supabase.table('agents').select('name, description').like('name', '%AICxO%').execute()
        print(f"Found {len(updated_agents.data)} agents with AICxO in name")
        
        for agent in updated_agents.data:
            print(f"  - {agent['name']}: {agent['description']}")
        
        print("\n🎉 Agent names successfully updated to AICxO!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating agent names: {str(e)}")
        return False

if __name__ == "__main__":
    success = update_agent_names()
    sys.exit(0 if success else 1)
