#!/bin/bash

# AI Co-Founder Production Fix Deployment Script
# This script fixes the 404 API errors by ensuring proper deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 AI Co-Founder Production Fix Deployment${NC}"
echo "=============================================="

# Function to print status
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "netlify.toml" ]; then
    print_error "netlify.toml not found. Please run this script from the project root."
    exit 1
fi

print_status "Found netlify.toml - in correct directory"

# Check if Netlify CLI is installed
if ! command -v netlify &> /dev/null; then
    print_warning "Netlify CLI not found. Installing..."
    npm install -g netlify-cli
    print_status "Netlify CLI installed"
else
    print_status "Netlify CLI found"
fi

# Build the frontend
echo -e "${BLUE}Building frontend...${NC}"
cd frontend
npm install
npm run build
cd ..
print_status "Frontend built successfully"

# Test Netlify functions locally first
echo -e "${BLUE}Testing Netlify functions...${NC}"
if [ -f "netlify/functions/api.js" ]; then
    print_status "API function found"
else
    print_error "API function not found at netlify/functions/api.js"
    exit 1
fi

if [ -f "netlify/functions/test.js" ]; then
    print_status "Test function found"
else
    print_error "Test function not found at netlify/functions/test.js"
    exit 1
fi

# Check if _redirects file exists
if [ -f "frontend/public/_redirects" ]; then
    print_status "_redirects file found"
else
    print_error "_redirects file not found"
    exit 1
fi

# Deploy to Netlify
echo -e "${BLUE}Deploying to Netlify...${NC}"
cd frontend
netlify deploy --prod --dir=.next
cd ..

print_status "Deployment completed"

echo -e "${GREEN}🎉 Production fix deployment completed!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Wait 2-3 minutes for deployment to propagate"
echo "2. Test the API endpoints:"
echo "   - https://aicofounder.site/api/health"
echo "   - https://aicofounder.site/api/feature-flags/custom_agents"
echo "   - https://aicofounder.site/api/billing/subscription"
echo "3. Check browser console for errors"
echo ""
echo -e "${BLUE}Test commands:${NC}"
echo "curl https://aicofounder.site/api/health"
echo "curl https://aicofounder.site/.netlify/functions/test"
