-- FINAL WORKING AGENTS FIX
-- This checks the actual table structure and creates agents accordingly

-- First, ensure the account exists in basejump.accounts
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Safe cleanup
UPDATE public.agents SET current_version_id = NULL WHERE current_version_id IS NOT NULL;
DELETE FROM public.agent_versions;
DELETE FROM public.agents;

-- Check and display the actual table structure
DO $$
DECLARE
    col_record record;
    fk_record record;
BEGIN
    RAISE NOTICE 'Checking agents table structure:';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'agents' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'agents.%: % (nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
    
    RAISE NOTICE 'Checking agent_versions table structure:';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'agent_versions' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'agent_versions.%: % (nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
    
    RAISE NOTICE 'Checking foreign key constraints:';
    FOR fk_record IN
        SELECT 
            tc.constraint_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'agent_versions'
        AND tc.table_schema = 'public'
    LOOP
        RAISE NOTICE 'FK: agent_versions.% -> %.%', fk_record.column_name, fk_record.foreign_table_name, fk_record.foreign_column_name;
    END LOOP;
END $$;

-- Add missing columns to agents table if needed
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'system_prompt' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN system_prompt text;
        RAISE NOTICE 'Added system_prompt column to agents table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_default' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_default boolean DEFAULT false;
        RAISE NOTICE 'Added is_default column to agents table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_public' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN is_public boolean DEFAULT false;
        RAISE NOTICE 'Added is_public column to agents table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'configured_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN configured_mcps jsonb DEFAULT '[]'::jsonb;
        RAISE NOTICE 'Added configured_mcps column to agents table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'custom_mcps' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN custom_mcps jsonb DEFAULT '[]'::jsonb;
        RAISE NOTICE 'Added custom_mcps column to agents table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'agentpress_tools' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN agentpress_tools jsonb DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Added agentpress_tools column to agents table';
    END IF;
END $$;

-- Create agents using only the minimal required fields first
INSERT INTO public.agents (
    account_id, 
    name, 
    description, 
    role
) VALUES 
(
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Suna',
    'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
    'assistant'
),
(
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Code Assistant',
    'A specialized AI assistant for coding, debugging, and technical development tasks.',
    'assistant'
),
(
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Marketing Advisor',
    'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
    'assistant'
);

-- Now create agent versions using the actual primary key from agents table
DO $$
DECLARE
    agent_rec record;
    version_rec record;
BEGIN
    RAISE NOTICE 'Creating agent versions for existing agents:';
    
    FOR agent_rec IN 
        SELECT * FROM public.agents 
        WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid 
        ORDER BY name
    LOOP
        RAISE NOTICE 'Processing agent: % (ID: %)', agent_rec.name, agent_rec.id;
        
        -- Insert agent version using the actual primary key (id) not agent_id
        INSERT INTO public.agent_versions (
            agent_id,
            version_number,
            name,
            system_prompt
        ) VALUES (
            agent_rec.id,  -- Use the actual primary key
            1,
            agent_rec.name || ' v1.0',
            CASE 
                WHEN agent_rec.name = 'Suna' THEN 
                    'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.'
                WHEN agent_rec.name = 'Code Assistant' THEN 
                    'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.'
                WHEN agent_rec.name = 'Marketing Advisor' THEN 
                    'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.'
                ELSE 'You are a helpful AI assistant.'
            END
        );
        
        -- Get the version that was just created and update the agent
        SELECT * INTO version_rec 
        FROM public.agent_versions 
        WHERE agent_id = agent_rec.id AND version_number = 1;
        
        -- Update the agent with the current version and other fields
        UPDATE public.agents 
        SET 
            current_version_id = version_rec.version_id,
            system_prompt = version_rec.system_prompt,
            is_default = CASE WHEN name = 'Suna' THEN true ELSE false END,
            is_public = false,
            configured_mcps = '[]'::jsonb,
            custom_mcps = '[]'::jsonb,
            agentpress_tools = '{}'::jsonb
        WHERE id = agent_rec.id;
        
        RAISE NOTICE 'Created version % for agent %', version_rec.version_id, agent_rec.name;
    END LOOP;
    
    RAISE NOTICE 'Successfully created all agents and versions';
END $$;

-- Final verification
DO $$
DECLARE
    agent_count integer;
    version_count integer;
BEGIN
    SELECT COUNT(*) INTO agent_count FROM public.agents WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    SELECT COUNT(*) INTO version_count FROM public.agent_versions;
    
    RAISE NOTICE 'Final verification: % agents, % versions created', agent_count, version_count;
END $$;

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';
