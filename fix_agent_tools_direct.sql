-- Fix agent tools configuration directly in the database
-- This will enable all core tools for the Code Assistant agent

-- First, let's check the current structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'agent_versions' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Update the agent_versions table to enable all core tools
-- Using the agent_id we found: 368c1ae6-2e2d-4441-8d51-812a9d87d72a

UPDATE agent_versions 
SET agentpress_tools = '{
    "sb_shell_tool": {"enabled": true, "description": "Execute shell commands in tmux sessions for terminal operations, CLI tools, and system management"},
    "sb_files_tool": {"enabled": true, "description": "Create, read, update, and delete files in the workspace with comprehensive file management"},
    "sb_browser_tool": {"enabled": true, "description": "Browser automation for web navigation, clicking, form filling, and page interaction"},
    "sb_deploy_tool": {"enabled": true, "description": "Deploy applications and services to various platforms"},
    "sb_expose_tool": {"enabled": true, "description": "Expose local ports for testing and development"},
    "web_search_tool": {"enabled": true, "description": "Search the web using Tavily API"},
    "sb_vision_tool": {"enabled": true, "description": "Vision and image processing capabilities"},
    "data_providers_tool": {"enabled": true, "description": "Access to data providers and external APIs"}
}'::jsonb
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a';

-- Also update the config column if it exists
UPDATE agent_versions 
SET config = jsonb_set(
    COALESCE(config, '{}'::jsonb),
    '{tools,agentpress}',
    '{
        "sb_shell_tool": true,
        "sb_files_tool": true,
        "sb_browser_tool": true,
        "sb_deploy_tool": true,
        "sb_expose_tool": true,
        "web_search_tool": true,
        "sb_vision_tool": true,
        "data_providers_tool": true
    }'::jsonb
)
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a'
AND config IS NOT NULL;

-- Verify the update
SELECT 
    agent_id,
    version_name,
    agentpress_tools,
    config->'tools'->'agentpress' as config_tools
FROM agent_versions 
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a';

-- Also check if we need to update the main agents table
SELECT 
    agent_id,
    name,
    agentpress_tools,
    config->'tools'->'agentpress' as config_tools
FROM agents 
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a';

-- Update the main agents table too if needed
UPDATE agents 
SET agentpress_tools = '{
    "sb_shell_tool": {"enabled": true, "description": "Execute shell commands in tmux sessions for terminal operations, CLI tools, and system management"},
    "sb_files_tool": {"enabled": true, "description": "Create, read, update, and delete files in the workspace with comprehensive file management"},
    "sb_browser_tool": {"enabled": true, "description": "Browser automation for web navigation, clicking, form filling, and page interaction"},
    "sb_deploy_tool": {"enabled": true, "description": "Deploy applications and services to various platforms"},
    "sb_expose_tool": {"enabled": true, "description": "Expose local ports for testing and development"},
    "web_search_tool": {"enabled": true, "description": "Search the web using Tavily API"},
    "sb_vision_tool": {"enabled": true, "description": "Vision and image processing capabilities"},
    "data_providers_tool": {"enabled": true, "description": "Access to data providers and external APIs"}
}'::jsonb
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a';

-- Update config column in agents table if it exists
UPDATE agents 
SET config = jsonb_set(
    COALESCE(config, '{}'::jsonb),
    '{tools,agentpress}',
    '{
        "sb_shell_tool": true,
        "sb_files_tool": true,
        "sb_browser_tool": true,
        "sb_deploy_tool": true,
        "sb_expose_tool": true,
        "web_search_tool": true,
        "sb_vision_tool": true,
        "data_providers_tool": true
    }'::jsonb
)
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a'
AND config IS NOT NULL;

-- Final verification
SELECT 
    'agent_versions' as table_name,
    agent_id,
    version_name,
    agentpress_tools,
    config->'tools'->'agentpress' as config_tools
FROM agent_versions 
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a'

UNION ALL

SELECT 
    'agents' as table_name,
    agent_id,
    name as version_name,
    agentpress_tools,
    config->'tools'->'agentpress' as config_tools
FROM agents 
WHERE agent_id = '368c1ae6-2e2d-4441-8d51-812a9d87d72a';

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
