-- CREATE SAMPLE AGENTS FOR TESTING
-- Run this after Step 2 completes

-- Create sample agents
INSERT INTO public.agents (account_id, name, description, instructions, created_by, updated_by)
SELECT 
    a.id,
    '<PERSON>a',
    'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
    'You are <PERSON><PERSON>, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
    a.primary_owner_user_id,
    a.primary_owner_user_id
FROM basejump.accounts a
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents ag WHERE ag.name = 'Suna' AND ag.account_id = a.id
)
UNION ALL
SELECT 
    a.id,
    'Code Assistant',
    'A specialized AI assistant for coding, debugging, and technical development tasks.',
    'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
    a.primary_owner_user_id,
    a.primary_owner_user_id
FROM basejump.accounts a
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents ag WHERE ag.name = 'Code Assistant' AND ag.account_id = a.id
)
UNION ALL
SELECT 
    a.id,
    'Marketing Advisor',
    'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
    'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
    a.primary_owner_user_id,
    a.primary_owner_user_id
FROM basejump.accounts a
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents ag WHERE ag.name = 'Marketing Advisor' AND ag.account_id = a.id
);

-- Create agent versions for the new agents
INSERT INTO public.agent_versions (agent_id, version_number, name, description, instructions, system_prompt)
SELECT 
    ag.id,
    1,
    ag.name,
    ag.description,
    ag.instructions,
    ag.instructions
FROM public.agents ag
WHERE ag.id NOT IN (SELECT DISTINCT agent_id FROM public.agent_versions WHERE agent_id IS NOT NULL);

-- Update agents to reference their current versions
UPDATE public.agents 
SET current_version_id = (
    SELECT av.id FROM public.agent_versions av
    WHERE av.agent_id = public.agents.id 
    AND av.version_number = 1 
    LIMIT 1
)
WHERE current_version_id IS NULL;
