#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix foreign key constraints in the database
"""
import os
import sys
import asyncio
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_SERVICE_ROLE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
    print("Error: Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY")
    sys.exit(1)

# Create Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

def check_table_structure():
    """Check the current table structure"""
    try:
        # Check projects table structure
        print("Checking projects table structure...")
        projects_result = supabase.rpc('get_table_info', {'table_name': 'projects'}).execute()
        print(f"Projects table info: {projects_result.data}")
        
        # Check threads table structure  
        print("Checking threads table structure...")
        threads_result = supabase.rpc('get_table_info', {'table_name': 'threads'}).execute()
        print(f"Threads table info: {threads_result.data}")
        
    except Exception as e:
        print(f"Error checking table structure: {e}")
        
        # Try a simpler approach - just query the tables
        try:
            print("Trying to query projects table...")
            projects = supabase.table('projects').select('*').limit(1).execute()
            if projects.data:
                print(f"Projects table columns: {list(projects.data[0].keys())}")
            else:
                print("Projects table is empty")
                
            print("Trying to query threads table...")
            threads = supabase.table('threads').select('*').limit(1).execute()
            if threads.data:
                print(f"Threads table columns: {list(threads.data[0].keys())}")
            else:
                print("Threads table is empty")
                
        except Exception as e2:
            print(f"Error querying tables: {e2}")

def fix_foreign_keys():
    """Fix foreign key constraints"""
    try:
        # Read the SQL migration file
        with open('fix_foreign_key_constraints.sql', 'r') as f:
            sql_content = f.read()
        
        print("Executing foreign key fix migration...")
        
        # Split the SQL into individual statements
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip() and not stmt.strip().upper().startswith('--')]
        
        for i, statement in enumerate(statements):
            if statement.upper() in ['BEGIN', 'COMMIT']:
                continue
                
            try:
                print(f"Executing statement {i+1}...")
                result = supabase.rpc('exec_sql', {'sql': statement}).execute()
                print(f"Statement {i+1} executed successfully")
            except Exception as e:
                print(f"Error executing statement {i+1}: {e}")
                print(f"Statement was: {statement[:100]}...")
                
    except Exception as e:
        print(f"Error fixing foreign keys: {e}")

def test_project_creation():
    """Test creating a project and thread"""
    try:
        print("Testing project creation...")
        
        # Create a test project
        project_data = {
            "account_id": "cd4d9095-2924-414f-9609-f201507f965e",  # Use existing account
            "name": "Test Project",
            "description": "Test project for FK validation"
        }
        
        project_result = supabase.table('projects').insert(project_data).execute()
        if project_result.data:
            project = project_result.data[0]
            project_id = project.get('project_id') or project.get('id')
            print(f"Created test project with ID: {project_id}")
            print(f"Project data: {project}")
            
            # Try to create a thread
            thread_data = {
                "project_id": project_id,
                "account_id": "cd4d9095-2924-414f-9609-f201507f965e",
                "title": "Test Thread"
            }
            
            thread_result = supabase.table('threads').insert(thread_data).execute()
            if thread_result.data:
                thread = thread_result.data[0]
                thread_id = thread.get('thread_id') or thread.get('id')
                print(f"Created test thread with ID: {thread_id}")
                print(f"Thread data: {thread}")
                
                # Clean up
                supabase.table('threads').delete().eq('id', thread_id).execute()
                print("Cleaned up test thread")
            else:
                print("Failed to create test thread")
                
            # Clean up project
            project_column = 'project_id' if 'project_id' in project else 'id'
            supabase.table('projects').delete().eq(project_column, project_id).execute()
            print("Cleaned up test project")
            
        else:
            print("Failed to create test project")
            
    except Exception as e:
        print(f"Error testing project creation: {e}")

if __name__ == "__main__":
    print("=== Database Foreign Key Fix Script ===")
    
    print("\n1. Checking table structure...")
    check_table_structure()
    
    print("\n2. Testing project creation (before fix)...")
    test_project_creation()
    
    print("\n3. Fixing foreign key constraints...")
    fix_foreign_keys()
    
    print("\n4. Testing project creation (after fix)...")
    test_project_creation()
    
    print("\nDone!")
