-- CORRECTED COMPLETE FIX FOR ACCOUNTS AND DATABASE
-- This creates the missing account and completes the setup

-- First, ensure the account exists in basejump.accounts
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e',
    'cd4d9095-2924-414f-9609-f201507f965e',
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Ensure the user exists in auth.users (if using Supabase auth)
-- This is usually handled by Supabase Auth, but let's make sure
DO $$
BEGIN
    -- Check if user exists in auth.users, if not create a placeholder
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = 'cd4d9095-2924-414f-9609-f201507f965e') THEN
        -- Note: In production, this would be handled by <PERSON>pa<PERSON> Auth
        -- For development, we'll ensure the foreign key constraint is satisfied
        INSERT INTO auth.users (id, email, created_at, updated_at)
        VALUES (
            'cd4d9095-2924-414f-9609-f201507f965e',
            '<EMAIL>',
            now(),
            now()
        ) ON CONFLICT (id) DO NOTHING;
    END IF;
EXCEPTION
    WHEN insufficient_privilege THEN
        -- If we can't insert into auth.users (which is normal), that's okay
        -- The account creation above should be sufficient
        NULL;
END $$;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

-- Ensure all required columns exist in projects table
DO $$
BEGIN
    -- Add project_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN project_id uuid DEFAULT extensions.uuid_generate_v4();
        UPDATE public.projects SET project_id = id WHERE project_id IS NULL;
    END IF;
    
    -- Ensure account_id exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
    END IF;
    
    -- Ensure other required columns exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'status' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN status text DEFAULT 'active';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'metadata' AND table_schema = 'public') THEN
        ALTER TABLE public.projects ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- Ensure threads table has all required columns
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'project_id' AND table_schema = 'public') THEN
        ALTER TABLE public.threads ADD COLUMN project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Ensure agents table has all required columns
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'account_id' AND table_schema = 'public') THEN
        ALTER TABLE public.agents ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Update existing data to use the correct account_id
UPDATE public.projects SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e' WHERE account_id IS NULL;
UPDATE public.threads SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e' WHERE account_id IS NULL;
UPDATE public.agents SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e' WHERE account_id IS NULL;
UPDATE public.messages SET account_id = 'cd4d9095-2924-414f-9609-f201507f965e' WHERE account_id IS NULL;

-- Create sample data with the correct account_id
INSERT INTO public.projects (account_id, name, description, status)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e',
    'Welcome Project',
    'Your first project to get started with Suna',
    'active'
) ON CONFLICT DO NOTHING;

-- Create sample threads
INSERT INTO public.threads (account_id, project_id, title, status)
SELECT 
    'cd4d9095-2924-414f-9609-f201507f965e',
    p.id,
    'Welcome Chat',
    'active'
FROM public.projects p
WHERE p.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'
AND NOT EXISTS (
    SELECT 1 FROM public.threads t 
    WHERE t.account_id = 'cd4d9095-2924-414f-9609-f201507f965e' AND t.project_id = p.id
);

-- Create sample agents with the correct account_id (without problematic constraint)
INSERT INTO public.agents (account_id, name, description, instructions, created_by, updated_by)
SELECT 
    'cd4d9095-2924-414f-9609-f201507f965e',
    'Suna',
    'Your AI Co-Founder assistant that helps with business strategy, product development, and startup guidance.',
    'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
    'cd4d9095-2924-414f-9609-f201507f965e',
    'cd4d9095-2924-414f-9609-f201507f965e'
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents 
    WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e' AND name = 'Suna'
)
UNION ALL
SELECT 
    'cd4d9095-2924-414f-9609-f201507f965e',
    'Code Assistant',
    'A specialized AI assistant for coding, debugging, and technical development tasks.',
    'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
    'cd4d9095-2924-414f-9609-f201507f965e',
    'cd4d9095-2924-414f-9609-f201507f965e'
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents 
    WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e' AND name = 'Code Assistant'
)
UNION ALL
SELECT 
    'cd4d9095-2924-414f-9609-f201507f965e',
    'Marketing Advisor',
    'An AI assistant specialized in marketing strategy, content creation, and growth tactics.',
    'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
    'cd4d9095-2924-414f-9609-f201507f965e',
    'cd4d9095-2924-414f-9609-f201507f965e'
WHERE NOT EXISTS (
    SELECT 1 FROM public.agents 
    WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e' AND name = 'Marketing Advisor'
);

-- Create agent versions for the new agents
INSERT INTO public.agent_versions (agent_id, version_number, name, description, instructions, system_prompt)
SELECT 
    ag.id,
    1,
    ag.name,
    ag.description,
    ag.instructions,
    ag.instructions
FROM public.agents ag
WHERE ag.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'
AND ag.id NOT IN (SELECT DISTINCT agent_id FROM public.agent_versions WHERE agent_id IS NOT NULL);

-- Update agents to reference their current versions
UPDATE public.agents 
SET current_version_id = (
    SELECT av.id FROM public.agent_versions av
    WHERE av.agent_id = public.agents.id 
    AND av.version_number = 1 
    LIMIT 1
)
WHERE current_version_id IS NULL;

-- Refresh schema cache again
NOTIFY pgrst, 'reload schema';
