#!/usr/bin/env python3
"""
Test message flow to identify the exact issue
"""

import requests
import json
import uuid

def test_message_flow():
    """Test the exact message flow that the frontend uses"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Message Flow - Simulating Frontend")
    print("=" * 50)
    
    # Step 1: Check available models
    print("\n1. Checking Available Models...")
    try:
        response = requests.get(f"{base_url}/api/billing/models")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            models_data = response.json()
            print(f"   Available models: {len(models_data.get('models', []))}")
            for model in models_data.get('models', [])[:5]:  # Show first 5
                print(f"     - {model.get('id', 'N/A')} ({model.get('display_name', 'N/A')})")
        else:
            print(f"   Error: {response.text[:200]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Step 2: Test thread creation (this might work without auth in local mode)
    print("\n2. Testing Thread Creation...")
    thread_id = str(uuid.uuid4())
    try:
        response = requests.post(f"{base_url}/api/threads", json={
            "title": "Test Thread",
            "project_id": None
        })
        print(f"   Status: {response.status_code}")
        if response.status_code == 200 or response.status_code == 201:
            thread_data = response.json()
            thread_id = thread_data.get('id', thread_id)
            print(f"   ✅ Thread created: {thread_id}")
        else:
            print(f"   Using fallback thread ID: {thread_id}")
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   Using fallback thread ID: {thread_id}")
        print(f"   Error: {str(e)}")
    
    # Step 3: Test message creation
    print("\n3. Testing Message Creation...")
    try:
        response = requests.post(f"{base_url}/api/thread/{thread_id}/messages", json={
            "content": "Hello! This is a test message to verify GROQ is working.",
            "role": "user"
        })
        print(f"   Status: {response.status_code}")
        if response.status_code == 200 or response.status_code == 201:
            print("   ✅ Message created successfully")
        else:
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Step 4: Test agent start with GROQ model
    print("\n4. Testing Agent Start with GROQ Model...")
    try:
        response = requests.post(f"{base_url}/api/thread/{thread_id}/agent/start", json={
            "model_name": "groq/llama-3.3-70b-versatile",
            "enable_thinking": False,
            "stream": False,
            "reasoning_effort": "low",
            "enable_context_manager": False
        })
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:300]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Step 5: Test agent start with default model (no model specified)
    print("\n5. Testing Agent Start with Default Model...")
    try:
        response = requests.post(f"{base_url}/api/thread/{thread_id}/agent/start", json={
            "enable_thinking": False,
            "stream": False,
            "reasoning_effort": "low",
            "enable_context_manager": False
        })
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:300]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Step 6: Test agent start with Gemini model
    print("\n6. Testing Agent Start with Gemini Model...")
    try:
        response = requests.post(f"{base_url}/api/thread/{thread_id}/agent/start", json={
            "model_name": "gemini/gemini-1.5-flash",
            "enable_thinking": False,
            "stream": False,
            "reasoning_effort": "low",
            "enable_context_manager": False
        })
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:300]}")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 Message Flow Test Complete!")
    print("\nNext: Check backend logs for detailed model usage information")

if __name__ == "__main__":
    test_message_flow()
