-- Fix messages table by adding missing agent_id column
-- This fixes the schema cache error: "Could not find the 'agent_id' column of 'messages'"

BEGIN;

-- Add agent_id column to messages table if it doesn't exist
DO $$
BEGIN
    -- Check if agent_id column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'agent_id'
        AND table_schema = 'public'
    ) THEN
        -- Add the agent_id column
        ALTER TABLE public.messages 
        ADD COLUMN agent_id uuid REFERENCES public.agents(id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added agent_id column to messages table';
    ELSE
        RAISE NOTICE 'agent_id column already exists in messages table';
    END IF;
END $$;

-- Create index on agent_id for better performance
CREATE INDEX IF NOT EXISTS idx_messages_agent_id ON public.messages(agent_id);

-- Update RLS policies to include agent_id if needed
-- (This ensures the new column works with existing security policies)

-- Verify the column was added
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'messages' 
AND table_schema = 'public'
AND column_name = 'agent_id';

COMMIT;

SELECT 'Messages table agent_id column fix completed!' as status;
