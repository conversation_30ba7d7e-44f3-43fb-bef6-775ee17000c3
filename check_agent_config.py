#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the current agent configuration and fix tool settings
"""

import os
import sys
import json
from supabase import create_client, Client

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def check_agent_config():
    # Load environment variables
    from dotenv import load_dotenv
    
    # Try different paths for .env file
    env_paths = [
        'backend/.env',
        '.env',
        os.path.join(os.path.dirname(__file__), 'backend', '.env'),
        os.path.join(os.path.dirname(__file__), '.env')
    ]
    
    env_loaded = False
    for env_path in env_paths:
        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"📁 Loaded environment from: {env_path}")
            env_loaded = True
            break
    
    if not env_loaded:
        print("⚠️  No .env file found, trying environment variables...")
    
    # Initialize Supabase client
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not url or not key:
        print("❌ Error: Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY")
        return False
    
    supabase: Client = create_client(url, key)
    
    try:
        print("🔍 Checking current agent configurations...")
        
        # Get all agents
        agents_result = supabase.table('agents').select('*').execute()
        
        if agents_result.data:
            print(f"📋 Found {len(agents_result.data)} agents:")
            
            for agent in agents_result.data:
                print(f"\n🤖 Agent: {agent['name']} (ID: {agent['agent_id']})")
                print(f"   Description: {agent.get('description', 'N/A')}")
                print(f"   Is Default: {agent.get('is_default', False)}")
                
                # Get the current version
                if agent.get('current_version_id'):
                    # Try different column names for version_id
                    try:
                        version_result = supabase.table('agent_versions').select('*').eq('version_id', agent['current_version_id']).execute()
                    except:
                        try:
                            version_result = supabase.table('agent_versions').select('*').eq('id', agent['current_version_id']).execute()
                        except:
                            version_result = supabase.table('agent_versions').select('*').eq('agent_version_id', agent['current_version_id']).execute()
                    
                    if version_result.data:
                        version = version_result.data[0]
                        print(f"   Version: {version.get('version_name', 'N/A')}")
                        
                        # Check agentpress_tools configuration
                        agentpress_tools = version.get('agentpress_tools', {})
                        print(f"   AgentPress Tools: {json.dumps(agentpress_tools, indent=2) if agentpress_tools else 'Empty/None'}")
                        
                        # Check configured_mcps
                        configured_mcps = version.get('configured_mcps', [])
                        print(f"   Configured MCPs: {len(configured_mcps)} servers")
                        
                        # Check custom_mcps
                        custom_mcps = version.get('custom_mcps', [])
                        print(f"   Custom MCPs: {len(custom_mcps)} servers")
                        
                        # Check if this agent has no tools enabled
                        if not agentpress_tools and not configured_mcps and not custom_mcps:
                            print("   ⚠️  WARNING: This agent has NO TOOLS enabled!")
                            print("   🔧 This agent will only have basic message tools")
                        
                        # Count enabled tools
                        enabled_count = 0
                        if isinstance(agentpress_tools, dict):
                            for tool_name, tool_config in agentpress_tools.items():
                                if isinstance(tool_config, dict) and tool_config.get('enabled', False):
                                    enabled_count += 1
                                elif isinstance(tool_config, bool) and tool_config:
                                    enabled_count += 1
                        
                        print(f"   📊 Enabled AgentPress Tools: {enabled_count}")
                        
                        if enabled_count == 0 and not configured_mcps and not custom_mcps:
                            print("   🚨 ISSUE FOUND: Agent has no functional tools!")

                            # Suggest fix
                            print("\n🛠️  SUGGESTED FIX:")
                            print("   1. Enable core tools for this agent")
                            print("   2. Or modify the system to default to full tools when none are specified")

                            # Return agent_id and try to get version_id from different fields
                            version_id = version.get('version_id') or version.get('id') or version.get('agent_version_id') or agent['current_version_id']
                            return agent['agent_id'], version_id
        
        else:
            print("📭 No agents found in database")
        
        return None, None
        
    except Exception as e:
        print(f"❌ Error checking agent config: {str(e)}")
        return None, None

def fix_agent_tools(agent_id, version_id):
    """Fix the agent configuration to enable all core tools"""
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    supabase: Client = create_client(url, key)
    
    # Define the full set of core tools that should be enabled
    core_tools = {
        'sb_shell_tool': {'enabled': True, 'description': 'Execute shell commands in tmux sessions for terminal operations, CLI tools, and system management'},
        'sb_files_tool': {'enabled': True, 'description': 'Create, read, update, and delete files in the workspace with comprehensive file management'},
        'sb_browser_tool': {'enabled': True, 'description': 'Browser automation for web navigation, clicking, form filling, and page interaction'},
        'sb_deploy_tool': {'enabled': True, 'description': 'Deploy applications and services to various platforms'},
        'sb_expose_tool': {'enabled': True, 'description': 'Expose local ports for testing and development'},
        'web_search_tool': {'enabled': True, 'description': 'Search the web using Tavily API'},
        'sb_vision_tool': {'enabled': True, 'description': 'Vision and image processing capabilities'},
        'data_providers_tool': {'enabled': True, 'description': 'Access to data providers and external APIs'}
    }
    
    try:
        print(f"🔧 Updating agent version {version_id} with core tools...")
        
        # Update the agent version with the core tools
        # Try different column names for the update
        try:
            update_result = supabase.table('agent_versions').update({
                'agentpress_tools': core_tools
            }).eq('version_id', version_id).execute()
        except:
            try:
                update_result = supabase.table('agent_versions').update({
                    'agentpress_tools': core_tools
                }).eq('id', version_id).execute()
            except:
                update_result = supabase.table('agent_versions').update({
                    'agentpress_tools': core_tools
                }).eq('agent_version_id', version_id).execute()
        
        if update_result.data:
            print("✅ Successfully updated agent with core tools!")
            print("📋 Enabled tools:")
            for tool_name, tool_config in core_tools.items():
                print(f"   - {tool_name}: {tool_config['description']}")
            return True
        else:
            print("❌ Failed to update agent configuration")
            return False
            
    except Exception as e:
        print(f"❌ Error updating agent: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 Checking agent configurations for tool issues...\n")
    
    agent_id, version_id = check_agent_config()
    
    if agent_id and version_id:
        print(f"\n🔧 Found agent with no tools: {agent_id}")
        
        response = input("\n❓ Would you like to fix this agent by enabling all core tools? (y/n): ")
        if response.lower() in ['y', 'yes']:
            success = fix_agent_tools(agent_id, version_id)
            if success:
                print("\n🎉 Agent configuration fixed!")
                print("🔄 Please restart the backend server to apply changes")
            else:
                print("\n❌ Failed to fix agent configuration")
        else:
            print("\n📝 Agent configuration not changed")
    else:
        print("\n✅ No issues found with agent configurations")
    
    print("\n🏁 Check completed!")
