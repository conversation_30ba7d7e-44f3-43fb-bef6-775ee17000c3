-- Fix foreign key constraints to match actual table structure
-- This script ensures that foreign key constraints reference the correct primary key columns

BEGIN;

-- First, check if we need to fix the projects table structure
DO $$
DECLARE
    has_project_id_pk boolean;
    has_id_pk boolean;
    has_threads_fk boolean;
BEGIN
    -- Check if projects table has project_id as primary key
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'projects' 
        AND tc.constraint_type = 'PRIMARY KEY' 
        AND kcu.column_name = 'project_id'
        AND tc.table_schema = 'public'
    ) INTO has_project_id_pk;
    
    -- Check if projects table has id as primary key
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'projects' 
        AND tc.constraint_type = 'PRIMARY KEY' 
        AND kcu.column_name = 'id'
        AND tc.table_schema = 'public'
    ) INTO has_id_pk;
    
    -- Check if threads table has foreign key to projects
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        WHERE tc.table_name = 'threads' 
        AND tc.constraint_type = 'FOREIGN KEY'
        AND tc.constraint_name LIKE '%project%'
        AND tc.table_schema = 'public'
    ) INTO has_threads_fk;
    
    RAISE NOTICE 'Projects table - has project_id PK: %, has id PK: %, threads has FK: %', 
        has_project_id_pk, has_id_pk, has_threads_fk;
    
    -- If projects table uses 'id' as PK but threads FK references 'project_id', we need to fix it
    IF has_id_pk AND NOT has_project_id_pk AND has_threads_fk THEN
        RAISE NOTICE 'Fixing foreign key constraint to reference projects(id) instead of projects(project_id)';
        
        -- Drop existing foreign key constraint if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE table_name = 'threads' 
            AND constraint_name = 'threads_project_id_fkey'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE public.threads DROP CONSTRAINT threads_project_id_fkey;
            RAISE NOTICE 'Dropped existing threads_project_id_fkey constraint';
        END IF;
        
        -- Add correct foreign key constraint
        ALTER TABLE public.threads 
        ADD CONSTRAINT threads_project_id_fkey 
        FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added new foreign key constraint referencing projects(id)';
    END IF;
    
    -- If projects table uses 'project_id' as PK, ensure threads FK references it correctly
    IF has_project_id_pk AND NOT has_id_pk THEN
        RAISE NOTICE 'Projects table uses project_id as PK - ensuring FK is correct';
        
        -- Drop existing foreign key constraint if it references wrong column
        IF EXISTS (
            SELECT 1 FROM information_schema.referential_constraints rc
            JOIN information_schema.key_column_usage kcu ON rc.constraint_name = kcu.constraint_name
            WHERE rc.constraint_name = 'threads_project_id_fkey'
            AND kcu.table_name = 'threads'
            AND rc.unique_constraint_name NOT IN (
                SELECT tc.constraint_name 
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu2 ON tc.constraint_name = kcu2.constraint_name
                WHERE tc.table_name = 'projects' 
                AND tc.constraint_type = 'PRIMARY KEY' 
                AND kcu2.column_name = 'project_id'
            )
        ) THEN
            ALTER TABLE public.threads DROP CONSTRAINT threads_project_id_fkey;
            RAISE NOTICE 'Dropped incorrect foreign key constraint';
        END IF;
        
        -- Add correct foreign key constraint
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE table_name = 'threads' 
            AND constraint_name = 'threads_project_id_fkey'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE public.threads 
            ADD CONSTRAINT threads_project_id_fkey 
            FOREIGN KEY (project_id) REFERENCES public.projects(project_id) ON DELETE CASCADE;
            
            RAISE NOTICE 'Added foreign key constraint referencing projects(project_id)';
        END IF;
    END IF;
END $$;

-- Also fix any other foreign key constraints that might have similar issues
DO $$
BEGIN
    -- Fix agent_runs table if it exists and has wrong FK
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'agent_runs' AND table_schema = 'public') THEN
        -- Check if agent_runs references threads correctly
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'threads' AND column_name = 'id' AND table_schema = 'public'
        ) AND EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'agent_runs' AND column_name = 'thread_id' AND table_schema = 'public'
        ) THEN
            -- Drop and recreate FK if needed
            IF EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE table_name = 'agent_runs' 
                AND constraint_name LIKE '%thread%'
                AND constraint_type = 'FOREIGN KEY'
                AND table_schema = 'public'
            ) THEN
                -- Check if FK references wrong column
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.referential_constraints rc
                    JOIN information_schema.key_column_usage kcu ON rc.constraint_name = kcu.constraint_name
                    JOIN information_schema.key_column_usage kcu2 ON rc.unique_constraint_name = kcu2.constraint_name
                    WHERE kcu.table_name = 'agent_runs' 
                    AND kcu.column_name = 'thread_id'
                    AND kcu2.table_name = 'threads'
                    AND kcu2.column_name = (
                        CASE 
                            WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'threads' AND column_name = 'thread_id' AND table_schema = 'public') 
                            THEN 'thread_id'
                            ELSE 'id'
                        END
                    )
                ) THEN
                    -- Drop existing constraint
                    PERFORM 1 FROM information_schema.table_constraints 
                    WHERE table_name = 'agent_runs' 
                    AND constraint_type = 'FOREIGN KEY'
                    AND constraint_name LIKE '%thread%'
                    AND table_schema = 'public';
                    
                    -- We'll let the application handle this for now
                    RAISE NOTICE 'Agent_runs FK constraint may need manual fixing';
                END IF;
            END IF;
        END IF;
    END IF;
END $$;

COMMIT;
