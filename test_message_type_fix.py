#!/usr/bin/env python3
"""
Test the schema fix - using 'message_type' instead of 'type' column
"""

import requests
import json
import uuid

def test_message_type_fix():
    """Test that the message_type column fix works"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Schema Fix (message_type column)")
    print("=" * 50)
    
    # Test 1: Agent initiate endpoint
    print("\n1. Testing Agent Initiate Endpoint...")
    
    form_data = {
        'prompt': 'Hello, this is a test to verify the message_type column works!',
        'model_name': 'groq/llama-3.1-70b-versatile',
        'enable_thinking': 'false',
        'stream': 'true'
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/agent/initiate",
            data=form_data,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ SUCCESS: Returns 401 (auth required) - no schema error!")
        elif response.status_code == 500:
            print("   ❌ FAILED: Still returns 500 error")
            if "type" in response.text:
                print("   ❌ Still has 'type' column error")
            elif "message_type" in response.text:
                print("   ❌ Now has 'message_type' column error")
            print(f"   Full response: {response.text}")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    # Test 2: Message endpoint
    print("\n2. Testing Message Endpoint...")
    
    test_thread_id = str(uuid.uuid4())
    message_data = {
        "content": "Test message with message_type column fix"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/thread/{test_thread_id}/messages",
            json=message_data,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ SUCCESS: Returns 401 (auth required) - no schema error!")
        elif response.status_code == 500:
            print("   ❌ FAILED: Still returns 500 error")
            if "type" in response.text:
                print("   ❌ Still has 'type' column error")
            elif "message_type" in response.text:
                print("   ❌ Now has 'message_type' column error")
            print(f"   Full response: {response.text}")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print("If both tests show 401 (auth required) instead of 500 (schema error),")
    print("then the database schema issue has been resolved!")
    print("The frontend should now be able to send messages successfully.")

if __name__ == "__main__":
    test_message_type_fix()
