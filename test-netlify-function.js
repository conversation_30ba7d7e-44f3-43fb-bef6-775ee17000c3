// Test Netlify Function Accessibility
// Run this in browser console on https://aicofounder.site

console.log('🔍 Testing Netlify Function Accessibility...');

const testNetlifyFunction = async () => {
  const tests = [
    {
      name: 'Direct Function Call',
      url: '/.netlify/functions/api',
      description: 'Test if the function is accessible directly'
    },
    {
      name: 'Function with Health Path',
      url: '/.netlify/functions/api/health',
      description: 'Test health endpoint through direct function call'
    },
    {
      name: 'API Redirect Test',
      url: '/api/health',
      description: 'Test if redirects are working'
    },
    {
      name: 'Feature Flags Test',
      url: '/api/feature-flags/custom_agents',
      description: 'Test feature flags endpoint'
    }
  ];

  for (const test of tests) {
    try {
      console.log(`\n🧪 ${test.name}: ${test.description}`);
      console.log(`   URL: ${test.url}`);
      
      const response = await fetch(test.url);
      const responseText = await response.text();
      
      console.log(`   Status: ${response.status}`);
      console.log(`   Headers:`, Object.fromEntries(response.headers.entries()));
      
      if (response.ok) {
        try {
          const data = JSON.parse(responseText);
          console.log(`   ✅ SUCCESS:`, data);
        } catch (e) {
          console.log(`   ✅ SUCCESS (non-JSON):`, responseText.substring(0, 200));
        }
      } else {
        console.log(`   ❌ FAILED:`, responseText.substring(0, 200));
      }
    } catch (error) {
      console.log(`   ❌ ERROR:`, error.message);
    }
  }
};

// Run the test
testNetlifyFunction().then(() => {
  console.log('\n🎯 Netlify function test completed!');
});

// Export for manual use
window.testNetlifyFunction = testNetlifyFunction;
