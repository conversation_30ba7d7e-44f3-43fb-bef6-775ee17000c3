#!/usr/bin/env python3
"""
Verify that the superuser system is working correctly
"""

import os
import asyncio
from dotenv import load_dotenv
from supabase import create_async_client

# Load environment variables
load_dotenv()

async def verify_superuser_system():
    """Verify the superuser system is working"""
    
    print("🔍 Verifying superuser system...")
    print("=" * 50)
    
    # Get environment variables
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Error: Missing environment variables")
        return False
    
    try:
        # Create Supabase client
        supabase = await create_async_client(supabase_url, supabase_key)
        
        print("1. 🔍 Checking superusers table...")
        
        # Check if superusers table exists and has data
        try:
            superusers_result = await supabase.table('superusers').select('*').execute()
            if superusers_result.data:
                print("   ✅ Superusers table exists and has data")
                for user in superusers_result.data:
                    print(f"   📧 Superuser: {user['email']} (Active: {user['is_active']})")
            else:
                print("   ⚠️  Superusers table exists but is empty")
        except Exception as e:
            print(f"   ❌ Superusers table check failed: {e}")
        
        print("\n2. 🔍 Checking admin_activity_log table...")
        
        # Check if admin_activity_log table exists
        try:
            activity_result = await supabase.table('admin_activity_log').select('*').limit(1).execute()
            print("   ✅ Admin activity log table exists")
        except Exception as e:
            print(f"   ❌ Admin activity log table check failed: {e}")
        
        print("\n3. 🔍 Testing is_superuser function...")
        
        # Test the is_superuser function
        try:
            function_result = await supabase.rpc('is_superuser', {'user_email': '<EMAIL>'}).execute()
            if function_result.data:
                print("   ✅ is_superuser function works!")
                print(f"   📧 <EMAIL> is superuser: {function_result.data}")
            else:
                print("   ⚠️  is_superuser function returned no data")
        except Exception as e:
            print(f"   ❌ is_superuser function test failed: {e}")
        
        print("\n4. 🔍 Testing log_admin_activity function...")
        
        # Test the log_admin_activity function
        try:
            log_result = await supabase.rpc('log_admin_activity', {
                'action_name': 'test_verification',
                'target_type_param': 'system',
                'target_id_param': 'verification',
                'details_param': {'test': True}
            }).execute()
            print("   ✅ log_admin_activity function works!")
        except Exception as e:
            print(f"   ❌ log_admin_activity function test failed: {e}")
        
        print("\n" + "=" * 50)
        print("🎯 VERIFICATION SUMMARY:")
        
        # Final check - try <NAME_EMAIL> superuser record
        try:
            final_check = await supabase.table('superusers').select('*').eq('email', '<EMAIL>').execute()
            if final_check.data:
                user_data = final_check.data[0]
                print("✅ SUPERUSER SYSTEM IS WORKING!")
                print(f"   📧 Email: {user_data['email']}")
                print(f"   🔑 User ID: {user_data['user_id']}")
                print(f"   ✅ Active: {user_data['is_active']}")
                print(f"   📅 Created: {user_data['created_at']}")
                print("\n🚀 <EMAIL> can now access:")
                print("   • Admin Dashboard at /admin")
                print("   • View all users and their activity")
                print("   • System statistics and monitoring")
                print("   • Admin activity logs")
                return True
            else:
                print("❌ SUPERUSER RECORD NOT FOUND")
                print("   <EMAIL> is not set up as a superuser")
                print("   Please run the manual SQL setup first")
                return False
        except Exception as e:
            print(f"❌ Final verification failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(verify_superuser_system())
    if success:
        print("\n🎉 SUCCESS! The superuser system is fully operational.")
    else:
        print("\n⚠️  Issues found. Please check the setup.")
        exit(1)
