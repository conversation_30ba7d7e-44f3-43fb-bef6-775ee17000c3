#!/usr/bin/env python3
"""
Simple database fix for Suna application
Adds missing agent_id column to messages table
"""

import os
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    print("Starting database schema fix...")
    
    # Get Supabase credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not supabase_url or not supabase_service_key:
        print("ERROR: Missing Supabase credentials")
        return False
    
    try:
        # Create Supabase client
        supabase = create_client(supabase_url, supabase_service_key)
        
        print("Connected to Supabase successfully")
        
        # Check if agents table exists first
        check_agents_sql = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'agents'
        );
        """
        
        # For now, let's just add the column without the foreign key constraint
        # since we're not sure if the agents table exists
        sql_add_column = """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'messages' 
                AND column_name = 'agent_id'
                AND table_schema = 'public'
            ) THEN
                ALTER TABLE public.messages 
                ADD COLUMN agent_id uuid;
                
                RAISE NOTICE 'Added agent_id column to messages table';
            ELSE
                RAISE NOTICE 'agent_id column already exists';
            END IF;
        END $$;
        """
        
        print("Executing SQL to add agent_id column...")
        
        # Try to execute using the table() method and raw SQL
        try:
            # Use raw SQL execution
            result = supabase.table('messages').select('id').limit(1).execute()
            print("Messages table exists and is accessible")
            
            # Now try to add the column using a different approach
            # We'll use the REST API to execute raw SQL
            print("Adding agent_id column...")
            
            # Since we can't execute DDL directly through the client,
            # let's create a simple verification script
            print("SUCCESS: Database connection verified")
            print("MANUAL STEP REQUIRED:")
            print("Please run the following SQL in your Supabase SQL editor:")
            print("")
            print(sql_add_column)
            print("")
            print("This will add the missing agent_id column to the messages table.")
            
            return True
            
        except Exception as e:
            print("Error accessing messages table: " + str(e))
            return False

    except Exception as e:
        print("Error connecting to Supabase: " + str(e))
        return False

if __name__ == "__main__":
    main()
