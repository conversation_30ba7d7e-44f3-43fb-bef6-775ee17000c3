#!/usr/bin/env python3
"""
Run the superuser system setup SQL script
"""

import os
import asyncio
from dotenv import load_dotenv
from supabase import create_async_client

# Load environment variables
load_dotenv()

async def setup_superuser_system():
    """Setup the superuser system"""
    
    print("🔧 Setting up superuser <NAME_EMAIL>...")
    print("=" * 70)
    
    # Get environment variables
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Error: Missing environment variables")
        print("   SUPABASE_URL:", supabase_url)
        print("   SUPABASE_SERVICE_ROLE_KEY:", "***" if supabase_key else "None")
        return False
    
    try:
        # Create Supabase client
        supabase = await create_async_client(supabase_url, supabase_key)
        
        print("📋 Reading SQL setup script...")
        
        # Read the SQL script
        with open('../create_superuser_system.sql', 'r') as f:
            sql_script = f.read()
        
        print("🗄️  Executing superuser system setup...")
        
        # Execute the SQL script in chunks
        sql_commands = sql_script.split(';')
        
        for i, command in enumerate(sql_commands):
            command = command.strip()
            if command and not command.startswith('--'):
                try:
                    print(f"   Executing command {i+1}/{len(sql_commands)}...")
                    
                    # Use raw SQL execution
                    result = await supabase.rpc('exec_sql', {'sql': command + ';'}).execute()
                    
                    if result.data:
                        print(f"   ✅ Command {i+1} executed successfully")
                    
                except Exception as cmd_error:
                    if "already exists" in str(cmd_error).lower():
                        print(f"   ⚠️  Command {i+1} - object already exists (OK)")
                    else:
                        print(f"   ❌ Command {i+1} failed: {cmd_error}")
        
        print("\n🧪 Testing superuser setup...")
        
        # Test the superuser function
        test_result = await supabase.rpc('is_superuser', {'user_email': '<EMAIL>'}).execute()
        
        if test_result.data:
            print("   ✅ Superuser function test successful!")
            print(f"   <EMAIL> is_superuser: {test_result.data}")
        else:
            print("   ⚠️  Superuser function test returned no data")
        
        # Check superuser records
        superuser_check = await supabase.table('superusers').select('*').eq('email', '<EMAIL>').execute()
        
        if superuser_check.data:
            print("   ✅ Superuser record found!")
            print(f"   Record: {superuser_check.data[0]}")
        else:
            print("   ⚠️  No superuser record found - user may need to sign up first")
        
        print("\n🎉 Superuser system setup completed successfully!")
        print("   <EMAIL> now has superuser access")
        print("   Admin dashboard available at: /admin")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up superuser system: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Show manual instructions
        print("\n📝 MANUAL SETUP REQUIRED:")
        print("   Go to your Supabase dashboard > SQL Editor and run:")
        print("   the contents of create_superuser_system.sql")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(setup_superuser_system())
    if success:
        print("\n🚀 Ready! <EMAIL> can now access the admin dashboard.")
        print("   Navigate to /admin in the application to access superuser features.")
    else:
        print("\n⚠️  Manual intervention required. See instructions above.")
        exit(1)
