#!/usr/bin/env python3
"""
Enable required feature flags for the application
"""
import asyncio
import os
from dotenv import load_dotenv
from flags.flags import enable_flag, is_enabled, list_flags

load_dotenv()

async def enable_required_flags():
    """Enable all required feature flags for the application"""
    
    print("🚀 Enabling required feature flags...")
    
    # List of required feature flags
    required_flags = [
        ('knowledge_base', 'Enable knowledge base functionality'),
        ('custom_agents', 'Enable custom agent creation and management'),
        ('agent_marketplace', 'Enable agent marketplace features'),
        ('workflows', 'Enable workflow functionality'),
        ('integrations', 'Enable integrations functionality'),
        ('triggers', 'Enable trigger functionality')
    ]
    
    # Check current status
    print("\n📋 Current feature flag status:")
    try:
        current_flags = await list_flags()
        for flag_name, description in required_flags:
            status = current_flags.get(flag_name, False)
            print(f"  {flag_name}: {'✅ enabled' if status else '❌ disabled'}")
    except Exception as e:
        print(f"  ⚠️  Could not check current status: {e}")
    
    # Enable all required flags
    print("\n🔧 Enabling feature flags...")
    for flag_name, description in required_flags:
        try:
            success = await enable_flag(flag_name, description)
            if success:
                print(f"  ✅ {flag_name}: enabled")
            else:
                print(f"  ❌ {flag_name}: failed to enable")
        except Exception as e:
            print(f"  ❌ {flag_name}: error - {e}")
    
    # Verify all flags are enabled
    print("\n🔍 Verifying feature flags...")
    all_enabled = True
    for flag_name, description in required_flags:
        try:
            enabled = await is_enabled(flag_name)
            if enabled:
                print(f"  ✅ {flag_name}: verified enabled")
            else:
                print(f"  ❌ {flag_name}: still disabled")
                all_enabled = False
        except Exception as e:
            print(f"  ⚠️  {flag_name}: could not verify - {e}")
            all_enabled = False
    
    if all_enabled:
        print("\n🎉 All required feature flags are enabled!")
    else:
        print("\n⚠️  Some feature flags may not be enabled. Check Redis connection.")
        print("   In local development mode, flags should work even without Redis.")
    
    print("\n✅ Feature flag setup completed!")

if __name__ == "__main__":
    asyncio.run(enable_required_flags())
