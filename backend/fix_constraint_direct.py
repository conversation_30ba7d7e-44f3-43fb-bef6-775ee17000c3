#!/usr/bin/env python3
"""
Direct fix for the messages table role constraint
"""

import os
import asyncio
from supabase import create_async_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def fix_role_constraint():
    """Fix the messages table role constraint"""
    
    print("🔧 Fixing messages table role constraint...")
    print("=" * 60)
    
    # Get environment variables
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Error: Missing environment variables")
        print("   SUPABASE_URL:", supabase_url)
        print("   SUPABASE_SERVICE_ROLE_KEY:", "***" if supabase_key else "None")
        return False
    
    try:
        # Create Supabase client
        supabase = await create_async_client(supabase_url, supabase_key)
        
        print("📋 Executing SQL commands to fix role constraint...")
        
        # Method 1: Try using direct SQL execution
        try:
            # Drop the existing constraint
            drop_sql = "ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_role_check;"
            await supabase.rpc('exec_sql', {'sql': drop_sql}).execute()
            print("   ✅ Dropped old constraint")
            
            # Add the new constraint
            add_sql = """
            ALTER TABLE public.messages ADD CONSTRAINT messages_role_check 
            CHECK (role IN (
                'user', 'assistant', 'system', 'tool', 'status', 
                'assistant_response_end', 'assistant_response_start', 
                'tool_result', 'error'
            ));
            """
            await supabase.rpc('exec_sql', {'sql': add_sql}).execute()
            print("   ✅ Added new constraint")
            
        except Exception as rpc_error:
            print(f"   ⚠️  RPC method failed: {rpc_error}")
            print("   🔄 Trying alternative approach...")
            
            # Method 2: Try using raw SQL through PostgREST
            try:
                # Use PostgREST's raw SQL capability
                response = await supabase.postgrest.rpc('exec_sql', {
                    'sql': drop_sql + add_sql
                }).execute()
                print("   ✅ Alternative method successful")
                
            except Exception as alt_error:
                print(f"   ❌ Alternative method also failed: {alt_error}")
                raise alt_error
        
        # Test the fix
        print("\n🧪 Testing the constraint fix...")
        try:
            # Try to insert a test message with the problematic role
            test_data = {
                'role': 'assistant_response_end',
                'content': '{"test": "constraint fix"}',
                'thread_id': '00000000-0000-0000-0000-000000000000',
                'metadata': {}
            }
            
            # This should not fail now
            result = await supabase.table('messages').insert(test_data).execute()
            
            # Clean up the test record
            if result.data:
                test_id = result.data[0]['id']
                await supabase.table('messages').delete().eq('id', test_id).execute()
                print("   ✅ Constraint test successful - assistant_response_end role accepted!")
            
        except Exception as test_error:
            if "messages_role_check" in str(test_error):
                print(f"   ❌ Constraint still failing: {test_error}")
                return False
            else:
                print(f"   ⚠️  Test failed for other reason (probably OK): {test_error}")
        
        print("\n🎉 Role constraint fix completed successfully!")
        print("   The application should now be able to save messages with all role types.")
        print("   Including: assistant_response_end, status, tool, etc.")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing role constraint: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Show manual fix instructions
        print("\n📝 MANUAL FIX REQUIRED:")
        print("   Go to your Supabase dashboard > SQL Editor and run:")
        print("   " + "=" * 70)
        print("   ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_role_check;")
        print("   ")
        print("   ALTER TABLE public.messages ADD CONSTRAINT messages_role_check")
        print("   CHECK (role IN (")
        print("       'user', 'assistant', 'system', 'tool', 'status',")
        print("       'assistant_response_end', 'assistant_response_start',")
        print("       'tool_result', 'error'")
        print("   ));")
        print("   " + "=" * 70)
        
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_role_constraint())
    if success:
        print("\n🚀 Ready to test! Try sending a message in the application.")
        print("   The 'assistant_response_end' role error should be resolved.")
    else:
        print("\n⚠️  Manual intervention required. See instructions above.")
        exit(1)
