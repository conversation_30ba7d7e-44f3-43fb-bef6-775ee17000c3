"""
Security middleware for the Suna application.
Provides rate limiting, security headers, and request validation.
"""

import time
import async<PERSON>
from typing import Dict, <PERSON><PERSON>, Tuple
from collections import defaultdict, deque
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from utils.logger import logger
import ipaddress


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware using sliding window algorithm.
    Tracks requests per IP address and endpoint.
    """
    
    def __init__(self, app, requests_per_minute: int = 60, requests_per_hour: int = 1000):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        
        # Store request timestamps per IP
        self.request_history: Dict[str, deque] = defaultdict(lambda: deque())
        
        # Cleanup task
        self._cleanup_task = None
        
    async def dispatch(self, request: Request, call_next):
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Check rate limits
        if not self._check_rate_limit(client_ip):
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "detail": f"Maximum {self.requests_per_minute} requests per minute allowed"
                },
                headers={
                    "Retry-After": "60",
                    "X-RateLimit-Limit": str(self.requests_per_minute),
                    "X-RateLimit-Remaining": "0"
                }
            )
        
        # Record this request
        self._record_request(client_ip)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = self._get_remaining_requests(client_ip)
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers."""
        # Check for forwarded headers (for reverse proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection IP
        return request.client.host if request.client else "unknown"
    
    def _check_rate_limit(self, client_ip: str) -> bool:
        """Check if client IP is within rate limits."""
        current_time = time.time()
        request_times = self.request_history[client_ip]
        
        # Remove old requests (older than 1 hour)
        while request_times and current_time - request_times[0] > 3600:
            request_times.popleft()
        
        # Check minute limit
        minute_ago = current_time - 60
        minute_requests = sum(1 for t in request_times if t > minute_ago)
        
        if minute_requests >= self.requests_per_minute:
            return False
        
        # Check hour limit
        if len(request_times) >= self.requests_per_hour:
            return False
        
        return True
    
    def _record_request(self, client_ip: str):
        """Record a request timestamp for the client IP."""
        current_time = time.time()
        self.request_history[client_ip].append(current_time)
    
    def _get_remaining_requests(self, client_ip: str) -> int:
        """Get remaining requests for the current minute."""
        current_time = time.time()
        minute_ago = current_time - 60
        request_times = self.request_history[client_ip]
        
        minute_requests = sum(1 for t in request_times if t > minute_ago)
        return max(0, self.requests_per_minute - minute_requests)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to all responses.
    """
    
    def __init__(self, app):
        super().__init__(app)
        
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Add security headers
        security_headers = {
            # Prevent clickjacking
            "X-Frame-Options": "DENY",
            
            # Prevent MIME type sniffing
            "X-Content-Type-Options": "nosniff",
            
            # Enable XSS protection
            "X-XSS-Protection": "1; mode=block",
            
            # Referrer policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Content Security Policy (basic)
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self' https:; "
                "font-src 'self' data:; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "form-action 'self'"
            ),
            
            # Strict Transport Security (HTTPS only)
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            
            # Permissions Policy
            "Permissions-Policy": (
                "camera=(), microphone=(), geolocation=(), "
                "payment=(), usb=(), magnetometer=(), gyroscope=()"
            )
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for request validation and security checks.
    """
    
    def __init__(self, app, max_request_size: int = 10 * 1024 * 1024):  # 10MB default
        super().__init__(app)
        self.max_request_size = max_request_size
        
    async def dispatch(self, request: Request, call_next):
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                if size > self.max_request_size:
                    logger.warning(f"Request too large: {size} bytes from {request.client.host}")
                    return JSONResponse(
                        status_code=413,
                        content={
                            "error": "Request too large",
                            "detail": f"Maximum request size is {self.max_request_size} bytes"
                        }
                    )
            except ValueError:
                pass
        
        # Check for suspicious patterns in URL
        path = str(request.url.path)
        suspicious_patterns = [
            "../", "..\\", "<script", "javascript:", "data:text/html",
            "eval(", "expression(", "vbscript:", "onload=", "onerror="
        ]
        
        for pattern in suspicious_patterns:
            if pattern.lower() in path.lower():
                logger.warning(f"Suspicious request pattern detected: {pattern} in {path}")
                return JSONResponse(
                    status_code=400,
                    content={
                        "error": "Invalid request",
                        "detail": "Request contains suspicious patterns"
                    }
                )
        
        # Check User-Agent header (basic bot detection)
        user_agent = request.headers.get("user-agent", "").lower()
        if not user_agent or len(user_agent) < 10:
            logger.info(f"Request with suspicious User-Agent: {user_agent}")
        
        return await call_next(request)


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """
    Optional IP whitelist middleware for admin endpoints.
    """
    
    def __init__(self, app, admin_endpoints: list = None, allowed_ips: list = None):
        super().__init__(app)
        self.admin_endpoints = admin_endpoints or ["/api/admin"]
        self.allowed_networks = []
        
        if allowed_ips:
            for ip in allowed_ips:
                try:
                    self.allowed_networks.append(ipaddress.ip_network(ip, strict=False))
                except ValueError:
                    logger.warning(f"Invalid IP/network in whitelist: {ip}")
    
    async def dispatch(self, request: Request, call_next):
        # Check if this is an admin endpoint
        path = str(request.url.path)
        is_admin_endpoint = any(path.startswith(endpoint) for endpoint in self.admin_endpoints)
        
        if is_admin_endpoint and self.allowed_networks:
            client_ip = self._get_client_ip(request)
            
            try:
                client_addr = ipaddress.ip_address(client_ip)
                allowed = any(client_addr in network for network in self.allowed_networks)
                
                if not allowed:
                    logger.warning(f"Unauthorized admin access attempt from {client_ip}")
                    return JSONResponse(
                        status_code=403,
                        content={
                            "error": "Access denied",
                            "detail": "IP address not authorized for admin access"
                        }
                    )
            except ValueError:
                logger.warning(f"Invalid client IP: {client_ip}")
                return JSONResponse(
                    status_code=403,
                    content={"error": "Access denied", "detail": "Invalid client IP"}
                )
        
        return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
