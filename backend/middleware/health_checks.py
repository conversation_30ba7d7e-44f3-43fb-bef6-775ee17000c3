"""
Comprehensive health checks for production monitoring
"""

import asyncio
import time
from typing import Dict, Any, List
from fastapi import HTT<PERSON>Exception
from utils.logger import logger
from utils.config import config
from services.supabase import DBConnection
from services.redis import client as redis_client
import litellm

class HealthChecker:
    """Production-grade health checker"""
    
    def __init__(self):
        self.checks = {
            "database": self._check_database,
            "redis": self._check_redis,
            "llm_service": self._check_llm_service,
            "disk_space": self._check_disk_space,
            "memory": self._check_memory,
        }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks and return comprehensive status"""
        start_time = time.time()
        results = {}
        overall_status = "healthy"
        
        for check_name, check_func in self.checks.items():
            try:
                check_start = time.time()
                result = await check_func()
                check_duration = time.time() - check_start
                
                results[check_name] = {
                    "status": "healthy" if result["healthy"] else "unhealthy",
                    "details": result.get("details", {}),
                    "response_time_ms": round(check_duration * 1000, 2),
                    "timestamp": time.time()
                }
                
                if not result["healthy"]:
                    overall_status = "unhealthy"
                    
            except Exception as e:
                logger.error(f"Health check failed for {check_name}: {e}")
                results[check_name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "response_time_ms": 0,
                    "timestamp": time.time()
                }
                overall_status = "unhealthy"
        
        total_duration = time.time() - start_time
        
        return {
            "status": overall_status,
            "timestamp": time.time(),
            "total_response_time_ms": round(total_duration * 1000, 2),
            "checks": results,
            "version": "1.0.0",
            "environment": str(config.ENV_MODE.value) if hasattr(config.ENV_MODE, 'value') else str(config.ENV_MODE)
        }
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity and basic operations"""
        try:
            # Get database connection
            db = DBConnection()
            client = await db.client

            # Test basic connection
            response = await client.table("threads").select("id").limit(1).execute()

            # Check if we can perform basic operations
            healthy = response.data is not None

            return {
                "healthy": healthy,
                "details": {
                    "connection": "ok",
                    "query_test": "passed" if healthy else "failed"
                }
            }
        except Exception as e:
            return {
                "healthy": False,
                "details": {
                    "error": str(e),
                    "connection": "failed"
                }
            }
    
    async def _check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity and performance"""
        try:
            if not redis_client:
                return {
                    "healthy": False,
                    "details": {"error": "Redis client not initialized"}
                }
            
            # Test ping
            start_time = time.time()
            await redis_client.ping()
            ping_time = time.time() - start_time
            
            # Test set/get operation
            test_key = "health_check_test"
            test_value = str(time.time())
            
            await redis_client.set(test_key, test_value, ex=10)
            retrieved_value = await redis_client.get(test_key)
            await redis_client.delete(test_key)
            
            healthy = retrieved_value == test_value
            
            return {
                "healthy": healthy,
                "details": {
                    "ping_time_ms": round(ping_time * 1000, 2),
                    "set_get_test": "passed" if healthy else "failed"
                }
            }
        except Exception as e:
            return {
                "healthy": False,
                "details": {"error": str(e)}
            }
    
    async def _check_llm_service(self) -> Dict[str, Any]:
        """Check LLM service availability"""
        try:
            # Test with a simple completion
            start_time = time.time()
            
            response = await litellm.acompletion(
                model=f"groq/{config.MODEL_TO_USE}",
                messages=[{"role": "user", "content": "ping"}],
                max_tokens=5,
                timeout=10
            )
            
            response_time = time.time() - start_time
            healthy = response and hasattr(response, 'choices') and len(response.choices) > 0
            
            return {
                "healthy": healthy,
                "details": {
                    "model": config.MODEL_TO_USE,
                    "response_time_ms": round(response_time * 1000, 2),
                    "test": "passed" if healthy else "failed"
                }
            }
        except Exception as e:
            return {
                "healthy": False,
                "details": {
                    "model": config.MODEL_TO_USE,
                    "error": str(e)
                }
            }
    
    async def _check_disk_space(self) -> Dict[str, Any]:
        """Check available disk space"""
        try:
            import shutil
            
            total, used, free = shutil.disk_usage("/")
            free_percent = (free / total) * 100
            
            # Consider unhealthy if less than 10% free space
            healthy = free_percent > 10
            
            return {
                "healthy": healthy,
                "details": {
                    "total_gb": round(total / (1024**3), 2),
                    "used_gb": round(used / (1024**3), 2),
                    "free_gb": round(free / (1024**3), 2),
                    "free_percent": round(free_percent, 2)
                }
            }
        except Exception as e:
            return {
                "healthy": False,
                "details": {"error": str(e)}
            }
    
    async def _check_memory(self) -> Dict[str, Any]:
        """Check memory usage"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            available_percent = memory.available / memory.total * 100
            
            # Consider unhealthy if less than 10% memory available
            healthy = available_percent > 10
            
            return {
                "healthy": healthy,
                "details": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_percent": round(memory.percent, 2),
                    "available_percent": round(available_percent, 2)
                }
            }
        except ImportError:
            # psutil not available, skip this check
            return {
                "healthy": True,
                "details": {"note": "Memory monitoring not available (psutil not installed)"}
            }
        except Exception as e:
            return {
                "healthy": False,
                "details": {"error": str(e)}
            }

# Global health checker instance
health_checker = HealthChecker()

async def get_health_status() -> Dict[str, Any]:
    """Get comprehensive health status"""
    return await health_checker.run_all_checks()

async def get_readiness_status() -> Dict[str, Any]:
    """Get readiness status (essential services only)"""
    essential_checks = ["database", "redis", "llm_service"]
    results = {}
    overall_status = "ready"
    
    for check_name in essential_checks:
        if check_name in health_checker.checks:
            try:
                result = await health_checker.checks[check_name]()
                results[check_name] = {
                    "status": "ready" if result["healthy"] else "not_ready",
                    "details": result.get("details", {})
                }
                if not result["healthy"]:
                    overall_status = "not_ready"
            except Exception as e:
                results[check_name] = {
                    "status": "not_ready",
                    "error": str(e)
                }
                overall_status = "not_ready"
    
    return {
        "status": overall_status,
        "timestamp": time.time(),
        "checks": results
    }
