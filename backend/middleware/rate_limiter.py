"""
Rate limiting middleware for production API protection
"""

import time
from typing import Dict, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
import redis.asyncio as redis
from utils.config import config
from utils.logger import logger

class RateLimiter:
    """Redis-based rate limiter for API endpoints"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.enabled = getattr(config, 'RATE_LIMIT_ENABLED', True)
        self.requests_per_minute = getattr(config, 'RATE_LIMIT_REQUESTS_PER_MINUTE', 60)
        self.burst_limit = getattr(config, 'RATE_LIMIT_BURST', 10)
        
    async def init_redis(self):
        """Initialize Redis connection for rate limiting"""
        if not self.enabled:
            return
            
        try:
            self.redis_client = redis.Redis(
                host=config.REDIS_HOST,
                port=config.REDIS_PORT,
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("Rate limiter Redis connection initialized")
        except Exception as e:
            logger.error(f"Failed to initialize rate limiter Redis: {e}")
            self.enabled = False
    
    async def is_allowed(self, client_ip: str, endpoint: str = "default") -> tuple[bool, Dict]:
        """
        Check if request is allowed based on rate limits
        Returns (is_allowed, rate_limit_info)
        """
        if not self.enabled or not self.redis_client:
            return True, {}
        
        try:
            current_time = int(time.time())
            window_start = current_time - 60  # 1-minute window
            
            # Create unique key for this client and endpoint
            key = f"rate_limit:{client_ip}:{endpoint}"
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            # Remove old entries outside the window
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(key, 120)  # 2 minutes to be safe
            
            results = await pipe.execute()
            current_count = results[1]
            
            # Check if limit exceeded
            if current_count >= self.requests_per_minute:
                rate_limit_info = {
                    "limit": self.requests_per_minute,
                    "remaining": 0,
                    "reset_time": window_start + 60,
                    "retry_after": 60
                }
                return False, rate_limit_info
            
            rate_limit_info = {
                "limit": self.requests_per_minute,
                "remaining": self.requests_per_minute - current_count - 1,
                "reset_time": window_start + 60
            }
            
            return True, rate_limit_info
            
        except Exception as e:
            logger.error(f"Rate limiter error: {e}")
            # Fail open - allow request if rate limiter fails
            return True, {}
    
    async def check_burst_limit(self, client_ip: str) -> bool:
        """Check burst limit (requests in last 10 seconds)"""
        if not self.enabled or not self.redis_client:
            return True
        
        try:
            current_time = int(time.time())
            burst_window_start = current_time - 10  # 10-second window
            
            key = f"burst_limit:{client_ip}"
            
            # Remove old entries and count current
            pipe = self.redis_client.pipeline()
            pipe.zremrangebyscore(key, 0, burst_window_start)
            pipe.zcard(key)
            pipe.zadd(key, {str(current_time): current_time})
            pipe.expire(key, 30)
            
            results = await pipe.execute()
            current_count = results[1]
            
            return current_count < self.burst_limit
            
        except Exception as e:
            logger.error(f"Burst limit check error: {e}")
            return True

# Global rate limiter instance
rate_limiter = RateLimiter()

async def rate_limit_middleware(request: Request, call_next):
    """FastAPI middleware for rate limiting"""
    
    # Skip rate limiting for health checks
    if request.url.path in ["/api/health", "/health", "/ping"]:
        return await call_next(request)
    
    # Get client IP
    client_ip = request.client.host
    if "x-forwarded-for" in request.headers:
        client_ip = request.headers["x-forwarded-for"].split(",")[0].strip()
    elif "x-real-ip" in request.headers:
        client_ip = request.headers["x-real-ip"]
    
    # Check burst limit first
    if not await rate_limiter.check_burst_limit(client_ip):
        logger.warning(f"Burst limit exceeded for IP: {client_ip}")
        return JSONResponse(
            status_code=429,
            content={
                "error": "Too many requests",
                "message": "Burst limit exceeded. Please slow down.",
                "retry_after": 10
            },
            headers={"Retry-After": "10"}
        )
    
    # Check main rate limit
    endpoint = request.url.path
    is_allowed, rate_info = await rate_limiter.is_allowed(client_ip, endpoint)
    
    if not is_allowed:
        logger.warning(f"Rate limit exceeded for IP: {client_ip}, endpoint: {endpoint}")
        return JSONResponse(
            status_code=429,
            content={
                "error": "Too many requests",
                "message": f"Rate limit exceeded. Limit: {rate_info['limit']} requests per minute.",
                "retry_after": rate_info.get("retry_after", 60)
            },
            headers={
                "Retry-After": str(rate_info.get("retry_after", 60)),
                "X-RateLimit-Limit": str(rate_info["limit"]),
                "X-RateLimit-Remaining": str(rate_info["remaining"]),
                "X-RateLimit-Reset": str(rate_info["reset_time"])
            }
        )
    
    # Add rate limit headers to response
    response = await call_next(request)
    
    if rate_info:
        response.headers["X-RateLimit-Limit"] = str(rate_info["limit"])
        response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
        response.headers["X-RateLimit-Reset"] = str(rate_info["reset_time"])
    
    return response
