"""
Admin API endpoints for superuser dashboard functionality
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Request
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime, timezone
import uuid

from utils.auth_utils import get_current_user_id_from_jwt
from services.supabase import DBConnection
from utils.logger import logger

# Initialize database connection
db = DBConnection()

async def get_supabase_client():
    """Get the Supabase client instance"""
    return await db.client

router = APIRouter(prefix="/admin", tags=["admin"])

# =====================================================
# PYDANTIC MODELS
# =====================================================

class UserSummary(BaseModel):
    user_id: str
    email: str
    user_created_at: datetime
    last_sign_in_at: Optional[datetime]
    thread_count: int
    message_count: int
    project_count: int
    agent_count: int
    last_activity_at: Optional[datetime]

class AdminActivity(BaseModel):
    id: str
    admin_user_id: str
    action: str
    target_type: Optional[str]
    target_id: Optional[str]
    details: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime

class SystemStats(BaseModel):
    total_users: int
    active_users_24h: int
    active_users_7d: int
    total_threads: int
    total_messages: int
    total_projects: int
    total_agents: int
    total_agent_runs: int

class SuperuserInfo(BaseModel):
    id: str
    user_id: str
    email: str
    created_at: datetime
    is_active: bool
    permissions: Dict[str, Any]
    last_login_at: Optional[datetime]

# =====================================================
# HELPER FUNCTIONS
# =====================================================

async def verify_superuser(request: Request):
    """Verify that the current user is a superuser"""
    try:
        # Get user ID from JWT
        user_id = await get_current_user_id_from_jwt(request)
        if not user_id:
            raise HTTPException(status_code=401, detail="Authentication required")

        supabase = await get_supabase_client()

        # Get user email from superusers table first
        superuser_result = await supabase.table('superusers').select('email').eq('user_id', user_id).execute()

        if not superuser_result.data:
            raise HTTPException(status_code=403, detail="Access denied. You do not have superuser privileges.")

        user_email = superuser_result.data[0]['email']

        # Check if user is a superuser using explicit email
        result = await supabase.rpc('is_superuser', {'user_email': user_email}).execute()

        if not result.data:
            raise HTTPException(status_code=403, detail="Access denied. You do not have superuser privileges.")

        return {"user_id": user_id, "email": user_email}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying superuser: {e}")
        raise HTTPException(status_code=500, detail="Authentication error")

async def log_admin_action(action: str, target_type: str = None, target_id: str = None, details: dict = None):
    """Log admin activity"""
    try:
        supabase = await get_supabase_client()
        await supabase.rpc('log_admin_activity', {
            'action_name': action,
            'target_type_param': target_type,
            'target_id_param': target_id,
            'details_param': details or {}
        }).execute()
    except Exception as e:
        logger.warning(f"Failed to log admin activity: {e}")

# =====================================================
# ADMIN ENDPOINTS
# =====================================================

@router.get("/verify", response_model=dict)
async def verify_admin_access(request: Request):
    """Verify admin access and return user info"""
    current_user = await verify_superuser(request)
    await log_admin_action("admin_dashboard_access")

    return {
        "is_superuser": True,
        "user": current_user,
        "permissions": [
            "view_all_users",
            "view_all_threads",
            "view_all_messages",
            "view_all_projects",
            "view_all_agents",
            "view_system_stats",
            "manage_users",
            "view_billing"
        ]
    }

@router.get("/users", response_model=List[UserSummary])
async def get_all_users(
    request: Request,
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    search: Optional[str] = Query(None)
):
    """Get all users with activity summary"""
    current_user = await verify_superuser(request)
    supabase = await get_supabase_client()
    
    try:
        # Build query
        query = supabase.table('user_activity_summary').select('*')
        
        if search:
            query = query.ilike('email', f'%{search}%')
        
        query = query.order('user_created_at', desc=True).range(offset, offset + limit - 1)
        
        result = await query.execute()
        
        await log_admin_action("view_all_users", details={"search": search, "limit": limit, "offset": offset})
        
        return [UserSummary(**user) for user in result.data]
        
    except Exception as e:
        logger.error(f"Error fetching users: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch users")

@router.get("/stats", response_model=SystemStats)
async def get_system_stats(request: Request):
    """Get system-wide statistics"""
    current_user = await verify_superuser(request)
    supabase = await get_supabase_client()
    
    try:
        # Get various counts
        stats = {}
        
        # Total users
        users_result = await supabase.table('auth.users').select('id', count='exact').execute()
        stats['total_users'] = users_result.count or 0
        
        # Active users (last 24h and 7d)
        active_24h = await supabase.rpc('exec_sql', {
            'sql': """
            SELECT COUNT(*) FROM auth.users 
            WHERE last_sign_in_at > NOW() - INTERVAL '24 hours'
            """
        }).execute()
        stats['active_users_24h'] = active_24h.data[0]['count'] if active_24h.data else 0
        
        active_7d = await supabase.rpc('exec_sql', {
            'sql': """
            SELECT COUNT(*) FROM auth.users 
            WHERE last_sign_in_at > NOW() - INTERVAL '7 days'
            """
        }).execute()
        stats['active_users_7d'] = active_7d.data[0]['count'] if active_7d.data else 0
        
        # Other counts
        threads_result = await supabase.table('threads').select('id', count='exact').execute()
        stats['total_threads'] = threads_result.count or 0
        
        messages_result = await supabase.table('messages').select('id', count='exact').execute()
        stats['total_messages'] = messages_result.count or 0
        
        projects_result = await supabase.table('projects').select('id', count='exact').execute()
        stats['total_projects'] = projects_result.count or 0
        
        agents_result = await supabase.table('agents').select('id', count='exact').execute()
        stats['total_agents'] = agents_result.count or 0
        
        agent_runs_result = await supabase.table('agent_runs').select('id', count='exact').execute()
        stats['total_agent_runs'] = agent_runs_result.count or 0
        
        await log_admin_action("view_system_stats")
        
        return SystemStats(**stats)
        
    except Exception as e:
        logger.error(f"Error fetching system stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch system statistics")

@router.get("/activity", response_model=List[AdminActivity])
async def get_admin_activity(
    request: Request,
    limit: int = Query(50, le=200),
    offset: int = Query(0, ge=0)
):
    """Get admin activity log"""
    current_user = await verify_superuser(request)
    supabase = await get_supabase_client()
    
    try:
        result = await supabase.table('admin_activity_log').select('*').order('created_at', desc=True).range(offset, offset + limit - 1).execute()
        
        return [AdminActivity(**activity) for activity in result.data]
        
    except Exception as e:
        logger.error(f"Error fetching admin activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch admin activity")

@router.get("/user/{user_id}/details")
async def get_user_details(
    user_id: str,
    request: Request
):
    """Get detailed information about a specific user"""
    current_user = await verify_superuser(request)
    supabase = await get_supabase_client()
    
    try:
        # Get user info from public users table
        user_result = await supabase.table('users').select('*').eq('id', user_id).execute()
        if not user_result.data:
            raise HTTPException(status_code=404, detail="User not found")

        user = user_result.data[0]
        
        # Get user's threads
        threads_result = await supabase.table('threads').select('*').eq('account_id', user_id).limit(10).execute()
        
        # Get user's recent messages
        messages_result = await supabase.rpc('exec_sql', {
            'sql': f"""
            SELECT m.* FROM messages m
            JOIN threads t ON m.thread_id = t.id
            WHERE t.account_id = '{user_id}'
            ORDER BY m.created_at DESC
            LIMIT 10
            """
        }).execute()
        
        await log_admin_action("view_user_details", "user", user_id)
        
        return {
            "user": user,
            "recent_threads": threads_result.data,
            "recent_messages": messages_result.data if messages_result.data else []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching user details: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch user details")

@router.get("/superusers", response_model=List[SuperuserInfo])
async def get_superusers(request: Request):
    """Get all superusers"""
    current_user = await verify_superuser(request)
    supabase = await get_supabase_client()
    
    try:
        result = await supabase.table('superusers').select('*').order('created_at', desc=True).execute()
        
        await log_admin_action("view_superusers")
        
        return [SuperuserInfo(**superuser) for superuser in result.data]
        
    except Exception as e:
        logger.error(f"Error fetching superusers: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch superusers")

@router.post("/user/{user_id}/disable")
async def disable_user(
    user_id: str,
    request: Request
):
    """Disable a user account (admin action)"""
    current_user = await verify_superuser(request)
    supabase = await get_supabase_client()
    
    try:
        # Note: Supabase doesn't allow direct user disabling via API
        # This would typically be done through the Supabase dashboard
        # For now, we'll log the action and return a message
        
        await log_admin_action("disable_user_attempt", "user", user_id, {
            "note": "User disable attempted - requires Supabase dashboard action"
        })
        
        return {
            "message": "User disable logged. Complete action in Supabase dashboard.",
            "user_id": user_id,
            "action_required": "Go to Supabase Auth > Users and disable the user manually"
        }
        
    except Exception as e:
        logger.error(f"Error disabling user: {e}")
        raise HTTPException(status_code=500, detail="Failed to disable user")
