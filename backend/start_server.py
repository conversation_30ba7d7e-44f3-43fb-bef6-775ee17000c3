#!/usr/bin/env python3
"""
Startup script that patches SSL verification before importing litellm
"""
import os
import ssl
import sys

# Disable SSL verification for development
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['SSL_VERIFY'] = 'false'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# Monkey patch ssl.create_default_context to avoid certificate issues
original_create_default_context = ssl.create_default_context

def patched_create_default_context(purpose=ssl.Purpose.SERVER_AUTH, cafile=None, capath=None, cadata=None):
    """Patched SSL context creation that disables verification"""
    context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

# Apply the patch before any imports
ssl.create_default_context = patched_create_default_context

# Now import httpx and patch it too
import httpx
import httpx._config

def patched_httpx_create_ssl_context(verify=None, cert=None, trust_env=True):
    """Patched httpx SSL context creation"""
    return patched_create_default_context()

httpx._config.create_ssl_context = patched_httpx_create_ssl_context

# Now import and run the app
if __name__ == "__main__":
    import uvicorn
    from api import app

    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
