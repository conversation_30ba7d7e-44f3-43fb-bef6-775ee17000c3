{"summary": {"total_tests": 14, "passed": 14, "failed": 0, "skipped": 0, "success_rate": 100.0, "average_response_time": 0.027963995933532715}, "test_results": [{"name": "health_check", "result": "PASS", "status_code": 200, "response_time": 0.2562999725341797, "error_message": null}, {"name": "feature_flags_custom_agents", "result": "PASS", "status_code": 200, "response_time": 0.03158688545227051, "error_message": null}, {"name": "billing_available_models", "result": "PASS", "status_code": 401, "response_time": 0.03833484649658203, "error_message": null}, {"name": "billing_subscription", "result": "PASS", "status_code": 401, "response_time": 0.003170013427734375, "error_message": null}, {"name": "agents_list", "result": "PASS", "status_code": 401, "response_time": 0.003763914108276367, "error_message": null}, {"name": "oauth_available_integrations", "result": "PASS", "status_code": 200, "response_time": 0.005216121673583984, "error_message": null}, {"name": "mcp_popular_servers", "result": "PASS", "status_code": 401, "response_time": 0.004024028778076172, "error_message": null}, {"name": "threads_list", "result": "PASS", "status_code": 401, "response_time": 0.007670879364013672, "error_message": null}, {"name": "projects_list", "result": "PASS", "status_code": 401, "response_time": 0.004758119583129883, "error_message": null}, {"name": "knowledge_base_agents", "result": "PASS", "status_code": 401, "response_time": 0.016328811645507812, "error_message": null}, {"name": "templates_marketplace", "result": "PASS", "status_code": 401, "response_time": 0.008277177810668945, "error_message": null}, {"name": "invalid_endpoint", "result": "PASS", "status_code": 404, "response_time": 0.004609107971191406, "error_message": null}, {"name": "invalid_method", "result": "PASS", "status_code": 405, "response_time": 0.004758119583129883, "error_message": null}, {"name": "large_request_test", "result": "PASS", "status_code": 405, "response_time": 0.0026979446411132812, "error_message": null}]}