{"summary": {"total_tests": 7, "passed": 6, "partial": 1, "failed": 0, "security_score": 92.85714285714286}, "test_results": [{"test": "security_headers", "status": "PASS", "present_headers": ["X-Frame-Options", "X-Content-Type-Options", "X-XSS-Protection", "Referrer-Policy", "Content-Security-Policy"], "missing_headers": [], "total_security_headers": 5}, {"test": "rate_limiting", "status": "PARTIAL", "requests_made": 113, "rate_limited": false, "note": "Rate limit not triggered (may need more requests)"}, {"test": "request_size_limits", "status": "PASS", "response_status": 405, "note": "Request size validation working (got expected error)"}, {"test": "xss_protection", "status": "PASS", "total_payloads": 5, "detected_count": 5, "results": [{"payload": "<script>alert('xss')</script>", "detected": true}, {"payload": "javascript:alert('xss')", "detected": true}, {"payload": "<img src=x onerror=alert('xss')>", "detected": true}, {"payload": "<iframe src='javascript:alert(1)'></iframe>", "detected": true}, {"payload": "eval('alert(1)')", "detected": true}]}, {"test": "sql_injection_protection", "status": "PASS", "total_payloads": 6, "detected_count": 6, "results": [{"payload": "' OR '1'='1", "detected": true}, {"payload": "1; DROP TABLE users;--", "detected": true}, {"payload": "UNION SELECT * FROM users", "detected": true}, {"payload": "' AND 1=1--", "detected": true}, {"payload": "admin'--", "detected": true}, {"payload": "1' OR 1=1#", "detected": true}]}, {"test": "path_traversal_protection", "status": "PASS", "total_payloads": 5, "detected_count": 5, "results": [{"payload": "../../../etc/passwd", "detected": true}, {"payload": "..\\..\\..\\windows\\system32", "detected": true}, {"payload": "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd", "detected": true}, {"payload": "....//....//....//etc/passwd", "detected": true}, {"payload": "..%2f..%2f..%2fetc%2fpasswd", "detected": true}]}, {"test": "input_validation", "status": "PASS", "total_cases": 6, "passed_cases": 6, "results": [{"input": "<EMAIL>", "type": "email", "expected": true, "actual": true, "passed": true}, {"input": "invalid-email", "type": "email", "expected": false, "actual": false, "passed": true}, {"input": "550e8400-e29b-41d4-a716-************", "type": "uuid", "expected": true, "actual": true, "passed": true}, {"input": "invalid-uuid", "type": "uuid", "expected": false, "actual": false, "passed": true}, {"input": "https://example.com", "type": "url", "expected": true, "actual": true, "passed": true}, {"input": "javascript:alert(1)", "type": "url", "expected": false, "actual": false, "passed": true}]}]}