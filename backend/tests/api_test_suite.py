#!/usr/bin/env python3
"""
Comprehensive API Test Suite for Suna Application
Tests all major endpoints for functionality, performance, and reliability.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestResult(Enum):
    PASS = "PASS"
    FAIL = "FAIL"
    SKIP = "SKIP"

@dataclass
class ApiTestCase:
    name: str
    method: str
    endpoint: str
    headers: Optional[Dict[str, str]] = None
    data: Optional[Dict[str, Any]] = None
    expected_status: int = 200
    requires_auth: bool = False
    description: str = ""

@dataclass
class TestReport:
    test_name: str
    result: TestResult
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    response_data: Optional[Dict[str, Any]] = None

class SunaApiTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
        self.auth_token: Optional[str] = None
        self.test_results: List[TestReport] = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def get_test_cases(self) -> List[ApiTestCase]:
        """Define all API test cases"""
        return [
            # Health and System Endpoints
            ApiTestCase(
                name="health_check",
                method="GET",
                endpoint="/api/health",
                description="Basic health check endpoint"
            ),
            
            # Feature Flags
            ApiTestCase(
                name="feature_flags_custom_agents",
                method="GET", 
                endpoint="/api/feature-flags/custom_agents",
                description="Check custom agents feature flag"
            ),
            
            # Billing Endpoints
            ApiTestCase(
                name="billing_available_models",
                method="GET",
                endpoint="/api/billing/available-models",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="Get available billing models"
            ),
            
            ApiTestCase(
                name="billing_subscription",
                method="GET",
                endpoint="/api/billing/subscription",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="Get user subscription status"
            ),
            
            # Agent Endpoints
            ApiTestCase(
                name="agents_list",
                method="GET",
                endpoint="/api/agents",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="List all agents for user"
            ),
            
            # OAuth Integrations
            ApiTestCase(
                name="oauth_available_integrations",
                method="GET",
                endpoint="/api/integrations/available",
                description="Get available OAuth integrations"
            ),
            
            # MCP Servers
            ApiTestCase(
                name="mcp_popular_servers",
                method="GET",
                endpoint="/api/mcp/popular-servers",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="Get popular MCP servers"
            ),
            
            # Threads and Projects (Backend API)
            ApiTestCase(
                name="threads_list",
                method="GET",
                endpoint="/api/threads",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="List all threads for user"
            ),

            ApiTestCase(
                name="projects_list",
                method="GET",
                endpoint="/api/projects",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="List all projects for user"
            ),

            # Knowledge Base
            ApiTestCase(
                name="knowledge_base_agents",
                method="GET",
                endpoint="/api/knowledge-base/agents",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="Get knowledge base for agents"
            ),

            # Templates
            ApiTestCase(
                name="templates_marketplace",
                method="GET",
                endpoint="/api/templates/marketplace",
                requires_auth=True,
                expected_status=401,  # Expected to fail without proper auth
                description="Get marketplace templates"
            ),

            # Invalid Endpoints (Error Testing)
            ApiTestCase(
                name="invalid_endpoint",
                method="GET",
                endpoint="/api/nonexistent",
                expected_status=404,
                description="Test 404 error handling"
            ),

            ApiTestCase(
                name="invalid_method",
                method="DELETE",
                endpoint="/api/health",
                expected_status=405,
                description="Test method not allowed error"
            ),

            # Large Request Test (test request size limits)
            ApiTestCase(
                name="large_request_test",
                method="POST",
                endpoint="/api/health",
                data={"large_data": "x" * 10000},  # Large payload to test limits
                expected_status=405,  # Method not allowed (health doesn't accept POST)
                description="Test large request handling"
            ),
        ]

    async def run_test_case(self, test_case: ApiTestCase) -> TestReport:
        """Execute a single test case"""
        start_time = time.time()
        
        try:
            # Prepare headers
            headers = test_case.headers or {}
            if test_case.requires_auth and self.auth_token:
                headers["Authorization"] = f"Bearer {self.auth_token}"
            
            # Make the request
            url = f"{self.base_url}{test_case.endpoint}"
            
            async with self.session.request(
                method=test_case.method,
                url=url,
                headers=headers,
                json=test_case.data,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                response_time = time.time() - start_time
                
                try:
                    response_data = await response.json()
                except:
                    response_data = {"text": await response.text()}
                
                # Check if test passed
                if response.status == test_case.expected_status:
                    return TestReport(
                        test_name=test_case.name,
                        result=TestResult.PASS,
                        status_code=response.status,
                        response_time=response_time,
                        response_data=response_data
                    )
                else:
                    return TestReport(
                        test_name=test_case.name,
                        result=TestResult.FAIL,
                        status_code=response.status,
                        response_time=response_time,
                        error_message=f"Expected status {test_case.expected_status}, got {response.status}",
                        response_data=response_data
                    )
                    
        except asyncio.TimeoutError:
            return TestReport(
                test_name=test_case.name,
                result=TestResult.FAIL,
                response_time=time.time() - start_time,
                error_message="Request timeout"
            )
        except Exception as e:
            return TestReport(
                test_name=test_case.name,
                result=TestResult.FAIL,
                response_time=time.time() - start_time,
                error_message=str(e)
            )

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all test cases and generate report"""
        print(f"🚀 Starting Suna API Test Suite")
        print(f"📍 Testing against: {self.base_url}")
        print("=" * 60)
        
        test_cases = self.get_test_cases()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"[{i:2d}/{len(test_cases)}] Testing {test_case.name}... ", end="", flush=True)
            
            result = await self.run_test_case(test_case)
            self.test_results.append(result)
            
            # Print result
            if result.result == TestResult.PASS:
                print(f"✅ PASS ({result.response_time:.3f}s)")
            elif result.result == TestResult.FAIL:
                print(f"❌ FAIL - {result.error_message}")
            else:
                print(f"⏭️  SKIP")
        
        return self.generate_report()

    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r.result == TestResult.PASS])
        failed_tests = len([r for r in self.test_results if r.result == TestResult.FAIL])
        skipped_tests = len([r for r in self.test_results if r.result == TestResult.SKIP])
        
        avg_response_time = sum(
            r.response_time for r in self.test_results 
            if r.response_time is not None
        ) / max(1, len([r for r in self.test_results if r.response_time is not None]))
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "skipped": skipped_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                "average_response_time": avg_response_time
            },
            "test_results": [
                {
                    "name": r.test_name,
                    "result": r.result.value,
                    "status_code": r.status_code,
                    "response_time": r.response_time,
                    "error_message": r.error_message
                }
                for r in self.test_results
            ]
        }
        
        return report

    def print_detailed_report(self, report: Dict[str, Any]):
        """Print detailed test report"""
        print("\n" + "=" * 60)
        print("📊 DETAILED TEST REPORT")
        print("=" * 60)
        
        summary = report["summary"]
        print(f"Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"⏭️  Skipped: {summary['skipped']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"⏱️  Average Response Time: {summary['average_response_time']:.3f}s")
        
        # Show failed tests
        failed_results = [r for r in self.test_results if r.result == TestResult.FAIL]
        if failed_results:
            print(f"\n❌ FAILED TESTS ({len(failed_results)}):")
            for result in failed_results:
                print(f"  • {result.test_name}: {result.error_message}")
        
        # Show performance insights
        slow_tests = [r for r in self.test_results if r.response_time and r.response_time > 1.0]
        if slow_tests:
            print(f"\n⚠️  SLOW TESTS (>1s):")
            for result in slow_tests:
                print(f"  • {result.test_name}: {result.response_time:.3f}s")

async def main():
    """Main test runner"""
    async with SunaApiTester() as tester:
        report = await tester.run_all_tests()
        tester.print_detailed_report(report)
        
        # Save report to file
        with open("api_test_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        print(f"\n💾 Full report saved to: api_test_report.json")
        
        # Exit with appropriate code
        if report["summary"]["failed"] > 0:
            sys.exit(1)
        else:
            print("\n🎉 All tests passed!")
            sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())
