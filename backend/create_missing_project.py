#!/usr/bin/env python3
"""
Create the missing project that the thread is referencing.
This fixes the file creation issue by ensuring the project exists.
"""

import os
import sys
from datetime import datetime, timezone
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from .env file
load_dotenv()

from supabase import create_client, Client

def create_missing_project():
    """Create the missing project in the database."""
    
    # Get environment variables
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not supabase_service_key:
        print("❌ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables")
        return False
    
    try:
        # Create Supabase client with service role key for admin operations
        supabase: Client = create_client(supabase_url, supabase_service_key)
        
        print("🔧 Creating missing project...")
        
        # The specific project ID that the thread is referencing
        missing_project_id = "********-cbca-432e-b598-a0b5ef281686"
        account_id = "cd4d9095-2924-414f-9609-f201507f965e"
        
        # Check if project already exists
        existing_project = supabase.table('projects').select('*').eq('project_id', missing_project_id).execute()
        
        if existing_project.data:
            print(f"✅ Project {missing_project_id} already exists")
            return True
        
        # Create the missing project
        project_data = {
            'project_id': missing_project_id,
            'account_id': account_id,
            'name': 'Auto-created Project',
            'description': 'Automatically created to fix missing project reference',
            'status': 'active',
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table('projects').insert(project_data).execute()
        
        if result.data:
            print(f"✅ Successfully created project {missing_project_id}")
            print(f"   Project details: {result.data[0]}")
            return True
        else:
            print(f"❌ Failed to create project: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating project: {e}")
        return False

def main():
    """Main function to create the missing project."""
    print("🚀 Starting missing project creation")
    print("=" * 50)
    
    if create_missing_project():
        print("\n✅ Missing project creation completed successfully!")
        print("\n🔧 Next steps:")
        print("   1. The project now exists in the database")
        print("   2. File creation should now work")
        print("   3. Test by sending a message that creates a file")
    else:
        print("\n❌ Missing project creation failed!")
        print("\n💡 You may need to:")
        print("   1. Check your Supabase credentials")
        print("   2. Verify database permissions")
        print("   3. Create the project manually in Supabase dashboard")

if __name__ == "__main__":
    main()
