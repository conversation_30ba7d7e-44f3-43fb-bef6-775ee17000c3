-- Fix missing project issue
-- Create the specific project that the thread is referencing

BEGIN;

-- Insert the missing project with the specific ID that the thread is referencing
INSERT INTO public.projects (
    project_id,
    account_id,
    name,
    description,
    status,
    created_at,
    updated_at
) VALUES (
    '********-cbca-432e-b598-a0b5ef281686'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Auto-created Project',
    'Automatically created to fix missing project reference',
    'active',
    NOW(),
    NOW()
) ON CONFLICT (project_id) DO NOTHING;

-- Verify the project was created
SELECT 
    project_id,
    name,
    account_id,
    created_at
FROM public.projects 
WHERE project_id = '********-cbca-432e-b598-a0b5ef281686'::uuid;

COMMIT;

SELECT 'Missing project fix completed!' as status;
