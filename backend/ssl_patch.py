"""
SSL patch to fix certificate issues with httpx and litellm.
This module patches the SSL context creation to avoid certificate verification errors.
"""

import ssl
import sys
import os

# Set environment variables to disable SSL verification
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['SSL_VERIFY'] = 'false'
os.environ['REQUESTS_CA_BUNDLE'] = ''
os.environ['CURL_CA_BUNDLE'] = ''

# Patch SSL context creation
original_create_default_context = ssl.create_default_context

def patched_create_default_context(purpose=ssl.Purpose.SERVER_AUTH, *, cafile=None, capath=None, cadata=None):
    """Create an SSL context that doesn't verify certificates."""
    context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

ssl.create_default_context = patched_create_default_context

# Patch httpx config if it's already imported
if 'httpx._config' in sys.modules:
    import httpx._config
    httpx._config.create_ssl_context = lambda verify=None, cert=None, trust_env=True: patched_create_default_context()

print("SSL patch applied successfully")
