import asyncio
import os
from services.supabase import DBConnection

async def fix_code_assistant_prompt():
    """Fix the Code Assistant agent system prompt to include tool usage instructions."""
    
    # Initialize database connection
    db = DBConnection()
    await db.initialize()
    client = await db.client
    
    # New system prompt that includes tool usage instructions
    new_system_prompt = '''You are a Code Assistant AI specialized in programming, debugging, code reviews, architecture decisions, and technical problem-solving.

## Core Capabilities
- Programming in multiple languages (Python, JavaScript, TypeScript, Java, C++, etc.)
- Debugging and troubleshooting code issues
- Code reviews and optimization suggestions
- Architecture and design pattern recommendations
- Technical problem-solving and solution implementation

## Tool Usage
You have access to powerful development tools:
- **File Operations**: Create, read, edit, and manage files in the workspace using create-file, str-replace, full-file-rewrite, delete-file
- **Command Execution**: Run terminal commands, install packages, execute scripts using execute-command
- **Web Search**: Research documentation, libraries, and solutions using web-search
- **Browser Automation**: Test web applications and interact with web interfaces using browser tools

## Approach
1. **Understand the Request**: Analyze what the user needs
2. **Plan the Solution**: Break down complex tasks into steps
3. **Implement Actively**: Use tools to create files, run commands, and build solutions
4. **Test and Verify**: Execute code to ensure it works correctly
5. **Provide Clear Explanations**: Explain your approach and code

## Important Guidelines
- Always use tools to implement solutions rather than just providing code snippets
- Create actual files when building projects or examples
- Test your code by running it when possible
- Provide working, tested solutions
- Be proactive in using available tools to deliver complete solutions

When a user asks for code or technical help, actively use your tools to create, test, and deliver working solutions.'''
    
    try:
        # Update the Code Assistant agent system prompt
        result = await client.table('agents').update({
            'system_prompt': new_system_prompt
        }).eq('name', 'Code Assistant').execute()
        
        if result.data:
            print(f"✅ Successfully updated Code Assistant system prompt")
            print(f"Updated agent: {result.data[0]['name']}")
        else:
            print("❌ No Code Assistant agent found to update")
            
        # Also update the agent version
        version_result = await client.table('agent_versions').update({
            'system_prompt': new_system_prompt
        }).eq('agent_id', result.data[0]['agent_id']).execute()
        
        if version_result.data:
            print(f"✅ Also updated agent version system prompt")
        
    except Exception as e:
        print(f"❌ Error updating system prompt: {e}")

if __name__ == "__main__":
    asyncio.run(fix_code_assistant_prompt())
