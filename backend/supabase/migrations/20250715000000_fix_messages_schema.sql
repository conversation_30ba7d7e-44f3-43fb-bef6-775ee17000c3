-- Fix messages table schema - ensure is_llm_message column exists
BEGIN;

-- Add is_llm_message column if it doesn't exist
ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;

-- Update existing user messages to have is_llm_message = false
UPDATE messages 
SET is_llm_message = false 
WHERE type = 'user' AND is_llm_message = true;

-- Update existing assistant/system messages to have is_llm_message = true
UPDATE messages 
SET is_llm_message = true 
WHERE type IN ('assistant', 'system', 'tool') AND is_llm_message = false;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);
CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);

COMMIT;
