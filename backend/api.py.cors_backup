# Apply SSL patch before any other imports
import ssl_patch

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Response, Depends, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import sentry
from middleware.security import (
    RateLimitMiddleware,
    SecurityHeadersMiddleware,
    RequestValidationMiddleware,
    IPWhitelistMiddleware
)
from contextlib import asynccontextmanager
from agentpress.thread_manager import ThreadManager
from services.supabase import DBConnection
from datetime import datetime, timezone
from dotenv import load_dotenv
from utils.config import config, EnvMode
import asyncio
from utils.logger import logger, structlog
import time
import traceback
from collections import OrderedDict
from typing import Dict, Any

from pydantic import BaseModel
import uuid
# Import the agent API module
from agent import api as agent_api
from agent import workflows as workflows_api
from sandbox import api as sandbox_api
from services import billing as billing_api
from flags import api as feature_flags_api
from services import transcription as transcription_api
import sys
from services import email_api
from triggers import api as triggers_api
from triggers import unified_oauth_api
from middleware.health_checks import get_health_status, get_readiness_status
from utils.env_validator import log_environment_status


load_dotenv()

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

# Initialize managers
db = DBConnection()
instance_id = "single"

# Rate limiter state
ip_tracker = OrderedDict()
MAX_CONCURRENT_IPS = 25

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f"Starting up FastAPI application with instance ID: {instance_id} in {config.ENV_MODE.value} mode")

    # Validate environment configuration
    log_environment_status()

    try:
        await db.initialize()
        
        agent_api.initialize(
            db,
            instance_id
        )
        
        # Initialize workflow API
        workflows_api.initialize(db, instance_id)
        
        sandbox_api.initialize(db)
        
        # Initialize Redis connection
        from services import redis
        try:
            await redis.initialize_async()
            logger.info("Redis connection initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {e}")
            # Continue without Redis - the application will handle Redis failures gracefully
        
        # Start background tasks
        # asyncio.create_task(agent_api.restore_running_agent_runs())
        
        # Initialize triggers API
        triggers_api.initialize(db)
        unified_oauth_api.initialize(db)
        
        # Initialize pipedream API
        pipedream_api.initialize(db)
        
        yield
        
        # Clean up agent resources
        logger.info("Cleaning up agent resources")
        await agent_api.cleanup()
        
        # Clean up Redis connection
        try:
            logger.info("Closing Redis connection")
            await redis.close()
            logger.info("Redis connection closed successfully")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
        
        # Clean up database connection
        logger.info("Disconnecting from database")
        await db.disconnect()
    except Exception as e:
        logger.error(f"Error during application startup: {e}")
        raise

app = FastAPI(lifespan=lifespan)

# Enhanced error handlers
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors with detailed messages."""
    logger.warning(f"Validation error for {request.method} {request.url.path}: {exc.errors()}")

    # Extract field names and error messages
    error_details = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        error_details.append(f"{field}: {message}")

    return JSONResponse(
        status_code=422,
        content={
            "detail": "Invalid request data",
            "errors": error_details,
            "message": "Please check your request parameters and try again."
        }
    )

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions with enhanced error messages."""
    logger.warning(f"HTTP exception for {request.method} {request.url.path}: {exc.status_code} - {exc.detail}")

    # Provide user-friendly messages for common status codes
    user_messages = {
        400: "Bad request. Please check your input and try again.",
        401: "Authentication required. Please sign in and try again.",
        403: "Access denied. You don't have permission to perform this action.",
        404: "The requested resource was not found.",
        405: "Method not allowed for this endpoint.",
        409: "Conflict. The resource already exists or is in use.",
        429: "Too many requests. Please wait a moment and try again.",
        500: "Internal server error. Our team has been notified.",
        502: "Service temporarily unavailable. Please try again later.",
        503: "Service temporarily unavailable. Please try again later."
    }

    user_message = user_messages.get(exc.status_code, "An error occurred while processing your request.")

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,
            "message": user_message,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle unexpected exceptions with proper logging and user-friendly messages."""
    request_id = getattr(request.state, 'request_id', 'unknown')

    # Log the full traceback for debugging
    logger.error(
        f"Unhandled exception for {request.method} {request.url.path} (request_id: {request_id}): {str(exc)}\n"
        f"Traceback: {traceback.format_exc()}"
    )

    # Don't expose internal error details to users in production
    if config.ENV_MODE == EnvMode.PRODUCTION:
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "message": "An unexpected error occurred. Our team has been notified and is working on a fix.",
                "request_id": request_id
            }
        )
    else:
        # In development, provide more details for debugging
        return JSONResponse(
            status_code=500,
            content={
                "detail": str(exc),
                "message": "An unexpected error occurred during development.",
                "request_id": request_id,
                "traceback": traceback.format_exc().split('\n') if config.ENV_MODE == EnvMode.LOCAL else None
            }
        )

@app.middleware("http")
async def log_requests_middleware(request: Request, call_next):
    structlog.contextvars.clear_contextvars()

    request_id = str(uuid.uuid4())
    start_time = time.time()
    client_ip = request.client.host if request.client else "unknown"
    method = request.method
    path = request.url.path
    query_params = str(request.query_params)

    # Store request_id in request state for error handlers
    request.state.request_id = request_id

    structlog.contextvars.bind_contextvars(
        request_id=request_id,
        client_ip=client_ip,
        method=method,
        path=path,
        query_params=query_params
    )

    # Log the incoming request
    logger.info(f"Request started: {method} {path} from {client_ip} | Query: {query_params}")
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        logger.debug(f"Request completed: {method} {path} | Status: {response.status_code} | Time: {process_time:.2f}s")
        return response
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"Request failed: {method} {path} | Error: {str(e)} | Time: {process_time:.2f}s")
        raise

# Define allowed origins based on environment
allowed_origins = ["https://www.suna.so", "https://suna.so"]
allow_origin_regex = None

# Add production domains
if config.ENV_MODE == EnvMode.PRODUCTION:
    allowed_origins.extend([
        "https://aicofounder.site",
        "https://www.aicofounder.site",
        "https://ai-co-founder.netlify.app",
        "https://www.ai-co-founder.netlify.app"
    ])

# Add local development origins
if config.ENV_MODE == EnvMode.LOCAL:
    allowed_origins.append("http://localhost:3000")
    allowed_origins.append("http://localhost:3001")  # Add support for port 3001
    # For local development, be more permissive
    allowed_origins.append("http://127.0.0.1:3000")
    allowed_origins.append("http://127.0.0.1:3001")

# Add staging-specific origins
if config.ENV_MODE == EnvMode.STAGING:
    allowed_origins.append("https://staging.suna.so")
    allowed_origins.append("http://localhost:3000")
    allowed_origins.append("http://localhost:3001")  # Add support for port 3001
    allow_origin_regex = r"https://suna-.*-prjcts\.vercel\.app"

# Log CORS configuration for debugging
logger.info(f"CORS allowed origins: {allowed_origins}")
logger.info(f"Environment mode: {config.ENV_MODE}")

# Add security middleware (order matters - add from innermost to outermost)
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_origin_regex=allow_origin_regex,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],  # Allow all headers for local development
)

# Add security headers middleware
app.add_middleware(SecurityHeadersMiddleware)

# Add request validation middleware
app.add_middleware(RequestValidationMiddleware, max_request_size=50 * 1024 * 1024)  # 50MB

# Add rate limiting middleware (more permissive for API usage)
app.add_middleware(RateLimitMiddleware, requests_per_minute=120, requests_per_hour=5000)

# Add security headers middleware
app.add_middleware(SecurityHeadersMiddleware)

# Add request validation middleware
app.add_middleware(RequestValidationMiddleware, max_request_size=50 * 1024 * 1024)  # 50MB

# Add rate limiting middleware (more permissive for API usage)
app.add_middleware(RateLimitMiddleware, requests_per_minute=120, requests_per_hour=5000)

# Add IP whitelist for admin endpoints (if configured)
admin_ips = []
if config.ENV_MODE == EnvMode.PRODUCTION:
    # In production, you might want to restrict admin access
    admin_ips = ["127.0.0.1", "::1"]  # localhost only

if admin_ips:
    app.add_middleware(
        IPWhitelistMiddleware,
        admin_endpoints=["/api/admin", "/api/system"],
        allowed_ips=admin_ips
    )

# Create a main API router
api_router = APIRouter()

# Include all API routers without individual prefixes
api_router.include_router(workflows_api.router)
api_router.include_router(agent_api.router)
api_router.include_router(sandbox_api.router)
api_router.include_router(billing_api.router)
api_router.include_router(feature_flags_api.router)

from mcp_service import api as mcp_api
from mcp_service import secure_api as secure_mcp_api
from mcp_service import template_api as template_api

api_router.include_router(mcp_api.router)
api_router.include_router(secure_mcp_api.router, prefix="/secure-mcp")
api_router.include_router(template_api.router, prefix="/templates")

api_router.include_router(transcription_api.router)
api_router.include_router(email_api.router)

from knowledge_base import api as knowledge_base_api
api_router.include_router(knowledge_base_api.router)

api_router.include_router(triggers_api.router)
api_router.include_router(unified_oauth_api.router)

from pipedream import api as pipedream_api
api_router.include_router(pipedream_api.router)

# Include admin API
import admin_api
api_router.include_router(admin_api.router)

@api_router.get("/health")
async def health_check():
    """Basic health check endpoint"""
    logger.info("Health check endpoint called")
    return {
        "status": "ok",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "instance_id": instance_id
    }

@api_router.get("/health/detailed")
async def detailed_health_check():
    """Comprehensive health check with all system components"""
    logger.info("Detailed health check endpoint called")
    try:
        health_status = await get_health_status()
        status_code = 200 if health_status["status"] == "healthy" else 503
        return JSONResponse(content=health_status, status_code=status_code)
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            },
            status_code=503
        )

@api_router.get("/health/ready")
async def readiness_check():
    """Kubernetes-style readiness probe"""
    logger.info("Readiness check endpoint called")
    try:
        readiness_status = await get_readiness_status()
        status_code = 200 if readiness_status["status"] == "ready" else 503
        return JSONResponse(content=readiness_status, status_code=status_code)
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            content={
                "status": "not_ready",
                "error": str(e),
                "timestamp": time.time()
            },
            status_code=503
        )


app.include_router(api_router, prefix="/api")


if __name__ == "__main__":
    import uvicorn
    
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    workers = 4
    
    logger.info(f"Starting server on 0.0.0.0:8000 with {workers} workers")
    uvicorn.run(
        "api:app", 
        host="0.0.0.0", 
        port=8000,
        workers=workers,
        loop="asyncio"
    )