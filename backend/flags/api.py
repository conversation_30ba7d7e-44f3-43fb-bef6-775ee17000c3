from fastapi import APIRouter
from utils.logger import logger
from .flags import list_flags, is_enabled, get_flag_details

router = APIRouter()


@router.get("/feature-flags")
async def get_feature_flags():
    try:
        flags = await list_flags()
        return {"flags": flags}
    except Exception as e:
        logger.error(f"Error fetching feature flags: {str(e)}")
        return {"flags": {}}

@router.get("/feature-flags/{flag_name}")
async def get_feature_flag(flag_name: str):
    """Get a specific feature flag status"""
    try:
        enabled = await is_enabled(flag_name)
        return {"enabled": enabled}
    except Exception as e:
        logger.error(f"Error fetching feature flag {flag_name}: {str(e)}")
        # Return default values for common flags
        defaults = {
            "custom_agents": True,
            "agent_marketplace": True,
            "knowledge_base": True
        }
        return {"enabled": defaults.get(flag_name, False)}