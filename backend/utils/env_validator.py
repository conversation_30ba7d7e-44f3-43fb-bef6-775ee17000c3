"""
Environment variable validation for production deployment
"""

import os
from typing import List, Dict, Any, Optional
from utils.logger import logger
from utils.config import config, EnvMode

class EnvironmentValidator:
    """Validates environment configuration for production readiness"""
    
    def __init__(self):
        self.required_vars = {
            "all": [
                "ENV_MODE",
                "SUPABASE_URL",
                "SUPABASE_SERVICE_ROLE_KEY",
                "REDIS_HOST",
                "REDIS_PORT",
                "MODEL_TO_USE",
                "ADMIN_API_KEY"
            ],
            "production": [
                "GROQ_API_KEY",
                "GEMINI_API_KEY",
                "TAVILY_API_KEY",
                "FIRECRAWL_API_KEY",
                "SMITHERY_API_KEY",
                "DAYTONA_API_KEY"
            ],
            "optional": [
                "ANTHROPIC_API_KEY",
                "OPENAI_API_KEY",
                "OPENROUTER_API_KEY",
                "XAI_API_KEY",
                "STRIPE_SECRET_KEY",
                "MAILTRAP_API_TOKEN",
                "AWS_ACCESS_KEY_ID",
                "AWS_SECRET_ACCESS_KEY"
            ]
        }
        
        self.demo_keys = [
            "demo_key_for_development",
            "sk_test_demo_key_for_development",
            "whsec_demo_webhook_secret_for_development",
            "price_demo_plan_for_development",
            "demo_signing_key_for_development",
            "demo_next_signing_key_for_development"
        ]
    
    def validate_environment(self) -> Dict[str, Any]:
        """Validate current environment configuration"""
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "environment": str(config.ENV_MODE.value) if hasattr(config.ENV_MODE, 'value') else str(config.ENV_MODE),
            "missing_required": [],
            "demo_keys_found": [],
            "recommendations": []
        }
        
        # Check required variables for all environments
        for var in self.required_vars["all"]:
            value = getattr(config, var, None) or os.getenv(var)
            if not value:
                validation_result["missing_required"].append(var)
                validation_result["errors"].append(f"Required variable {var} is not set")
                validation_result["valid"] = False
        
        # Check production-specific requirements
        if config.ENV_MODE == EnvMode.PRODUCTION:
            for var in self.required_vars["production"]:
                value = getattr(config, var, None) or os.getenv(var)
                if not value:
                    validation_result["missing_required"].append(var)
                    validation_result["errors"].append(f"Production variable {var} is not set")
                    validation_result["valid"] = False
        
        # Check for demo keys in production
        if config.ENV_MODE == EnvMode.PRODUCTION:
            self._check_demo_keys(validation_result)
        
        # Check SSL configuration
        self._check_ssl_config(validation_result)
        
        # Check database configuration
        self._check_database_config(validation_result)
        
        # Check security configuration
        self._check_security_config(validation_result)
        
        # Add recommendations
        self._add_recommendations(validation_result)
        
        return validation_result
    
    def _check_demo_keys(self, result: Dict[str, Any]):
        """Check for demo keys in production"""
        all_vars = {**os.environ}
        
        for var_name, var_value in all_vars.items():
            if var_value in self.demo_keys:
                result["demo_keys_found"].append(var_name)
                result["errors"].append(f"Demo key found in production for {var_name}")
                result["valid"] = False
    
    def _check_ssl_config(self, result: Dict[str, Any]):
        """Check SSL configuration"""
        ssl_verify = os.getenv("SSL_VERIFY", "true").lower()
        
        if config.ENV_MODE == EnvMode.PRODUCTION and ssl_verify == "false":
            result["warnings"].append("SSL verification is disabled in production")
            result["recommendations"].append("Enable SSL verification for production")
        
        if os.getenv("PYTHONHTTPSVERIFY") == "0":
            result["warnings"].append("Python HTTPS verification is disabled")
            if config.ENV_MODE == EnvMode.PRODUCTION:
                result["recommendations"].append("Enable Python HTTPS verification for production")
    
    def _check_database_config(self, result: Dict[str, Any]):
        """Check database configuration"""
        supabase_url = getattr(config, 'SUPABASE_URL', '')
        
        if not supabase_url.startswith('https://'):
            result["warnings"].append("Supabase URL should use HTTPS")
        
        if 'localhost' in supabase_url and config.ENV_MODE == EnvMode.PRODUCTION:
            result["errors"].append("Production environment should not use localhost database")
            result["valid"] = False
    
    def _check_security_config(self, result: Dict[str, Any]):
        """Check security configuration"""
        admin_key = getattr(config, 'ADMIN_API_KEY', '')
        
        if len(admin_key) < 32:
            result["warnings"].append("Admin API key should be at least 32 characters long")
        
        if 'admin' in admin_key.lower() or 'password' in admin_key.lower():
            result["warnings"].append("Admin API key should not contain predictable words")
        
        # Check rate limiting
        rate_limit_enabled = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
        if not rate_limit_enabled and config.ENV_MODE == EnvMode.PRODUCTION:
            result["warnings"].append("Rate limiting is disabled in production")
            result["recommendations"].append("Enable rate limiting for production")
    
    def _add_recommendations(self, result: Dict[str, Any]):
        """Add general recommendations"""
        if config.ENV_MODE == EnvMode.PRODUCTION:
            result["recommendations"].extend([
                "Use environment-specific configuration files",
                "Implement proper secret management (e.g., AWS Secrets Manager, HashiCorp Vault)",
                "Set up monitoring and alerting",
                "Configure log aggregation",
                "Implement backup strategies",
                "Set up health check monitoring",
                "Configure auto-scaling policies",
                "Implement proper error tracking (e.g., Sentry)",
                "Set up performance monitoring",
                "Configure security scanning"
            ])
        
        if not result["errors"] and not result["warnings"]:
            result["recommendations"].append("Environment configuration looks good!")

def validate_production_readiness() -> Dict[str, Any]:
    """Validate if the application is ready for production"""
    validator = EnvironmentValidator()
    return validator.validate_environment()

def log_environment_status():
    """Log environment validation status"""
    validation = validate_production_readiness()
    
    logger.info(f"Environment validation completed for {validation['environment']}")
    
    if validation["valid"]:
        logger.info("✅ Environment validation passed")
    else:
        logger.error("❌ Environment validation failed")
        for error in validation["errors"]:
            logger.error(f"  - {error}")
    
    if validation["warnings"]:
        logger.warning("⚠️  Environment warnings:")
        for warning in validation["warnings"]:
            logger.warning(f"  - {warning}")
    
    if validation["recommendations"]:
        logger.info("💡 Recommendations:")
        for rec in validation["recommendations"][:5]:  # Show first 5 recommendations
            logger.info(f"  - {rec}")
    
    return validation
