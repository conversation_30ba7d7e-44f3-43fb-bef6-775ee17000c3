"""
Enhanced input validation utilities for security.
Provides sanitization and validation functions for user inputs.
"""

import re
import html
import urllib.parse
from typing import Any, Dict, List, Optional, Union
from fastapi import HTTPException
from utils.logger import logger


class InputValidator:
    """
    Comprehensive input validation and sanitization class.
    """
    
    # Common dangerous patterns
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
        r'onmouseover\s*=',
        r'onfocus\s*=',
        r'onblur\s*=',
        r'onchange\s*=',
        r'onsubmit\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>.*?</embed>',
        r'<link[^>]*>',
        r'<meta[^>]*>',
        r'expression\s*\(',
        r'eval\s*\(',
        r'setTimeout\s*\(',
        r'setInterval\s*\(',
    ]
    
    SQL_INJECTION_PATTERNS = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
        r'(\b(OR|AND)\s+\d+\s*=\s*\d+)',
        r'(\b(OR|AND)\s+[\'"][^\'"]*[\'"])',
        r'(--|\#|/\*|\*/)',
        r'(\bUNION\s+SELECT\b)',
        r'(\bINTO\s+OUTFILE\b)',
        r'(\bLOAD_FILE\s*\()',
    ]
    
    PATH_TRAVERSAL_PATTERNS = [
        r'\.\./+',
        r'\.\.\\+',
        r'%2e%2e%2f',
        r'%2e%2e%5c',
        r'\.\.%2f',
        r'\.\.%5c',
    ]
    
    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 1000) -> str:
        """
        Sanitize a string input by removing dangerous patterns and limiting length.
        """
        if not isinstance(value, str):
            raise ValueError("Input must be a string")
        
        # Limit length
        if len(value) > max_length:
            logger.warning(f"Input truncated from {len(value)} to {max_length} characters")
            value = value[:max_length]
        
        # HTML encode to prevent XSS
        value = html.escape(value)
        
        # Remove null bytes
        value = value.replace('\x00', '')
        
        # Remove control characters except common whitespace
        value = re.sub(r'[\x01-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
        
        return value.strip()
    
    @classmethod
    def validate_email(cls, email: str) -> bool:
        """
        Validate email format.
        """
        if not email or len(email) > 254:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @classmethod
    def validate_uuid(cls, uuid_str: str) -> bool:
        """
        Validate UUID format.
        """
        if not uuid_str:
            return False
        
        pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        return bool(re.match(pattern, uuid_str.lower()))
    
    @classmethod
    def validate_url(cls, url: str, allowed_schemes: List[str] = None) -> bool:
        """
        Validate URL format and scheme.
        """
        if not url or len(url) > 2048:
            return False
        
        if allowed_schemes is None:
            allowed_schemes = ['http', 'https']
        
        try:
            parsed = urllib.parse.urlparse(url)
            return (
                parsed.scheme in allowed_schemes and
                parsed.netloc and
                not any(pattern in url.lower() for pattern in ['javascript:', 'data:', 'vbscript:'])
            )
        except Exception:
            return False
    
    @classmethod
    def check_xss_patterns(cls, value: str) -> bool:
        """
        Check if string contains XSS patterns.
        Returns True if dangerous patterns are found.
        """
        if not isinstance(value, str):
            return False
        
        value_lower = value.lower()
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, value_lower, re.IGNORECASE | re.DOTALL):
                logger.warning(f"XSS pattern detected: {pattern}")
                return True
        
        return False
    
    @classmethod
    def check_sql_injection(cls, value: str) -> bool:
        """
        Check if string contains SQL injection patterns.
        Returns True if dangerous patterns are found.
        """
        if not isinstance(value, str):
            return False
        
        value_lower = value.lower()
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value_lower, re.IGNORECASE):
                logger.warning(f"SQL injection pattern detected: {pattern}")
                return True
        
        return False
    
    @classmethod
    def check_path_traversal(cls, value: str) -> bool:
        """
        Check if string contains path traversal patterns.
        Returns True if dangerous patterns are found.
        """
        if not isinstance(value, str):
            return False
        
        value_lower = value.lower()
        for pattern in cls.PATH_TRAVERSAL_PATTERNS:
            if re.search(pattern, value_lower, re.IGNORECASE):
                logger.warning(f"Path traversal pattern detected: {pattern}")
                return True
        
        return False
    
    @classmethod
    def validate_json_input(cls, data: Dict[str, Any], max_depth: int = 10) -> Dict[str, Any]:
        """
        Validate and sanitize JSON input recursively.
        """
        def _validate_recursive(obj: Any, depth: int = 0) -> Any:
            if depth > max_depth:
                raise ValueError(f"JSON depth exceeds maximum of {max_depth}")
            
            if isinstance(obj, dict):
                if len(obj) > 100:  # Limit number of keys
                    raise ValueError("Too many keys in JSON object")
                
                result = {}
                for key, value in obj.items():
                    # Validate key
                    if not isinstance(key, str) or len(key) > 100:
                        raise ValueError("Invalid key in JSON object")
                    
                    clean_key = cls.sanitize_string(key, max_length=100)
                    if cls.check_xss_patterns(clean_key) or cls.check_sql_injection(clean_key):
                        raise ValueError(f"Dangerous pattern in key: {key}")
                    
                    # Validate value recursively
                    result[clean_key] = _validate_recursive(value, depth + 1)
                
                return result
            
            elif isinstance(obj, list):
                if len(obj) > 1000:  # Limit array size
                    raise ValueError("Array too large")
                
                return [_validate_recursive(item, depth + 1) for item in obj]
            
            elif isinstance(obj, str):
                if len(obj) > 10000:  # Limit string length
                    raise ValueError("String too long")
                
                clean_str = cls.sanitize_string(obj, max_length=10000)
                if cls.check_xss_patterns(clean_str) or cls.check_sql_injection(clean_str):
                    raise ValueError(f"Dangerous pattern in string: {obj[:100]}...")
                
                return clean_str
            
            elif isinstance(obj, (int, float, bool)) or obj is None:
                return obj
            
            else:
                raise ValueError(f"Unsupported data type: {type(obj)}")
        
        return _validate_recursive(data)
    
    @classmethod
    def validate_file_upload(cls, filename: str, content_type: str, file_size: int, 
                           max_size: int = 50 * 1024 * 1024,  # 50MB
                           allowed_types: List[str] = None) -> bool:
        """
        Validate file upload parameters.
        """
        if allowed_types is None:
            allowed_types = [
                'text/plain', 'text/csv', 'application/json',
                'application/pdf', 'image/jpeg', 'image/png', 'image/gif',
                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ]
        
        # Check file size
        if file_size > max_size:
            logger.warning(f"File too large: {file_size} bytes")
            return False
        
        # Check content type
        if content_type not in allowed_types:
            logger.warning(f"Disallowed content type: {content_type}")
            return False
        
        # Check filename
        if not filename or len(filename) > 255:
            logger.warning(f"Invalid filename: {filename}")
            return False
        
        # Check for dangerous file extensions
        dangerous_extensions = [
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
            '.jar', '.php', '.asp', '.aspx', '.jsp', '.sh', '.ps1'
        ]
        
        filename_lower = filename.lower()
        for ext in dangerous_extensions:
            if filename_lower.endswith(ext):
                logger.warning(f"Dangerous file extension: {ext}")
                return False
        
        # Check for path traversal in filename
        if cls.check_path_traversal(filename):
            logger.warning(f"Path traversal in filename: {filename}")
            return False
        
        return True


def validate_request_data(data: Any, validation_rules: Dict[str, Any] = None) -> Any:
    """
    Convenience function to validate request data.
    """
    try:
        if isinstance(data, dict):
            return InputValidator.validate_json_input(data)
        elif isinstance(data, str):
            return InputValidator.sanitize_string(data)
        else:
            return data
    except ValueError as e:
        logger.warning(f"Input validation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid input: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected validation error: {str(e)}")
        raise HTTPException(status_code=500, detail="Input validation error")
