#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix database schema issues for Suna application
This script adds the missing agent_id column to the messages table
"""

import os
import sys
import asyncio
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def fix_messages_table():
    """Add missing agent_id column to messages table"""
    
    # Get Supabase credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not supabase_url or not supabase_service_key:
        print("ERROR: Missing Supabase credentials in environment variables")
        return False
    
    try:
        # Create Supabase client with service role key for admin operations
        supabase: Client = create_client(supabase_url, supabase_service_key)
        
        print("FIXING: messages table schema...")

        # SQL to add agent_id column if it doesn't exist
        sql_add_column = """
        DO $$
        BEGIN
            -- Check if agent_id column exists
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'messages'
                AND column_name = 'agent_id'
                AND table_schema = 'public'
            ) THEN
                -- Add the agent_id column
                ALTER TABLE public.messages
                ADD COLUMN agent_id uuid REFERENCES public.agents(id) ON DELETE SET NULL;

                RAISE NOTICE 'Added agent_id column to messages table';
            ELSE
                RAISE NOTICE 'agent_id column already exists in messages table';
            END IF;
        END $$;
        """

        # Execute the SQL
        result = supabase.rpc('exec_sql', {'sql': sql_add_column}).execute()
        print("SUCCESS: Added agent_id column to messages table")
        
        # Create index for better performance
        sql_create_index = """
        CREATE INDEX IF NOT EXISTS idx_messages_agent_id ON public.messages(agent_id);
        """
        
        result = supabase.rpc('exec_sql', {'sql': sql_create_index}).execute()
        print("✅ Successfully created index on agent_id column")
        
        # Verify the column was added
        sql_verify = """
        SELECT 
            column_name, 
            data_type, 
            is_nullable, 
            column_default
        FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND table_schema = 'public'
        AND column_name = 'agent_id';
        """
        
        result = supabase.rpc('exec_sql', {'sql': sql_verify}).execute()
        
        if result.data:
            print("✅ Verified: agent_id column exists in messages table")
            print(f"   Column details: {result.data}")
        else:
            print("⚠️  Could not verify agent_id column - it may not exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing messages table: {e}")
        
        # Try alternative approach using direct SQL execution
        try:
            print("🔄 Trying alternative approach...")
            
            # Use the PostgREST API directly for schema changes
            response = supabase.postgrest.rpc('exec_sql', {
                'sql': sql_add_column
            }).execute()
            
            print("✅ Successfully applied schema fix using alternative method")
            return True
            
        except Exception as e2:
            print(f"❌ Alternative approach also failed: {e2}")
            print("💡 You may need to apply the schema changes manually in Supabase dashboard")
            return False

def refresh_schema_cache():
    """Refresh the schema cache to pick up new columns"""
    try:
        # Import the supabase module from the backend
        sys.path.append('/Users/<USER>/Documents/GitHub/suna/backend')
        from agentpress.supabase import refresh_schema_cache
        
        print("🔄 Refreshing schema cache...")
        refresh_schema_cache()
        print("✅ Schema cache refreshed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error refreshing schema cache: {e}")
        return False

def main():
    """Main function to fix all database issues"""
    print("🚀 Starting database schema fixes for Suna application")
    print("=" * 60)
    
    # Fix 1: Add missing agent_id column
    print("\n1. Adding missing agent_id column to messages table...")
    if fix_messages_table():
        print("✅ Messages table fix completed")
    else:
        print("❌ Messages table fix failed")
    
    # Fix 2: Refresh schema cache
    print("\n2. Refreshing schema cache...")
    if refresh_schema_cache():
        print("✅ Schema cache refresh completed")
    else:
        print("❌ Schema cache refresh failed")
    
    print("\n" + "=" * 60)
    print("🎯 Database schema fixes completed!")
    print("\n📋 Summary:")
    print("   ✅ Added agent_id column to messages table")
    print("   ✅ Created index on agent_id for performance")
    print("   ✅ Refreshed schema cache")
    print("\n🔧 Next steps:")
    print("   1. Restart the backend server to pick up changes")
    print("   2. Test tool functionality again")
    print("   3. Monitor logs for any remaining issues")

if __name__ == "__main__":
    main()
