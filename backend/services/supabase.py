"""
Centralized database connection management for AgentPress using Supabase.
"""

from typing import Optional
from supabase import create_async_client, AsyncClient
from utils.logger import logger
from utils.config import config

class DBConnection:
    """Singleton database connection manager using Supabase."""
    
    _instance: Optional['DBConnection'] = None
    _initialized = False
    _client: Optional[AsyncClient] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """No initialization needed in __init__ as it's handled in __new__"""
        pass

    async def initialize(self):
        """Initialize the database connection."""
        if self._initialized:
            return

        try:
            supabase_url = config.SUPABASE_URL
            # Use service role key preferentially for backend operations
            supabase_key = config.SUPABASE_SERVICE_ROLE_KEY or config.SUPABASE_ANON_KEY

            if not supabase_url or not supabase_key:
                logger.error("Missing required environment variables for Supabase connection")
                raise RuntimeError("SUPABASE_URL and a key (SERVICE_ROLE_KEY or ANON_KEY) environment variables must be set.")

            logger.debug("Initializing Supabase connection")
            self._client = await create_async_client(supabase_url, supabase_key)

            # Test the connection with a simple query
            try:
                result = await self._client.table("agents").select("id").limit(1).execute()
                logger.debug("Database connection test successful")

                # Refresh schema cache to ensure all columns are available
                await self._refresh_schema_cache()
            except Exception as test_error:
                logger.warning(f"Database connection test failed (continuing anyway): {test_error}")

            self._initialized = True
            key_type = "SERVICE_ROLE_KEY" if config.SUPABASE_SERVICE_ROLE_KEY else "ANON_KEY"
            logger.debug(f"Database connection initialized with Supabase using {key_type}")
        except Exception as e:
            logger.error(f"Database initialization error: {e}")
            # Provide more specific error messages
            if "Invalid API key" in str(e):
                raise RuntimeError("Invalid Supabase API key. Please check your SUPABASE_SERVICE_ROLE_KEY or SUPABASE_ANON_KEY.")
            elif "Invalid URL" in str(e):
                raise RuntimeError("Invalid Supabase URL. Please check your SUPABASE_URL environment variable.")
            else:
                raise RuntimeError(f"Failed to initialize database connection: {str(e)}")

    async def _refresh_schema_cache(self):
        """Refresh the database schema cache"""
        try:
            # Force schema refresh by making a query to each important table
            tables_to_refresh = ["messages", "threads", "projects", "agents", "agent_runs"]

            for table in tables_to_refresh:
                try:
                    await self._client.table(table).select("*").limit(1).execute()
                    logger.debug(f"Refreshed schema cache for table: {table}")
                except Exception as e:
                    logger.warning(f"Could not refresh schema for table {table}: {e}")

            logger.info("Database schema cache refreshed successfully")
        except Exception as e:
            logger.warning(f"Failed to refresh database schema cache: {e}")

    @classmethod
    async def disconnect(cls):
        """Disconnect from the database."""
        if cls._client:
            logger.info("Disconnecting from Supabase database")
            await cls._client.close()
            cls._initialized = False
            logger.info("Database disconnected successfully")

    @property
    async def client(self) -> AsyncClient:
        """Get the Supabase client instance."""
        if not self._initialized:
            logger.debug("Supabase client not initialized, initializing now")
            await self.initialize()
        if not self._client:
            logger.error("Database client is None after initialization")
            raise RuntimeError("Database not initialized")
        return self._client
