import redis.asyncio as redis
import os
from dotenv import load_dotenv
import asyncio
from utils.logger import logger
from typing import List, Any
from utils.retry import retry

# Redis client and connection pool
client: redis.Redis | None = None
pool: redis.ConnectionPool | None = None
_initialized = False
_init_lock = asyncio.Lock()

# Constants
REDIS_KEY_TTL = 3600 * 24  # 24 hour TTL as safety mechanism


def initialize():
    """Initialize Redis connection pool and client using environment variables."""
    global client, pool

    # Load environment variables if not already loaded
    load_dotenv()

    # Get Redis configuration
    redis_host = os.getenv("REDIS_HOST", "localhost")  # Default to localhost for development
    redis_port = int(os.getenv("REDIS_PORT", 6379))
    redis_password = os.getenv("REDIS_PASSWORD", "")

    # Connection pool configuration
    max_connections = int(os.getenv("REDIS_MAX_CONNECTIONS", 2048))
    retry_on_timeout = not (os.getenv("REDIS_RETRY_ON_TIMEOUT", "True").lower() != "true")

    logger.info(f"Initializing Redis connection pool to {redis_host}:{redis_port} with max {max_connections} connections")

    # Create connection pool with shorter timeouts for development
    pool = redis.ConnectionPool(
        host=redis_host,
        port=redis_port,
        password=redis_password,
        decode_responses=True,
        socket_timeout=5.0,  # Reduced from 20.0 for faster failure detection
        socket_connect_timeout=2.0,  # Reduced from 20.0 for faster failure detection
        retry_on_timeout=retry_on_timeout,
        health_check_interval=30,
        max_connections=max_connections,
    )

    # Create Redis client from connection pool
    client = redis.Redis(connection_pool=pool)

    return client


async def initialize_async():
    """Initialize Redis connection asynchronously."""
    global client, _initialized

    async with _init_lock:
        if _initialized:
            return client

        logger.info("Initializing Redis connection")
        initialize()

        try:
            # Use asyncio.wait_for to timeout quickly in development
            await asyncio.wait_for(client.ping(), timeout=3.0)
            logger.info("Successfully connected to Redis")
            _initialized = True
        except (Exception, asyncio.TimeoutError) as e:
            logger.warning(f"Redis connection failed: {e}")
            logger.info("Continuing without Redis features")
            client = None
            _initialized = True  # Mark as initialized to avoid retries
            # Don't raise exception, just continue without Redis

    return client


async def close():
    """Close Redis connection and connection pool."""
    global client, pool, _initialized
    if client:
        logger.info("Closing Redis connection")
        await client.aclose()
        client = None
    
    if pool:
        logger.info("Closing Redis connection pool")
        await pool.aclose()
        pool = None
    
    _initialized = False
    logger.info("Redis connection and pool closed")


async def get_client():
    """Get the Redis client, initializing if necessary."""
    global client, _initialized
    if not _initialized:
        await initialize_async()
    return client


# Basic Redis operations
async def set(key: str, value: str, ex: int = None, nx: bool = False):
    """Set a Redis key."""
    redis_client = await get_client()
    if redis_client is None:
        logger.debug(f"Redis not available, skipping set operation for key: {key}")
        return False
    return await redis_client.set(key, value, ex=ex, nx=nx)


async def get(key: str, default: str = None):
    """Get a Redis key."""
    redis_client = await get_client()
    if redis_client is None:
        logger.debug(f"Redis not available, returning default for key: {key}")
        return default
    result = await redis_client.get(key)
    return result if result is not None else default


async def delete(key: str):
    """Delete a Redis key."""
    redis_client = await get_client()
    if redis_client is None:
        logger.debug(f"Redis not available, skipping delete operation for key: {key}")
        return 0
    return await redis_client.delete(key)


async def publish(channel: str, message: str):
    """Publish a message to a Redis channel."""
    redis_client = await get_client()
    if redis_client is None:
        logger.debug(f"Redis not available, skipping publish to channel: {channel}")
        return 0
    return await redis_client.publish(channel, message)


async def create_pubsub():
    """Create a Redis pubsub object."""
    redis_client = await get_client()
    return redis_client.pubsub()


# List operations
async def rpush(key: str, *values: Any):
    """Append one or more values to a list."""
    redis_client = await get_client()
    return await redis_client.rpush(key, *values)


async def lrange(key: str, start: int, end: int) -> List[str]:
    """Get a range of elements from a list."""
    redis_client = await get_client()
    return await redis_client.lrange(key, start, end)


# Key management


async def keys(pattern: str) -> List[str]:
    """Get keys matching a pattern."""
    redis_client = await get_client()
    return await redis_client.keys(pattern)
