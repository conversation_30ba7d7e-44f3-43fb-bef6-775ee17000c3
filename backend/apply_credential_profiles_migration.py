#!/usr/bin/env python3
"""
Apply the credential profiles migration to fix the credentials page error.
"""

import asyncio
import os
from dotenv import load_dotenv
from supabase import create_async_client

load_dotenv()

async def apply_migration():
    """Apply the credential profiles migration"""
    
    # Read the migration file
    migration_path = "supabase/migrations/20250618000000_credential_profiles.sql"
    
    try:
        with open(migration_path, 'r') as f:
            migration_sql = f.read()
        
        print(f"Read migration file: {migration_path}")
        
        # Create Supabase client
        supabase = await create_async_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        )
        
        print("Connected to Supabase")
        
        # Check if table already exists by trying to query it
        try:
            result = await supabase.table('user_mcp_credential_profiles').select('profile_id').limit(1).execute()
            print("Table 'user_mcp_credential_profiles' already exists")
            return
        except Exception:
            print("Table 'user_mcp_credential_profiles' does not exist, applying migration...")

        # Split the migration into individual statements
        statements = migration_sql.split(';')

        for i, statement in enumerate(statements):
            statement = statement.strip()
            if not statement or statement.upper() in ['BEGIN', 'COMMIT']:
                continue

            print(f"Executing statement {i+1}/{len(statements)}: {statement[:50]}...")

            try:
                # Use the SQL editor to execute each statement
                await supabase.rpc('sql', {'query': statement}).execute()
            except Exception as e:
                print(f"Warning: Statement failed: {str(e)}")
                # Continue with other statements

        print("Migration applied successfully!")

        # Verify the table was created
        try:
            result = await supabase.table('user_mcp_credential_profiles').select('profile_id').limit(1).execute()
            print("✅ Table 'user_mcp_credential_profiles' created successfully")
        except Exception as e:
            print(f"❌ Table creation verification failed: {str(e)}")
            
    except FileNotFoundError:
        print(f"❌ Migration file not found: {migration_path}")
    except Exception as e:
        print(f"❌ Error applying migration: {str(e)}")

if __name__ == "__main__":
    asyncio.run(apply_migration())
