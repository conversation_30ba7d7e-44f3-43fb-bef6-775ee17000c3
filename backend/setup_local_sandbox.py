import asyncio
import uuid
from services.supabase import DBConnection

async def setup_local_sandbox():
    """Set up a local sandbox for the project."""
    
    # Initialize database connection
    db = DBConnection()
    await db.initialize()
    client = await db.client
    
    # Generate a unique sandbox ID for local sandbox
    sandbox_id = str(uuid.uuid4())
    
    # Update the project with local sandbox configuration
    sandbox_config = {
        'id': sandbox_id,
        'provider': 'local',
        'workspace_id': sandbox_id,
        'status': 'active',
        'type': 'local'
    }
    
    try:
        result = await client.table('projects').update({
            'sandbox': sandbox_config
        }).eq('project_id', 'cfa1ab02-a9ea-4a2b-a106-ddf042dd3b43').execute()
        
        if result.data:
            print(f'✅ Successfully configured local sandbox for project')
            print(f'Sandbox ID: {sandbox_id}')
            print(f'Sandbox config: {sandbox_config}')
        else:
            print('❌ Failed to update project sandbox configuration')
            
    except Exception as e:
        print(f'❌ Error setting up local sandbox: {e}')

if __name__ == "__main__":
    asyncio.run(setup_local_sandbox())
