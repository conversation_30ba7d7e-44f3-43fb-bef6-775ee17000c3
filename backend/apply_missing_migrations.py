#!/usr/bin/env python3
"""
Apply missing database migrations for agent_workflows and related tables
"""
import asyncio
import os
from dotenv import load_dotenv
from services.supabase import DBConnection

load_dotenv()

async def apply_missing_migrations():
    """Apply missing migrations to fix database schema issues"""
    db = DBConnection()
    client = await db.client

    print("🔧 Applying missing database migrations...")

    # Check if agent_workflows table exists
    try:
        result = await client.table('agent_workflows').select('id').limit(1).execute()
        print("✅ agent_workflows table exists")
    except Exception as e:
        print(f"❌ agent_workflows table missing: {e}")
        print("📝 The agent_workflows migration needs to be applied manually via Supabase dashboard")
        print("📝 Please run the SQL from: backend/supabase/migrations/20250705161610_agent_workflows.sql")

    # Check if agent_knowledge_base_entries table exists
    try:
        result = await client.table('agent_knowledge_base_entries').select('entry_id').limit(1).execute()
        print("✅ agent_knowledge_base_entries table exists")
    except Exception as e:
        print(f"❌ agent_knowledge_base_entries table missing: {e}")
        print("📝 The agent knowledge base migration needs to be applied manually via Supabase dashboard")
        print("📝 Please run the SQL from: backend/supabase/migrations/20250701082739_agent_knowledge_base.sql")

    # Check if knowledge_base_entries table exists
    try:
        result = await client.table('knowledge_base_entries').select('entry_id').limit(1).execute()
        print("✅ knowledge_base_entries table exists")
    except Exception as e:
        print(f"❌ knowledge_base_entries table missing: {e}")
        print("📝 The knowledge base migration needs to be applied manually via Supabase dashboard")
        print("📝 Please run the SQL from: backend/supabase/migrations/20250624093857_knowledge_base.sql")

    # Verify tables exist
    tables_to_check = [
        'agent_workflows',
        'agents',
        'agent_knowledge_base_entries',
        'knowledge_base_entries'
    ]

    print("\n🔍 Verifying table existence...")
    for table in tables_to_check:
        try:
            result = await client.table(table).select('*').limit(1).execute()
            print(f"✅ {table} table exists")
        except Exception as e:
            print(f"❌ {table} table missing or inaccessible: {e}")

    print("\n✅ Migration check completed!")

if __name__ == "__main__":
    asyncio.run(apply_missing_migrations())
