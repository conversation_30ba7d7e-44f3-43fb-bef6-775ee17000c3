#!/usr/bin/env python3
"""
Script to enable the custom_agents feature flag
"""
import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flags.flags import enable_flag

async def main():
    try:
        print("Enabling custom_agents feature flag...")
        success1 = await enable_flag("custom_agents", "Enable custom agents functionality")
        if success1:
            print("✅ Successfully enabled custom_agents feature flag")
        else:
            print("❌ Failed to enable custom_agents feature flag")

        print("Enabling agent_marketplace feature flag...")
        success2 = await enable_flag("agent_marketplace", "Enable agent marketplace functionality")
        if success2:
            print("✅ Successfully enabled agent_marketplace feature flag")
        else:
            print("❌ Failed to enable agent_marketplace feature flag")

        if not (success1 and success2):
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error enabling feature flags: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
