import os
import uuid
import asyncio
import subprocess
import tempfile
import shutil
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from dotenv import load_dotenv
from utils.logger import logger
from utils.config import config
from utils.config import Configuration

# Try to import Daytona SDK, but make it optional
try:
    from daytona_sdk import AsyncDaytona, DaytonaConfig, CreateSandboxFromImageParams, AsyncSandbox, SessionExecuteRequest, Resources, SandboxState
    DAYTONA_AVAILABLE = True
    logger.debug("Daytona SDK imported successfully")
except ImportError:
    DAYTONA_AVAILABLE = False
    logger.warning("Daytona SDK not available, using fallback sandbox implementation")

load_dotenv()

# Daytona configuration (optional)
daytona = None
if DAYTONA_AVAILABLE and config.DAYTONA_API_KEY:
    logger.debug("Initializing Daytona sandbox configuration")
    daytona_config = DaytonaConfig(
        api_key=config.DAYTONA_API_KEY,
        api_url=config.DAYTONA_SERVER_URL,
        target=config.DAYTONA_TARGET,
    )

    if daytona_config.api_key:
        logger.debug("Daytona API key configured successfully")
        daytona = AsyncDaytona(daytona_config)
    else:
        logger.warning("No Daytona API key found in environment variables")
else:
    logger.info("Using fallback sandbox implementation (no Daytona)")

# Fallback sandbox implementation
class LocalSandbox:
    """Local sandbox implementation that works without Daytona"""

    def __init__(self, sandbox_id: str, workspace_dir: str):
        self.id = sandbox_id
        self.workspace_dir = workspace_dir
        self.state = "RUNNING"
        self.created_at = datetime.now(timezone.utc)
        self.processes = {}

    async def execute(self, command: str, cwd: Optional[str] = None) -> Dict[str, Any]:
        """Execute a command in the sandbox"""
        try:
            if cwd is None:
                cwd = self.workspace_dir

            # Ensure the working directory exists
            os.makedirs(cwd, exist_ok=True)

            logger.debug(f"Executing command in local sandbox {self.id}: {command}")

            # Execute the command
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=cwd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )

            stdout, stderr = await process.communicate()

            result = {
                "exit_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore'),
                "command": command,
                "cwd": cwd
            }

            logger.debug(f"Command execution result: exit_code={result['exit_code']}")
            return result

        except Exception as e:
            logger.error(f"Error executing command in local sandbox: {str(e)}")
            return {
                "exit_code": 1,
                "stdout": "",
                "stderr": str(e),
                "command": command,
                "cwd": cwd
            }

    async def stop(self):
        """Stop the sandbox"""
        self.state = "STOPPED"
        logger.info(f"Local sandbox {self.id} stopped")

    async def start(self):
        """Start the sandbox"""
        self.state = "RUNNING"
        logger.info(f"Local sandbox {self.id} started")

    async def get_preview_link(self, port: int):
        """Get preview link for a port (local sandbox implementation)"""
        # For local sandbox, return a simple object with URL
        class PreviewLink:
            def __init__(self, url: str):
                self.url = url

        return PreviewLink(f"http://localhost:{port}")

# Global registry for local sandboxes
local_sandboxes: Dict[str, LocalSandbox] = {}

async def get_or_start_sandbox(sandbox_id: str):
    """Retrieve a sandbox by ID, check its state, and start it if needed."""

    logger.info(f"Getting or starting sandbox with ID: {sandbox_id}")

    # Try Daytona first if available
    if daytona:
        try:
            sandbox = await daytona.get(sandbox_id)

            # Check if sandbox needs to be started
            if sandbox.state == SandboxState.ARCHIVED or sandbox.state == SandboxState.STOPPED:
                logger.info(f"Sandbox is in {sandbox.state} state. Starting...")
                try:
                    await daytona.start(sandbox)
                    # Refresh sandbox state after starting
                    sandbox = await daytona.get(sandbox_id)

                    # Start supervisord in a session when restarting
                    await start_supervisord_session(sandbox)
                except Exception as e:
                    logger.error(f"Error starting sandbox: {e}")
                    raise e

            logger.info(f"Sandbox {sandbox_id} is ready")
            return sandbox

        except Exception as e:
            logger.warning(f"Daytona sandbox failed, falling back to local sandbox: {str(e)}")

    # Fallback to local sandbox
    if sandbox_id in local_sandboxes:
        sandbox = local_sandboxes[sandbox_id]
        if sandbox.state == "STOPPED":
            await sandbox.start()
        logger.info(f"Using existing local sandbox {sandbox_id}")
        return sandbox
    else:
        # Create new local sandbox
        workspace_dir = os.path.join(tempfile.gettempdir(), f"suna_sandbox_{sandbox_id}")
        sandbox = LocalSandbox(sandbox_id, workspace_dir)
        local_sandboxes[sandbox_id] = sandbox
        logger.info(f"Created new local sandbox {sandbox_id} at {workspace_dir}")
        return sandbox

async def start_supervisord_session(sandbox: AsyncSandbox):
    """Start supervisord in a session."""
    session_id = "supervisord-session"
    try:
        logger.info(f"Creating session {session_id} for supervisord")
        await sandbox.process.create_session(session_id)
        
        # Execute supervisord command
        await sandbox.process.execute_session_command(session_id, SessionExecuteRequest(
            command="exec /usr/bin/supervisord -n -c /etc/supervisor/conf.d/supervisord.conf",
            var_async=True
        ))
        logger.info(f"Supervisord started in session {session_id}")
    except Exception as e:
        logger.error(f"Error starting supervisord session: {str(e)}")
        raise e

async def create_sandbox(password: str, project_id: str = None):
    """Create a new sandbox with all required services configured and running."""

    # Try Daytona first if available
    if daytona:
        try:
            logger.debug("Creating new Daytona sandbox environment")
            logger.debug("Configuring sandbox with browser-use image and environment variables")

            labels = None
            if project_id:
                logger.debug(f"Using sandbox_id as label: {project_id}")
                labels = {'id': project_id}

            params = CreateSandboxFromImageParams(
                image=Configuration.SANDBOX_IMAGE_NAME,
                public=True,
                labels=labels,
                env_vars={
                    "CHROME_PERSISTENT_SESSION": "true",
                    "RESOLUTION": "1024x768x24",
                    "RESOLUTION_WIDTH": "1024",
                    "RESOLUTION_HEIGHT": "768",
                    "VNC_PASSWORD": password,
                    "ANONYMIZED_TELEMETRY": "false",
                    "CHROME_PATH": "",
                    "CHROME_USER_DATA": "",
                    "CHROME_DEBUGGING_PORT": "9222",
                    "CHROME_DEBUGGING_HOST": "localhost",
                    "CHROME_CDP": ""
                },
                resources=Resources(
                    cpu=2,
                    memory=4,
                    disk=5,
                ),
                auto_stop_interval=15,
                auto_archive_interval=24 * 60,
            )

            # Create the sandbox
            sandbox = await daytona.create(params)
            logger.debug(f"Sandbox created with ID: {sandbox.id}")

            # Start supervisord in a session for new sandbox
            await start_supervisord_session(sandbox)

            logger.debug(f"Sandbox environment successfully initialized")
            return sandbox

        except Exception as e:
            logger.warning(f"Daytona sandbox creation failed, falling back to local sandbox: {str(e)}")

    # Fallback to local sandbox
    sandbox_id = project_id or str(uuid.uuid4())
    workspace_dir = os.path.join(tempfile.gettempdir(), f"suna_sandbox_{sandbox_id}")

    # Create workspace directory
    os.makedirs(workspace_dir, exist_ok=True)

    # Create local sandbox
    sandbox = LocalSandbox(sandbox_id, workspace_dir)
    local_sandboxes[sandbox_id] = sandbox

    logger.info(f"Created local sandbox {sandbox_id} at {workspace_dir}")
    return sandbox

async def delete_sandbox(sandbox_id: str) -> bool:
    """Delete a sandbox by its ID."""
    logger.info(f"Deleting sandbox with ID: {sandbox_id}")

    # Try Daytona first if available
    if daytona:
        try:
            # Get the sandbox
            sandbox = await daytona.get(sandbox_id)

            # Delete the sandbox
            await daytona.delete(sandbox)

            logger.info(f"Successfully deleted Daytona sandbox {sandbox_id}")
            return True

        except Exception as e:
            logger.warning(f"Failed to delete Daytona sandbox, checking local sandbox: {str(e)}")

    # Handle local sandbox deletion
    if sandbox_id in local_sandboxes:
        try:
            sandbox = local_sandboxes[sandbox_id]
            await sandbox.stop()

            # Clean up workspace directory
            if os.path.exists(sandbox.workspace_dir):
                shutil.rmtree(sandbox.workspace_dir)

            # Remove from registry
            del local_sandboxes[sandbox_id]

            logger.info(f"Successfully deleted local sandbox {sandbox_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete local sandbox {sandbox_id}: {str(e)}")
            return False

    logger.warning(f"Sandbox {sandbox_id} not found in Daytona or local registry")
    return False
