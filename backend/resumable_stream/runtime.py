"""
Mock implementation of resumable_stream runtime for development purposes.
This is a simplified version to get the backend running.
"""
import asyncio
from typing import Any, Callable, Optional, AsyncGenerator


class ResumableStreamContext:
    """Mock implementation of ResumableStreamContext"""
    
    def __init__(self, redis_client, namespace: str):
        self.redis_client = redis_client
        self.namespace = namespace
        self._streams = {}
    
    async def resumable_stream(self, stream_id: str, generator_func: Callable) -> AsyncGenerator:
        """Create or resume a stream"""
        # For now, just execute the generator function directly
        print(f"Starting resumable stream for {stream_id}")
        try:
            result = generator_func()
            print(f"Generator function returned: {type(result)}")

            if asyncio.iscoroutine(result):
                print(f"Processing coroutine result for {stream_id}")
                async for item in await result:
                    yield item
            elif hasattr(result, '__aiter__'):
                print(f"Processing async iterator result for {stream_id}")
                async for item in result:
                    yield item
            else:
                # If it's not async, just yield the result
                print(f"Processing non-async result for {stream_id}: {result}")
                yield result
        except Exception as e:
            print(f"Error in resumable_stream for {stream_id}: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    async def resume_existing_stream(self, stream_id: str) -> Optional[AsyncGenerator]:
        """Resume an existing stream"""
        # For now, return None to indicate no existing stream
        return None
    
    async def cleanup_stream(self, stream_id: str):
        """Clean up a stream"""
        if stream_id in self._streams:
            del self._streams[stream_id]


def create_resumable_stream_context(redis_client, namespace: str) -> ResumableStreamContext:
    """Create a resumable stream context"""
    return ResumableStreamContext(redis_client, namespace)
