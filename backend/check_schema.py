import asyncio
import os
from supabase import create_client, Client

def main():
    url = os.environ.get('SUPABASE_URL', 'https://pldcxtmyivlpueddnuml.supabase.co')
    key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')
    
    if not key:
        print("Error: SUPABASE_SERVICE_ROLE_KEY environment variable not set")
        return
    
    client = create_client(url, key)
    
    # Check threads table schema
    try:
        result = client.table('threads').select('*').limit(1).execute()
        print('Columns in threads table:', list(result.data[0].keys()) if result.data else [])
    except Exception as e:
        print(f"Error querying threads table: {e}")

if __name__ == "__main__":
    main()
