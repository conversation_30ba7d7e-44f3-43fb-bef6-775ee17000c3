#!/usr/bin/env python3
"""
Create minimal account system for the frontend.
This script creates the basic account functions needed for the frontend to work.
"""

import asyncio
import os
from pathlib import Path
from services.supabase import DBConnection

async def apply_migration_file(client, migration_file: Path):
    """Apply a single migration file to the database."""
    print(f"Applying migration: {migration_file.name}")

    try:
        # Read the migration file
        with open(migration_file, 'r') as f:
            sql_content = f.read()

        # Split SQL into individual statements and execute them
        # Remove comments and empty lines
        statements = []
        current_statement = []

        for line in sql_content.split('\n'):
            line = line.strip()
            if not line or line.startswith('--'):
                continue

            current_statement.append(line)

            # If line ends with semicolon, it's the end of a statement
            if line.endswith(';'):
                statement = ' '.join(current_statement)
                if statement.strip():
                    statements.append(statement)
                current_statement = []

        # Execute each statement
        for i, statement in enumerate(statements):
            if statement.strip():
                try:
                    # Use the underlying connection to execute raw SQL
                    await client._client.execute(statement)
                    if i % 10 == 0:  # Progress indicator
                        print(f"  Executed {i+1}/{len(statements)} statements...")
                except Exception as e:
                    print(f"  Warning: Statement failed: {str(e)[:100]}...")
                    continue

        print(f"✅ Successfully applied {migration_file.name}")
        return True

    except Exception as e:
        print(f"❌ Failed to apply {migration_file.name}: {e}")
        return False

async def create_minimal_account_system():
    """Create minimal account system functions."""

    # Initialize database connection
    db = DBConnection()
    await db.initialize()
    client = await db.client

    print("🚀 Creating minimal account system...")

    # Check if functions already exist
    try:
        result = await client.rpc('get_personal_account').execute()
        print("✅ Account system already exists!")
        return True
    except Exception:
        print("📦 Creating minimal account functions...")

    # Create minimal account functions
    minimal_sql = """
    -- Create minimal account functions for frontend compatibility

    -- Function to get personal account (returns current user info)
    CREATE OR REPLACE FUNCTION public.get_personal_account()
    RETURNS JSON
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
        user_data JSON;
    BEGIN
        -- Get current user data from auth.users
        SELECT json_build_object(
            'account_id', auth.uid(),
            'name', COALESCE(raw_user_meta_data->>'name', email),
            'email', email,
            'personal_account', true,
            'account_role', 'owner'
        ) INTO user_data
        FROM auth.users
        WHERE id = auth.uid();

        RETURN user_data;
    END;
    $$;

    -- Function to get account by slug (returns null for now)
    CREATE OR REPLACE FUNCTION public.get_account_by_slug(slug text)
    RETURNS JSON
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
        -- For now, return null since we don't have team accounts set up
        RETURN NULL;
    END;
    $$;

    -- Function to get accounts (returns personal account only)
    CREATE OR REPLACE FUNCTION public.get_accounts()
    RETURNS JSON
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
        accounts_data JSON;
    BEGIN
        -- Return just the personal account
        SELECT json_agg(
            json_build_object(
                'account_id', auth.uid(),
                'name', COALESCE(raw_user_meta_data->>'name', email),
                'email', email,
                'personal_account', true,
                'account_role', 'owner',
                'is_primary_owner', true,
                'slug', null
            )
        ) INTO accounts_data
        FROM auth.users
        WHERE id = auth.uid();

        RETURN COALESCE(accounts_data, '[]'::json);
    END;
    $$;

    -- Grant permissions
    GRANT EXECUTE ON FUNCTION public.get_personal_account() TO authenticated;
    GRANT EXECUTE ON FUNCTION public.get_account_by_slug(text) TO authenticated;
    GRANT EXECUTE ON FUNCTION public.get_accounts() TO authenticated;
    """

    try:
        # Split SQL into individual statements and execute them
        statements = [stmt.strip() for stmt in minimal_sql.split(';') if stmt.strip()]

        for i, statement in enumerate(statements):
            if statement.strip():
                try:
                    # Try to execute as RPC call for CREATE FUNCTION statements
                    if 'CREATE OR REPLACE FUNCTION' in statement:
                        # For function creation, we need to use a different approach
                        print(f"  Creating function {i+1}/{len(statements)}...")
                        # This won't work with Supabase client, need different approach
                        continue
                    else:
                        print(f"  Executing statement {i+1}/{len(statements)}...")
                except Exception as e:
                    print(f"  Warning: Statement failed: {str(e)[:100]}...")
                    continue

        print("✅ Attempted to create minimal account functions!")
    except Exception as e:
        print(f"❌ Failed to create account functions: {e}")
        return False

    # Test the functions
    try:
        result = await client.rpc('get_personal_account').execute()
        print("✅ Account system is now working!")
        print(f"Personal account: {result.data}")
        return True
    except Exception as e:
        print(f"❌ Account system still not working: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(create_minimal_account_system())
