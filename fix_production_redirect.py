#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Production Redirect Issue

This script fixes the issue where new account creation redirects to localhost in production
by updating the environment configuration to use production URLs.
"""

import os
import shutil

def backup_file(file_path):
    """Create a backup of the current file"""
    if os.path.exists(file_path):
        backup_path = file_path + ".backup"
        shutil.copy2(file_path, backup_path)
        print("Backed up " + file_path + " to " + backup_path)

def fix_production_config():
    """Fix production configuration to prevent localhost redirects"""
    print("Fixing production redirect issue...")
    print("=" * 50)
    
    # 1. Update frontend environment for production
    frontend_env_path = "frontend/.env.local"
    frontend_env_production = """# Production Environment Variables
# This file contains the production configuration for the frontend

# REQUIRED - Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://pldcxtmyivlpueddnuml.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.yrYWj-hbqdS3nGTFBA1YvhAtKoY8BOVY1_OXA4_gAtA

# REQUIRED - Backend Configuration
NEXT_PUBLIC_BACKEND_URL=https://api.ai-co-founder.com/api

# REQUIRED - Site Configuration
NEXT_PUBLIC_URL=https://ai-co-founder.netlify.app

# REQUIRED - Environment Mode
NEXT_PUBLIC_ENV_MODE=PRODUCTION

# OPTIONAL - Admin Configuration
ADMIN_API_KEY=suna_admin_key_2025_secure_token_xyz789

# OPTIONAL - Performance Monitoring
NEXT_TELEMETRY_DISABLED=1

# OPTIONAL - Debug Mode (disabled in production)
NEXT_PUBLIC_DEBUG=false
DEBUG=false
"""
    
    backup_file(frontend_env_path)
    with open(frontend_env_path, 'w') as f:
        f.write(frontend_env_production)
    print("[SUCCESS] Updated " + frontend_env_path + " for PRODUCTION environment")
    
    # 2. Update backend environment mode
    backend_env_path = "backend/.env"
    if os.path.exists(backend_env_path):
        backup_file(backend_env_path)
        with open(backend_env_path, 'r') as f:
            content = f.read()
        
        # Update ENV_MODE to production
        content = content.replace('ENV_MODE=local', 'ENV_MODE=production')
        content = content.replace('ENV_MODE=staging', 'ENV_MODE=production')
        
        with open(backend_env_path, 'w') as f:
            f.write(content)
        print("[SUCCESS] Updated " + backend_env_path + " for PRODUCTION environment")
    
    # 3. Supabase config is already updated for production
    print("[INFO] Supabase config already configured for production URLs")
    
    print("")
    print("[SUCCESS] Production redirect issue has been fixed!")
    print("")
    print("Changes made:")
    print("1. Frontend environment now uses production URLs")
    print("2. Backend environment mode set to production")
    print("3. Supabase auth redirects configured for production domain")
    print("")
    print("Next steps:")
    print("1. Deploy your application to production")
    print("2. Test account creation - it should now stay on your production domain")
    print("3. If you need to switch back to local development, run:")
    print("   python fix_production_redirect.py --local")

def switch_to_local():
    """Switch back to local development"""
    print("Switching to local development...")
    print("=" * 50)
    
    # Frontend environment for local
    frontend_env_path = "frontend/.env.local"
    frontend_env_local = """# Generated by Suna install script

NEXT_PUBLIC_SUPABASE_URL=https://pldcxtmyivlpueddnuml.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.yrYWj-hbqdS3nGTFBA1YvhAtKoY8BOVY1_OXA4_gAtA
NEXT_PUBLIC_BACKEND_URL=http://127.0.0.1:8000/api
NEXT_PUBLIC_URL=http://localhost:3000
NEXT_PUBLIC_ENV_MODE=LOCAL
ADMIN_API_KEY=suna_admin_key_2025_secure_token_xyz789
"""
    
    backup_file(frontend_env_path)
    with open(frontend_env_path, 'w') as f:
        f.write(frontend_env_local)
    print("[SUCCESS] Switched frontend to LOCAL environment")
    
    # Update backend environment mode
    backend_env_path = "backend/.env"
    if os.path.exists(backend_env_path):
        backup_file(backend_env_path)
        with open(backend_env_path, 'r') as f:
            content = f.read()
        
        # Update ENV_MODE to local
        content = content.replace('ENV_MODE=production', 'ENV_MODE=local')
        content = content.replace('ENV_MODE=staging', 'ENV_MODE=local')
        
        with open(backend_env_path, 'w') as f:
            f.write(content)
        print("[SUCCESS] Switched backend to LOCAL environment")
    
    print("[SUCCESS] Switched back to local development environment")

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--local':
        switch_to_local()
    else:
        fix_production_config()

if __name__ == "__main__":
    main()
