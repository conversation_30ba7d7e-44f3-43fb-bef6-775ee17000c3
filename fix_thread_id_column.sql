-- Fix thread_id column mismatch
-- The database has 'id' but the code expects 'thread_id'

BEGIN;

-- Add thread_id column to threads table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'threads' AND column_name = 'thread_id' AND table_schema = 'public') THEN
        -- Add thread_id column as UUID with default value
        ALTER TABLE public.threads ADD COLUMN thread_id UUID DEFAULT extensions.uuid_generate_v4();
        
        -- Update existing rows to have thread_id = id
        UPDATE public.threads SET thread_id = id WHERE thread_id IS NULL;
        
        -- Make thread_id NOT NULL and UNIQUE
        ALTER TABLE public.threads ALTER COLUMN thread_id SET NOT NULL;
        ALTER TABLE public.threads ADD CONSTRAINT threads_thread_id_unique UNIQUE (thread_id);
        
        -- Create index for thread_id
        CREATE INDEX IF NOT EXISTS idx_threads_thread_id ON public.threads(thread_id);
        
        RAISE NOTICE 'Added thread_id column to threads table';
    ELSE
        RAISE NOTICE 'thread_id column already exists in threads table';
    END IF;
END $$;

-- Update messages table to reference thread_id instead of id
DO $$
BEGIN
    -- Check if messages.thread_id references threads.id instead of threads.thread_id
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints tc
               JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
               JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
               WHERE tc.table_name = 'messages' 
               AND tc.constraint_type = 'FOREIGN KEY'
               AND kcu.column_name = 'thread_id'
               AND ccu.table_name = 'threads'
               AND ccu.column_name = 'id') THEN
        
        -- Drop the existing foreign key constraint
        ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_thread_id_fkey;
        
        -- Add new foreign key constraint referencing threads.thread_id
        ALTER TABLE public.messages ADD CONSTRAINT messages_thread_id_fkey 
            FOREIGN KEY (thread_id) REFERENCES public.threads(thread_id) ON DELETE CASCADE;
            
        RAISE NOTICE 'Updated messages.thread_id foreign key to reference threads.thread_id';
    END IF;
END $$;

COMMIT;
