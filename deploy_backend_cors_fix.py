#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deploy Backend CORS Fix

This script helps deploy the updated backend with CORS fixes to production.
It provides multiple deployment options depending on your setup.
"""

import os
import subprocess
import sys

def run_command(cmd, cwd=None, check=True):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            check=check
        )
        return result
    except subprocess.CalledProcessError as e:
        print("Command failed: " + cmd)
        print("Error: " + str(e.stderr))
        return e

def check_docker():
    """Check if Docker is available"""
    try:
        result = run_command("docker --version", check=False)
        return result.returncode == 0
    except:
        return False

def check_git():
    """Check if git is available and repo is clean"""
    try:
        # Check if git is available
        result = run_command("git --version", check=False)
        if result.returncode != 0:
            return False, "Git not available"
        
        # Check if we're in a git repo
        result = run_command("git status --porcelain", check=False)
        if result.returncode != 0:
            return False, "Not in a git repository"
        
        # Check if there are uncommitted changes
        if result.stdout.strip():
            return False, "Uncommitted changes detected"
        
        return True, "Git repository is clean"
    except:
        return False, "Git check failed"

def build_docker_image():
    """Build the Docker image with updated CORS configuration"""
    print("🐳 Building Docker image with CORS fixes...")
    
    # Change to backend directory
    backend_dir = "backend"
    if not os.path.exists(backend_dir):
        print("❌ Backend directory not found")
        return False
    
    # Build the Docker image
    build_cmd = "docker build -t suna-backend:cors-fix ."
    result = run_command(build_cmd, cwd="backend", check=False)
    
    if result.returncode == 0:
        print("✅ Docker image built successfully")
        return True
    else:
        print("❌ Docker build failed")
        print(result.stderr)
        return False

def test_cors_locally():
    """Test CORS configuration locally"""
    print("🧪 Testing CORS configuration locally...")
    
    # Start the container
    print("Starting test container...")
    run_cmd = """docker run -d --name cors-test -p 8001:8000 \
        -e ENV_MODE=production \
        -e SUPABASE_URL=https://pldcxtmyivlpueddnuml.supabase.co \
        -e SUPABASE_ANON_KEY=test \
        -e SUPABASE_SERVICE_ROLE_KEY=test \
        -e REDIS_HOST=localhost \
        -e GROQ_API_KEY=test \
        suna-backend:cors-fix"""
    
    result = run_command(run_cmd, check=False)
    if result.returncode != 0:
        print("❌ Failed to start test container")
        return False
    
    # Wait a moment for startup
    import time
    time.sleep(5)
    
    # Test CORS headers
    test_cmd = """curl -s -I -X OPTIONS \
        -H "Origin: https://aicofounder.site" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: Content-Type" \
        http://localhost:8001/api/health"""
    
    result = run_command(test_cmd, check=False)
    
    # Clean up
    run_command("docker stop cors-test", check=False)
    run_command("docker rm cors-test", check=False)
    
    if result.returncode == 0 and "access-control-allow-origin" in result.stdout.lower():
        print("✅ CORS configuration working correctly")
        return True
    else:
        print("❌ CORS test failed")
        print("Response headers:", result.stdout)
        return False

def deploy_options():
    """Show deployment options"""
    print("\n🚀 Backend Deployment Options:")
    print("=" * 50)
    print()
    print("1. 🐳 Docker Hub / GitHub Container Registry")
    print("   - Build and push image to registry")
    print("   - Update production deployment to use new image")
    print()
    print("2. 🚂 Railway Deployment")
    print("   - Commit changes and push to trigger Railway deployment")
    print("   - Railway will automatically build and deploy")
    print()
    print("3. 🔧 Manual Server Deployment")
    print("   - SSH into production server")
    print("   - Pull latest code and restart services")
    print()
    print("4. ☁️  Cloud Platform (AWS/GCP/Azure)")
    print("   - Update container service with new image")
    print("   - Trigger rolling deployment")
    print()

def railway_deployment():
    """Deploy via Railway (git push)"""
    print("🚂 Deploying via Railway...")
    
    # Check git status
    git_ok, git_msg = check_git()
    if not git_ok:
        print("❌ Git issue: " + git_msg)
        if "uncommitted changes" in git_msg.lower():
            print("Please commit your CORS fixes first:")
            print("  git add .")
            print("  git commit -m 'Fix CORS configuration for production'")
            print("  git push")
        return False
    
    # Push to trigger deployment
    print("Pushing to trigger Railway deployment...")
    result = run_command("git push", check=False)
    
    if result.returncode == 0:
        print("✅ Code pushed successfully")
        print("🔄 Railway deployment should start automatically")
        print("📊 Check your Railway dashboard for deployment status")
        return True
    else:
        print("❌ Git push failed")
        print(result.stderr)
        return False

def docker_registry_deployment():
    """Deploy via Docker registry"""
    print("🐳 Deploying via Docker registry...")
    
    # Build image
    if not build_docker_image():
        return False
    
    # Tag for registry
    registry_tag = "ghcr.io/suna-ai/suna-backend:cors-fix"
    tag_cmd = "docker tag suna-backend:cors-fix " + registry_tag
    result = run_command(tag_cmd, check=False)
    
    if result.returncode != 0:
        print("❌ Failed to tag image")
        return False
    
    print("✅ Image tagged as " + registry_tag)
    print()
    print("Next steps:")
    print("1. Push to registry: docker push " + registry_tag)
    print("2. Update production deployment to use the new image")
    print("3. Restart production services")
    
    return True

def manual_deployment_instructions():
    """Show manual deployment instructions"""
    print("🔧 Manual Deployment Instructions:")
    print("=" * 40)
    print()
    print("1. SSH into your production server")
    print("2. Navigate to your application directory")
    print("3. Pull the latest code:")
    print("   git pull origin main")
    print()
    print("4. Rebuild and restart the backend:")
    print("   cd backend")
    print("   docker compose down")
    print("   docker compose up --build -d")
    print()
    print("5. Verify the deployment:")
    print("   curl -I -X OPTIONS \\")
    print("     -H 'Origin: https://aicofounder.site' \\")
    print("     -H 'Access-Control-Request-Method: GET' \\")
    print("     https://api.ai-co-founder.com/api/health")
    print()
    print("6. Check for 'access-control-allow-origin' header in response")

def main():
    print("🔧 Backend CORS Fix Deployment")
    print("=" * 40)
    print()
    
    # Check prerequisites
    print("Checking prerequisites...")
    
    if not check_docker():
        print("⚠️  Docker not available - some options will be limited")
    else:
        print("✅ Docker available")
    
    git_ok, git_msg = check_git()
    if git_ok:
        print("✅ Git repository ready")
    else:
        print("⚠️  Git: " + git_msg)
    
    print()
    
    # Show deployment options
    deploy_options()
    
    # Get user choice
    while True:
        choice = input("Choose deployment method (1-4) or 'q' to quit: ").strip()
        
        if choice.lower() == 'q':
            print("Deployment cancelled.")
            return
        
        if choice == '1':
            docker_registry_deployment()
            break
        elif choice == '2':
            railway_deployment()
            break
        elif choice == '3':
            manual_deployment_instructions()
            break
        elif choice == '4':
            print("Please refer to your cloud platform's documentation for container deployment.")
            break
        else:
            print("Invalid choice. Please enter 1-4 or 'q'.")
    
    print()
    print("📋 Post-Deployment Checklist:")
    print("- ✅ Verify CORS headers are present in API responses")
    print("- ✅ Test frontend can make API calls without CORS errors")
    print("- ✅ Check production logs for any errors")
    print("- ✅ Test user authentication and account creation flows")

if __name__ == "__main__":
    main()
