#!/usr/bin/env python3
"""
Simple script to apply Supabase migrations manually
"""
import os
import sys
import psycopg2
from urllib.parse import urlparse
import glob

def get_db_connection():
    """Get database connection from environment variables"""
    supabase_url = os.getenv('SUPABASE_URL')
    service_role_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

    if not supabase_url or not service_role_key:
        print("Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        sys.exit(1)

    # Parse the Supabase URL to get the database connection string
    # Supabase URL format: https://project-ref.supabase.co
    parsed = urlparse(supabase_url)
    project_ref = parsed.hostname.split('.')[0]

    # Construct PostgreSQL connection string for Supabase
    # Use the correct hostname format for Supabase database
    db_url = f"postgresql://postgres.{project_ref}:{service_role_key}@aws-0-us-west-1.pooler.supabase.com:6543/postgres"

    print(f"Connecting to database: {project_ref}")

    try:
        conn = psycopg2.connect(db_url)
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        print("Trying alternative connection format...")

        # Try alternative connection format
        alt_db_url = f"postgresql://postgres:{service_role_key}@db.{project_ref}.supabase.co:5432/postgres"
        try:
            conn = psycopg2.connect(alt_db_url)
            return conn
        except Exception as e2:
            print(f"Alternative connection also failed: {e2}")
            sys.exit(1)

def apply_migration(conn, migration_file):
    """Apply a single migration file"""
    print(f"Applying migration: {os.path.basename(migration_file)}")
    
    try:
        with open(migration_file, 'r') as f:
            sql_content = f.read()
        
        cursor = conn.cursor()
        cursor.execute(sql_content)
        conn.commit()
        cursor.close()
        print(f"✅ Successfully applied: {os.path.basename(migration_file)}")
        return True
    except Exception as e:
        print(f"❌ Error applying {os.path.basename(migration_file)}: {e}")
        conn.rollback()
        return False

def main():
    """Main function to apply all migrations"""
    # Load environment variables from backend/.env
    env_file = 'backend/.env'
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
    
    print("🚀 Starting Supabase migration process...")
    
    # Get database connection
    conn = get_db_connection()
    
    # Get all migration files in order
    migration_dir = 'backend/supabase/migrations'
    migration_files = sorted(glob.glob(os.path.join(migration_dir, '*.sql')))
    
    if not migration_files:
        print("No migration files found!")
        sys.exit(1)
    
    print(f"Found {len(migration_files)} migration files")
    
    # Apply migrations one by one
    success_count = 0
    for migration_file in migration_files:
        if apply_migration(conn, migration_file):
            success_count += 1
        else:
            print(f"Stopping at failed migration: {os.path.basename(migration_file)}")
            break
    
    conn.close()
    
    print(f"\n🎉 Applied {success_count}/{len(migration_files)} migrations successfully")
    
    if success_count == len(migration_files):
        print("✅ All migrations applied successfully!")
        print("\n⚠️  IMPORTANT: You must manually expose the 'basejump' schema in your Supabase dashboard:")
        print("   1. Go to Project Settings -> API -> Exposed schemas")
        print("   2. Add 'basejump' to the list of exposed schemas")
        print("   3. Save the changes")
    else:
        print("❌ Some migrations failed. Please check the errors above.")

if __name__ == "__main__":
    main()
