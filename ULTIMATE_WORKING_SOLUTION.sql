-- =====================================================
-- ULTIMATE WORKING SOLUTION FOR SUNA AGENTS
-- =====================================================
-- This solution analyzes the actual foreign key relationships and works with them

-- Ensure the account exists
INSERT INTO basejump.accounts (id, primary_owner_user_id, name, slug, created_at, updated_at)
VALUES (
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'cd4d9095-2924-414f-9609-f201507f965e'::uuid,
    'Default Account',
    'default-account',
    now(),
    now()
) ON CONFLICT (id) DO NOTHING;

-- Analyze the actual table structure and foreign key relationships
DO $$
DECLARE
    col_record record;
    fk_record record;
    agents_pk_column text;
    agent_versions_fk_column text;
    agents_fk_target text;
BEGIN
    RAISE NOTICE '=== ANALYZING ACTUAL DATABASE STRUCTURE ===';
    
    -- Check agents table structure
    RAISE NOTICE 'Agents table columns:';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'agents' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'agents.%: % (nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
    
    -- Check agent_versions table structure
    RAISE NOTICE 'Agent_versions table columns:';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'agent_versions' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'agent_versions.%: % (nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
    
    -- Find the primary key of agents table
    SELECT column_name INTO agents_pk_column
    FROM information_schema.key_column_usage kcu
    JOIN information_schema.table_constraints tc ON kcu.constraint_name = tc.constraint_name
    WHERE tc.table_name = 'agents' 
    AND tc.table_schema = 'public' 
    AND tc.constraint_type = 'PRIMARY KEY';
    
    RAISE NOTICE 'Agents table primary key: %', agents_pk_column;
    
    -- Find the foreign key relationship from agent_versions to agents
    FOR fk_record IN
        SELECT 
            kcu.column_name as fk_column,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'agent_versions'
        AND tc.table_schema = 'public'
        AND ccu.table_name = 'agents'
    LOOP
        agent_versions_fk_column := fk_record.fk_column;
        agents_fk_target := fk_record.foreign_column_name;
        RAISE NOTICE 'FK: agent_versions.% -> agents.%', fk_record.fk_column, fk_record.foreign_column_name;
    END LOOP;
    
    -- Store the relationship info for later use
    IF agent_versions_fk_column IS NULL THEN
        RAISE NOTICE 'WARNING: No foreign key relationship found from agent_versions to agents';
    END IF;
END $$;

-- Clean up existing data safely
DO $$
BEGIN
    -- Check if current_version_id column exists before trying to update it
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'current_version_id' AND table_schema = 'public') THEN
        UPDATE agents SET current_version_id = NULL WHERE current_version_id IS NOT NULL;
    END IF;
    
    DELETE FROM agent_versions;
    DELETE FROM agents;
    
    RAISE NOTICE 'Cleaned up existing data';
END $$;

-- Add missing columns that the API expects
DO $$
BEGIN
    -- Add system_prompt if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'system_prompt' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN system_prompt text;
        RAISE NOTICE 'Added system_prompt column to agents table';
    END IF;
    
    -- Add configured_mcps if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'configured_mcps' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN configured_mcps jsonb DEFAULT '[]'::jsonb;
        RAISE NOTICE 'Added configured_mcps column to agents table';
    END IF;
    
    -- Add agentpress_tools if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'agentpress_tools' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN agentpress_tools jsonb DEFAULT '{}'::jsonb;
        RAISE NOTICE 'Added agentpress_tools column to agents table';
    END IF;
    
    -- Add is_default if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_default' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN is_default boolean DEFAULT false;
        RAISE NOTICE 'Added is_default column to agents table';
    END IF;
    
    -- Add avatar if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'avatar' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN avatar varchar(10);
        RAISE NOTICE 'Added avatar column to agents table';
    END IF;
    
    -- Add avatar_color if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'avatar_color' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN avatar_color varchar(7);
        RAISE NOTICE 'Added avatar_color column to agents table';
    END IF;
    
    -- Add current_version_id if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'current_version_id' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN current_version_id uuid;
        RAISE NOTICE 'Added current_version_id column to agents table';
    END IF;
    
    -- Add version_count if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'version_count' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN version_count integer DEFAULT 1;
        RAISE NOTICE 'Added version_count column to agents table';
    END IF;
    
    -- Add agent_id if it doesn't exist (for API compatibility)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'agent_id' AND table_schema = 'public') THEN
        ALTER TABLE agents ADD COLUMN agent_id uuid DEFAULT gen_random_uuid();
        RAISE NOTICE 'Added agent_id column to agents table';
    END IF;
END $$;

-- Create agents using minimal fields first, then update with additional data
DO $$
DECLARE
    suna_record record;
    code_record record;
    marketing_record record;
    suna_version_id uuid;
    code_version_id uuid;
    marketing_version_id uuid;
    agents_pk_column text;
    agent_versions_fk_column text;
    versions_pk_column text;
BEGIN
    RAISE NOTICE '=== CREATING AGENTS ===';
    
    -- Find the primary key column of agents table
    SELECT column_name INTO agents_pk_column
    FROM information_schema.key_column_usage kcu
    JOIN information_schema.table_constraints tc ON kcu.constraint_name = tc.constraint_name
    WHERE tc.table_name = 'agents' 
    AND tc.table_schema = 'public' 
    AND tc.constraint_type = 'PRIMARY KEY';
    
    -- Find the foreign key column in agent_versions
    SELECT kcu.column_name INTO agent_versions_fk_column
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'agent_versions'
    AND tc.table_schema = 'public'
    AND ccu.table_name = 'agents';
    
    RAISE NOTICE 'Using agents.% as primary key, agent_versions.% as foreign key', agents_pk_column, agent_versions_fk_column;
    
    -- Insert agents with minimal required fields including agent_id
    INSERT INTO agents (agent_id, account_id, name, description, role)
    VALUES
        (gen_random_uuid(), 'cd4d9095-2924-414f-9609-f201507f965e'::uuid, 'Suna', 'Your AI Co-Founder assistant', 'assistant'),
        (gen_random_uuid(), 'cd4d9095-2924-414f-9609-f201507f965e'::uuid, 'Code Assistant', 'A specialized AI assistant for coding', 'assistant'),
        (gen_random_uuid(), 'cd4d9095-2924-414f-9609-f201507f965e'::uuid, 'Marketing Advisor', 'An AI assistant for marketing strategy', 'assistant');
    
    -- Get the created agents
    SELECT * INTO suna_record FROM agents WHERE name = 'Suna' AND account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    SELECT * INTO code_record FROM agents WHERE name = 'Code Assistant' AND account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    SELECT * INTO marketing_record FROM agents WHERE name = 'Marketing Advisor' AND account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    
    RAISE NOTICE 'Created agents: Suna (%), Code (%), Marketing (%)',
        CASE WHEN agents_pk_column = 'id' THEN suna_record.id::text ELSE suna_record.agent_id::text END,
        CASE WHEN agents_pk_column = 'id' THEN code_record.id::text ELSE code_record.agent_id::text END,
        CASE WHEN agents_pk_column = 'id' THEN marketing_record.id::text ELSE marketing_record.agent_id::text END;
    
    -- Create agent versions using the correct foreign key reference
    IF agent_versions_fk_column = 'agent_id' AND agents_pk_column = 'id' THEN
        -- agent_versions.agent_id references agents.id
        INSERT INTO agent_versions (agent_id, version_number, name, system_prompt)
        VALUES 
            (suna_record.id, 1, 'Suna v1.0', 'You are Suna, an AI Co-Founder assistant.'),
            (code_record.id, 1, 'Code Assistant v1.0', 'You are a Code Assistant AI.'),
            (marketing_record.id, 1, 'Marketing Advisor v1.0', 'You are a Marketing Advisor AI.');
    ELSIF agent_versions_fk_column = 'agent_id' AND agents_pk_column = 'agent_id' THEN
        -- agent_versions.agent_id references agents.agent_id
        INSERT INTO agent_versions (agent_id, version_number, name, system_prompt)
        VALUES 
            (suna_record.agent_id, 1, 'Suna v1.0', 'You are Suna, an AI Co-Founder assistant.'),
            (code_record.agent_id, 1, 'Code Assistant v1.0', 'You are a Code Assistant AI.'),
            (marketing_record.agent_id, 1, 'Marketing Advisor v1.0', 'You are a Marketing Advisor AI.');
    ELSE
        RAISE EXCEPTION 'Unknown foreign key relationship: %.% -> %.%', 'agent_versions', agent_versions_fk_column, 'agents', agents_pk_column;
    END IF;
    
    -- Find the primary key column of agent_versions table
    SELECT column_name INTO versions_pk_column
    FROM information_schema.key_column_usage kcu
    JOIN information_schema.table_constraints tc ON kcu.constraint_name = tc.constraint_name
    WHERE tc.table_name = 'agent_versions'
    AND tc.table_schema = 'public'
    AND tc.constraint_type = 'PRIMARY KEY';

    RAISE NOTICE 'Agent_versions table primary key: %', versions_pk_column;

    -- Get version primary keys using the correct column
    IF versions_pk_column = 'version_id' THEN
        SELECT version_id INTO suna_version_id FROM agent_versions WHERE name = 'Suna v1.0';
        SELECT version_id INTO code_version_id FROM agent_versions WHERE name = 'Code Assistant v1.0';
        SELECT version_id INTO marketing_version_id FROM agent_versions WHERE name = 'Marketing Advisor v1.0';
    ELSIF versions_pk_column = 'id' THEN
        SELECT id INTO suna_version_id FROM agent_versions WHERE name = 'Suna v1.0';
        SELECT id INTO code_version_id FROM agent_versions WHERE name = 'Code Assistant v1.0';
        SELECT id INTO marketing_version_id FROM agent_versions WHERE name = 'Marketing Advisor v1.0';
    ELSE
        RAISE EXCEPTION 'Unknown primary key column for agent_versions: %', versions_pk_column;
    END IF;
    
    -- Update agents with all the additional fields
    UPDATE agents SET
        system_prompt = 'You are Suna, an AI Co-Founder assistant. You help entrepreneurs and startups with business strategy, product development, market analysis, and general startup guidance. Be helpful, insightful, and supportive.',
        configured_mcps = '[]'::jsonb,
        agentpress_tools = '{}'::jsonb,
        is_default = true,
        avatar = '🚀',
        avatar_color = '#3B82F6',
        current_version_id = suna_version_id,
        version_count = 1
    WHERE name = 'Suna';

    UPDATE agents SET
        system_prompt = 'You are a Code Assistant AI. You help with programming, debugging, code reviews, architecture decisions, and technical problem-solving. Provide clear, practical coding solutions and explanations.',
        configured_mcps = '[]'::jsonb,
        agentpress_tools = '{}'::jsonb,
        is_default = false,
        avatar = '💻',
        avatar_color = '#10B981',
        current_version_id = code_version_id,
        version_count = 1
    WHERE name = 'Code Assistant';

    UPDATE agents SET
        system_prompt = 'You are a Marketing Advisor AI. You help with marketing strategy, content creation, social media planning, SEO, and growth tactics. Provide actionable marketing advice and creative solutions.',
        configured_mcps = '[]'::jsonb,
        agentpress_tools = '{}'::jsonb,
        is_default = false,
        avatar = '📈',
        avatar_color = '#F59E0B',
        current_version_id = marketing_version_id,
        version_count = 1
    WHERE name = 'Marketing Advisor';
    
    RAISE NOTICE 'Successfully created and configured 3 agents with versions!';
END $$;

-- Final verification
DO $$
DECLARE
    agent_count integer;
    version_count integer;
    agent_rec record;
BEGIN
    SELECT COUNT(*) INTO agent_count FROM agents WHERE account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    SELECT COUNT(*) INTO version_count FROM agent_versions av JOIN agents a ON av.agent_id = a.id WHERE a.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid;
    
    RAISE NOTICE '=== FINAL VERIFICATION ===';
    RAISE NOTICE 'RESULT: % agents created, % versions created', agent_count, version_count;
    
    FOR agent_rec IN 
        SELECT a.name, a.id, a.agent_id, a.current_version_id, a.system_prompt IS NOT NULL as has_system_prompt
        FROM agents a 
        WHERE a.account_id = 'cd4d9095-2924-414f-9609-f201507f965e'::uuid
        ORDER BY a.name
    LOOP
        RAISE NOTICE 'Agent: % | PK: % | agent_id: % | Version: % | System Prompt: %', 
            agent_rec.name, agent_rec.id, agent_rec.agent_id, agent_rec.current_version_id, agent_rec.has_system_prompt;
    END LOOP;
END $$;

-- Refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';
