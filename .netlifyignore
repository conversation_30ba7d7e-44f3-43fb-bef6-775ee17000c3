# Ignore mise/rtx configuration files
mise.toml
.mise.toml
rtx.toml
.rtx.toml
.tool-versions

# Ignore backend directory
backend/

# Ignore development files
.env.local
.env.development.local

# Dependencies (already in node_modules)
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Documentation and config files
*.md
docs/
README.md
.prettierrc
.eslintrc*

# Test files
test/
tests/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Logs and cache
logs/
*.log
.cache/
.parcel-cache/
.eslintcache

# Git files
.git/
.gitignore

# Python files
*.py
*.pyc
__pycache__/

# Temporary files
tmp/
temp/
*.tmp
*.temp
