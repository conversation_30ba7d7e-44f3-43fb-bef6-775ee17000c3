#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test CORS Fix

This script tests whether the CORS configuration is working correctly
by making requests to the production API from the frontend domain.
"""

import requests
import sys

def test_cors_headers(api_url, origin):
    """Test CORS headers for a specific endpoint"""
    print("Testing CORS for " + api_url + " from origin " + origin)
    
    try:
        # Test preflight request (OPTIONS)
        headers = {
            'Origin': origin,
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type, Authorization'
        }
        
        response = requests.options(api_url, headers=headers, timeout=10)
        
        print("  Status Code: " + str(response.status_code))

        # Check CORS headers
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        }

        print("  CORS Headers:")
        for header, value in cors_headers.items():
            if value:
                print("    ✅ " + header + ": " + value)
            else:
                print("    ❌ " + header + ": Not present")

        # Check if origin is allowed
        allowed_origin = cors_headers['Access-Control-Allow-Origin']
        if allowed_origin == origin or allowed_origin == '*':
            print("  ✅ Origin " + origin + " is allowed")
            return True
        else:
            print("  ❌ Origin " + origin + " is NOT allowed")
            return False
            
    except requests.exceptions.RequestException as e:
        print("  ❌ Request failed: " + str(e))
        return False

def test_actual_request(api_url, origin):
    """Test an actual GET request"""
    print("Testing actual GET request to " + api_url)

    try:
        headers = {'Origin': origin}
        response = requests.get(api_url, headers=headers, timeout=10)

        print("  Status Code: " + str(response.status_code))

        # Check if CORS headers are present in actual response
        cors_origin = response.headers.get('Access-Control-Allow-Origin')
        if cors_origin:
            print("  ✅ Access-Control-Allow-Origin: " + cors_origin)
            return True
        else:
            print("  ❌ No Access-Control-Allow-Origin header in response")
            return False

    except requests.exceptions.RequestException as e:
        print("  ❌ Request failed: " + str(e))
        return False

def main():
    print("🧪 Testing CORS Configuration")
    print("=" * 40)
    print()
    
    # Configuration
    api_base = "https://api.ai-co-founder.com/api"
    frontend_origin = "https://aicofounder.site"
    
    # Test endpoints
    endpoints = [
        api_base + "/health",
        api_base + "/feature-flags/custom_agents",
        api_base + "/projects",
        api_base + "/threads"
    ]
    
    print("Frontend Origin: " + frontend_origin)
    print("API Base URL: " + api_base)
    print()

    all_passed = True

    for endpoint in endpoints:
        print("📍 Testing endpoint: " + endpoint)
        print("-" * 50)
        
        # Test preflight request
        preflight_ok = test_cors_headers(endpoint, frontend_origin)
        
        # Test actual request
        actual_ok = test_actual_request(endpoint, frontend_origin)
        
        endpoint_ok = preflight_ok and actual_ok
        all_passed = all_passed and endpoint_ok
        
        if endpoint_ok:
            print("  🎉 CORS working for this endpoint")
        else:
            print("  💥 CORS issues detected for this endpoint")
        
        print()
    
    print("=" * 50)
    if all_passed:
        print("🎉 ALL CORS TESTS PASSED!")
        print("Your frontend should now be able to communicate with the backend.")
    else:
        print("💥 SOME CORS TESTS FAILED!")
        print("The backend deployment may not have the updated CORS configuration.")
        print()
        print("Possible solutions:")
        print("1. Ensure the backend has been redeployed with the CORS fixes")
        print("2. Check that ENV_MODE=production in the backend environment")
        print("3. Verify the backend is using the updated api.py file")
        print("4. Check backend logs for any startup errors")
    
    print()
    print("🔧 Manual CORS Test:")
    print("You can also test CORS manually in your browser:")
    print("1. Open browser developer tools")
    print("2. Go to Console tab")
    print("3. Run this command:")
    print("   fetch('" + api_base + "/health')")
    print("4. Check if you get CORS errors")

if __name__ == "__main__":
    main()
