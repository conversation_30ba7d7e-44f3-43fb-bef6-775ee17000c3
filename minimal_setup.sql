-- MINIMAL SUPABASE SETUP FOR SUNA
-- Copy and paste this entire SQL into your Supabase SQL Editor and run it

-- Create basejump schema
CREATE SCHEMA IF NOT EXISTS basejump;
GRANT USAGE ON SCHEMA basejump to authenticated;
GRANT USAGE ON SCHEMA basejump to service_role;

-- Create account role enum
DO $$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_type t JOIN pg_namespace n ON n.oid = t.typnamespace 
                  WHERE t.typname = 'account_role' AND n.nspname = 'basejump') THEN
        CREATE TYPE basejump.account_role AS ENUM ('owner', 'member');
    end if;
end; $$;

-- Create accounts table
CREATE TABLE IF NOT EXISTS basejump.accounts (
    id                    uuid unique                NOT NULL DEFAULT extensions.uuid_generate_v4(),
    primary_owner_user_id uuid references auth.users not null default auth.uid(),
    name                  text,
    slug                  text unique,
    personal_account      boolean                             default false not null,
    updated_at            timestamp with time zone,
    created_at            timestamp with time zone,
    created_by            uuid references auth.users,
    updated_by            uuid references auth.users,
    private_metadata      jsonb                               default '{}'::jsonb,
    public_metadata       jsonb                               default '{}'::jsonb,
    PRIMARY KEY (id)
);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE basejump.accounts TO authenticated, service_role;

-- Enable RLS
ALTER TABLE basejump.accounts ENABLE ROW LEVEL SECURITY;

-- Create basic policies
CREATE POLICY "Users can view accounts they belong to" ON basejump.accounts
    FOR SELECT TO authenticated USING (
        auth.uid() = primary_owner_user_id OR 
        auth.uid() IN (
            SELECT user_id FROM basejump.account_user 
            WHERE account_id = basejump.accounts.id
        )
    );

-- Create account_user table for memberships
CREATE TABLE IF NOT EXISTS basejump.account_user (
    user_id uuid references auth.users not null,
    account_id uuid references basejump.accounts not null,
    account_role basejump.account_role not null default 'member',
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users,
    PRIMARY KEY (user_id, account_id)
);

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE basejump.account_user TO authenticated, service_role;
ALTER TABLE basejump.account_user ENABLE ROW LEVEL SECURITY;

-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.projects TO authenticated, service_role;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Create threads table
CREATE TABLE IF NOT EXISTS public.threads (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    project_id uuid REFERENCES public.projects(id) ON DELETE CASCADE,
    title text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users
);

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.threads TO authenticated, service_role;
ALTER TABLE public.threads ENABLE ROW LEVEL SECURITY;

-- Create get_accounts function
CREATE OR REPLACE FUNCTION public.get_accounts()
RETURNS TABLE(
    id uuid,
    name text,
    slug text,
    personal_account boolean,
    created_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        a.name,
        a.slug,
        a.personal_account,
        a.created_at
    FROM basejump.accounts a
    WHERE a.primary_owner_user_id = auth.uid()
       OR auth.uid() IN (
           SELECT au.user_id 
           FROM basejump.account_user au 
           WHERE au.account_id = a.id
       );
END;
$$;

GRANT EXECUTE ON FUNCTION public.get_accounts() TO authenticated;

-- Create basic policies for projects and threads
CREATE POLICY "Users can view projects in their accounts" ON public.projects
    FOR SELECT TO authenticated USING (
        account_id IN (SELECT id FROM basejump.accounts WHERE primary_owner_user_id = auth.uid())
    );

CREATE POLICY "Users can view threads in their accounts" ON public.threads
    FOR SELECT TO authenticated USING (
        account_id IN (SELECT id FROM basejump.accounts WHERE primary_owner_user_id = auth.uid())
    );

-- Insert a default personal account for existing users
INSERT INTO basejump.accounts (primary_owner_user_id, name, personal_account)
SELECT 
    id,
    COALESCE(raw_user_meta_data->>'full_name', email, 'Personal Account'),
    true
FROM auth.users
WHERE id NOT IN (SELECT primary_owner_user_id FROM basejump.accounts)
ON CONFLICT DO NOTHING;

-- Create timestamp triggers
CREATE OR REPLACE FUNCTION public.trigger_set_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    if TG_OP = 'INSERT' then
        NEW.created_at = now();
        NEW.updated_at = now();
    else
        NEW.updated_at = now();
        NEW.created_at = OLD.created_at;
    end if;
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Apply timestamp triggers
CREATE TRIGGER set_timestamps_accounts BEFORE INSERT OR UPDATE ON basejump.accounts
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

CREATE TRIGGER set_timestamps_projects BEFORE INSERT OR UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

CREATE TRIGGER set_timestamps_threads BEFORE INSERT OR UPDATE ON public.threads
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();
