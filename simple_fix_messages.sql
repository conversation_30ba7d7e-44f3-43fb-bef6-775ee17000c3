-- Simple fix for messages table schema
-- Add the missing 'type' column that the application expects

-- Check if 'type' column exists, if not add it
ALTER TABLE messages ADD COLUMN IF NOT EXISTS type TEXT NOT NULL DEFAULT 'user';

-- If we have a 'role' column instead of 'type', rename it
-- (This will fail if 'type' already exists, which is fine)
-- ALTER TABLE messages RENAME COLUMN role TO type;

-- Ensure is_llm_message column exists
ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;

-- Ensure content is JSONB
-- ALTER TABLE messages ALTER COLUMN content TYPE JSONB USING content::jsonb;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);
CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);

-- Update existing messages to have proper values
UPDATE messages 
SET type = 'user' 
WHERE type IS NULL OR type = '';

UPDATE messages 
SET is_llm_message = false 
WHERE type = 'user' AND is_llm_message = true;

UPDATE messages 
SET is_llm_message = true 
WHERE type IN ('assistant', 'system', 'tool', 'status') AND is_llm_message = false;
