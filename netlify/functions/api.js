// Netlify Functions API Handler
// This replaces the FastAPI backend with serverless functions

// Initialize Supabase client
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

let supabase;
console.log('Environment check:', {
  supabaseUrl: supabaseUrl ? 'SET' : 'MISSING',
  supabaseKey: supabaseKey ? 'SET' : 'MISSING',
  nodeEnv: process.env.NODE_ENV
});

if (supabaseUrl && supabaseKey) {
  try {
    supabase = createClient(supabaseUrl, supabaseKey);
    console.log('Supabase client initialized successfully');
  } catch (error) {
    console.error('Error initializing Supabase client:', error);
  }
} else {
  console.warn('Supabase not configured - missing URL or key');
}

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Helper function to create response
const createResponse = (statusCode, body, additionalHeaders = {}) => {
  return {
    statusCode,
    headers: {
      ...corsHeaders,
      'Content-Type': 'application/json',
      ...additionalHeaders,
    },
    body: JSON.stringify(body),
  };
};

// Helper function to generate UUID v4 - Updated for proper UUID generation
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Helper function to extract user ID from JWT - Fixed to always return valid UUIDs
const getUserIdFromToken = (authHeader) => {
  if (!authHeader) {
    // Return a consistent UUID for anonymous users
    console.log('[getUserIdFromToken] No auth header, returning anonymous UUID');
    return '00000000-0000-0000-0000-000000000001';
  }

  try {
    // Extract the token from "Bearer <token>"
    const token = authHeader.replace('Bearer ', '');

    // Decode JWT payload (simplified - in production, verify signature)
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());

    // Return the user ID from the token
    const userId = payload.sub || '00000000-0000-0000-0000-000000000001';
    console.log('[getUserIdFromToken] Extracted user ID:', userId);
    return userId;
  } catch (error) {
    console.error('Error parsing JWT token:', error);
    // Return a consistent UUID for invalid tokens
    console.log('[getUserIdFromToken] Error parsing token, returning anonymous UUID');
    return '00000000-0000-0000-0000-000000000001';
  }
};

// Route handlers
const routes = {
  // Health check endpoint
  'GET /health': async () => {
    return createResponse(200, {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'netlify-functions',
      message: 'AI Co-Founder API is running on Netlify Functions'
    });
  },

  // Feature flags endpoints
  'GET /feature-flags/custom_agents': async () => {
    return createResponse(200, {
      enabled: true,
      description: 'Custom agents feature'
    });
  },

  'GET /feature-flags/agent_marketplace': async () => {
    return createResponse(200, {
      enabled: true,
      description: 'Agent marketplace feature'
    });
  },

  'GET /feature-flags/knowledge_base': async () => {
    return createResponse(200, {
      enabled: true,
      description: 'Knowledge base feature'
    });
  },

  // Direct endpoint handlers (for backward compatibility)
  'GET /custom_agents': async () => {
    return createResponse(200, {
      enabled: true,
      description: 'Custom agents feature'
    });
  },

  'GET /agent_marketplace': async () => {
    return createResponse(200, {
      enabled: true,
      description: 'Agent marketplace feature'
    });
  },

  // Billing endpoints
  'GET /billing/subscription': async (event) => {
    // Get user ID (simplified for testing)
    const userId = getUserIdFromToken(event.headers.authorization);

    return createResponse(200, {
      plan: 'free',
      status: 'active',
      current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      cancel_at_period_end: false,
      usage: {
        messages: 45,
        limit: 100
      },
      payment_method: null,
      next_invoice: null,
      credits_remaining: 100,
      credits_total: 100
    });
  },

  // Create Stripe checkout session
  'POST /billing/create-checkout-session': async (event) => {
    try {
      let requestData = {};
      if (event.body) {
        try {
          requestData = JSON.parse(event.body);
        } catch (e) {
          return createResponse(400, { error: 'Invalid JSON in request body' });
        }
      }

      const { priceId, planName, successUrl, cancelUrl } = requestData;

      if (!priceId || !planName) {
        return createResponse(400, { error: 'priceId and planName are required' });
      }

      // Mock Stripe checkout session
      const sessionId = `cs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const checkoutUrl = `https://checkout.stripe.com/pay/${sessionId}`;

      return createResponse(200, {
        sessionId: sessionId,
        url: checkoutUrl,
        success_url: successUrl || 'https://aicofounder.site/dashboard?payment=success',
        cancel_url: cancelUrl || 'https://aicofounder.site/settings/billing?payment=cancelled'
      });
    } catch (error) {
      console.error('Error creating checkout session:', error);
      return createResponse(500, { error: 'Failed to create checkout session' });
    }
  },

  // Handle successful payment
  'POST /billing/payment-success': async (event) => {
    try {
      let requestData = {};
      if (event.body) {
        try {
          requestData = JSON.parse(event.body);
        } catch (e) {
          return createResponse(400, { error: 'Invalid JSON in request body' });
        }
      }

      const { sessionId, planName } = requestData;
      const userId = getUserIdFromToken(event.headers.authorization);

      // Mock successful payment processing
      console.log(`Payment successful for user ${userId}, plan: ${planName}, session: ${sessionId}`);

      return createResponse(200, {
        success: true,
        subscription: {
          id: `sub_${Date.now()}`,
          plan: planName,
          status: 'active',
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          created: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error processing payment success:', error);
      return createResponse(500, { error: 'Failed to process payment' });
    }
  },

  'GET /billing/available-models': async () => {
    return createResponse(200, {
      models: [
        {
          id: 'gpt-4',
          name: 'GPT-4',
          provider: 'openai',
          cost_per_token: 0.00003,
          available: true
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'openai',
          cost_per_token: 0.000002,
          available: true
        },
        {
          id: 'claude-3-sonnet',
          name: 'Claude 3 Sonnet',
          provider: 'anthropic',
          cost_per_token: 0.000015,
          available: true
        }
      ]
    });
  },

  'GET /billing/usage-logs': async (event) => {
    // Return realistic usage logs with varied data
    const now = new Date();
    const logs = [];

    // Generate 20 realistic usage entries over the past week
    for (let i = 0; i < 20; i++) {
      const hoursAgo = Math.floor(Math.random() * 168); // Random time in past week
      const timestamp = new Date(now.getTime() - (hoursAgo * 60 * 60 * 1000));

      const models = [
        { name: 'gpt-4o', cost_per_token: 0.00003, provider: 'OpenAI' },
        { name: 'gpt-4o-mini', cost_per_token: 0.000015, provider: 'OpenAI' },
        { name: 'claude-3.5-sonnet', cost_per_token: 0.000015, provider: 'Anthropic' },
        { name: 'claude-3-haiku', cost_per_token: 0.0000025, provider: 'Anthropic' },
        { name: 'gemini-2.0-flash-exp', cost_per_token: 0.0000075, provider: 'Google' },
        { name: 'o1-preview', cost_per_token: 0.00015, provider: 'OpenAI' }
      ];

      const operations = ['chat_completion', 'code_generation', 'text_analysis', 'summarization', 'translation'];
      const model = models[Math.floor(Math.random() * models.length)];
      const operation = operations[Math.floor(Math.random() * operations.length)];
      const tokens = Math.floor(Math.random() * 2000) + 100; // 100-2100 tokens
      const cost = tokens * model.cost_per_token;

      const promptTokens = Math.floor(tokens * 0.7); // 70% prompt tokens
      const completionTokens = tokens - promptTokens; // 30% completion tokens

      logs.push({
        message_id: `msg_${Date.now()}_${i}`,
        thread_id: `thread_${Math.random().toString(36).substr(2, 9)}`,
        created_at: timestamp.toISOString(),
        content: {
          usage: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens
          },
          model: model.name
        },
        total_tokens: tokens,
        estimated_cost: parseFloat(cost.toFixed(6)),
        project_id: `project_${Math.random().toString(36).substr(2, 9)}`
      });
    }

    // Sort by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    const totalCost = logs.reduce((sum, log) => sum + log.cost, 0);
    const totalTokens = logs.reduce((sum, log) => sum + log.tokens_used, 0);

    return createResponse(200, {
      logs: logs,
      has_more: false,
      total_cost: parseFloat(totalCost.toFixed(6)),
      total_tokens: totalTokens,
      period: 'current_month',
      summary: {
        total_requests: logs.length,
        unique_models: [...new Set(logs.map(log => log.model))].length,
        avg_tokens_per_request: Math.floor(totalTokens / logs.length)
      }
    });
  },

  // Direct billing endpoints (for backward compatibility with direct calls)
  'GET /subscription': async (event) => {
    return createResponse(200, {
      plan: 'free',
      status: 'active',
      credits_remaining: 100,
      credits_total: 100
    });
  },

  'GET /available-models': async () => {
    return createResponse(200, {
      models: [
        {
          id: 'gpt-4',
          name: 'GPT-4',
          provider: 'openai',
          cost_per_token: 0.00003,
          available: true
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'openai',
          cost_per_token: 0.000002,
          available: true
        }
      ]
    });
  },

  // Agents endpoint
  'GET /agents': async (event) => {
    // Return a list of available agents with pagination
    return createResponse(200, {
      agents: [
        {
          agent_id: 'coo',
          id: 'coo',
          name: 'COO',
          description: 'Your personal AI assistant',
          system_prompt: 'You are a helpful AI assistant.',
          avatar: '🤖',
          avatar_color: '#6B7280',
          configured_mcps: [],
          agentpress_tools: {},
          is_default: true,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          agent_id: 'ai-co-founder',
          id: 'ai-co-founder',
          name: 'AI Co-Founder',
          description: 'Your AI co-founder who gets things done',
          system_prompt: 'You are a helpful AI co-founder assistant.',
          avatar: '🚀',
          avatar_color: '#3B82F6',
          configured_mcps: [],
          agentpress_tools: {},
          is_default: false,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ],
      pagination: {
        page: 1,
        pages: 1,
        per_page: 50,
        total: 2
      }
    });
  },

  'GET /agents/:agentId': async (event) => {
    // Extract agent ID from path
    const pathParts = event.path.split('/');
    const agentId = pathParts[pathParts.length - 1];

    // Return specific agent details
    const agents = {
      'coo': {
        agent_id: 'coo',
        name: 'COO',
        description: 'Your personal AI assistant',
        status: 'active',
        capabilities: ['general_assistance', 'task_management', 'communication'],
        model: 'gpt-4',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      'suna': {
        agent_id: 'suna',
        name: 'Suna',
        description: 'AI Co-Founder assistant',
        status: 'active',
        capabilities: ['business_planning', 'market_research', 'strategy'],
        model: 'gpt-4',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      'ai-co-founder': {
        agent_id: 'ai-co-founder',
        name: 'AI Co-Founder',
        description: 'Your AI co-founder who gets things done',
        status: 'active',
        capabilities: ['business_planning', 'market_research', 'strategy', 'execution'],
        model: 'gpt-4',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };

    const agent = agents[agentId];
    if (!agent) {
      return createResponse(404, {
        error: 'Agent not found',
        message: `Agent with ID '${agentId}' does not exist`,
        available_agents: Object.keys(agents)
      });
    }

    return createResponse(200, agent);
  },

  // Agent builder chat history
  'GET /agents/:agentId/builder-chat-history': async (event) => {
    const pathParts = event.path.split('/');
    const agentIdIndex = pathParts.findIndex(part => part === 'agents') + 1;
    const agentId = pathParts[agentIdIndex];

    return createResponse(200, {
      agent_id: agentId,
      chat_history: [
        {
          id: generateUUID(),
          role: 'user',
          content: 'Help me configure this agent for customer support',
          timestamp: new Date().toISOString()
        },
        {
          id: generateUUID(),
          role: 'assistant',
          content: 'I can help you set up a customer support agent. What specific features would you like to include?',
          timestamp: new Date().toISOString()
        }
      ],
      total_messages: 2
    });
  },

  'POST /agents': async (event) => {
    // Create a new agent
    let agentData = {};

    if (event.body) {
      try {
        agentData = JSON.parse(event.body);
      } catch (e) {
        console.error('Error parsing agent data:', e);
      }
    }

    const agentId = generateUUID();

    console.log('Creating agent:', agentData);

    return createResponse(200, {
      agent_id: agentId,
      id: agentId, // Keep both for compatibility
      name: agentData.name || 'New Agent',
      description: agentData.description || 'A custom AI agent',
      system_prompt: agentData.system_prompt || 'You are a helpful assistant.',
      avatar: agentData.avatar || '🤖',
      avatar_color: agentData.avatar_color || '#3B82F6',
      configured_mcps: agentData.configured_mcps || [],
      agentpress_tools: agentData.agentpress_tools || {},
      is_default: agentData.is_default || false,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
  },

  'GET /workflows/categories': async (event) => {
    // Return workflow categories and statistics
    return createResponse(200, {
      categories: [
        {
          id: 'customer_service',
          name: 'Customer Service',
          description: 'Automate customer support and communication',
          icon: '🎧',
          workflow_count: 12,
          avg_time_saved: '2.5 hours/day'
        },
        {
          id: 'sales_marketing',
          name: 'Sales & Marketing',
          description: 'Lead generation, qualification, and nurturing',
          icon: '🎯',
          workflow_count: 18,
          avg_time_saved: '3 hours/day'
        },
        {
          id: 'development',
          name: 'Development',
          description: 'Code review, testing, and deployment automation',
          icon: '💻',
          workflow_count: 15,
          avg_time_saved: '1.5 hours/day'
        },
        {
          id: 'content_marketing',
          name: 'Content & Marketing',
          description: 'Content creation, publishing, and promotion',
          icon: '📝',
          workflow_count: 10,
          avg_time_saved: '4 hours/week'
        },
        {
          id: 'finance_operations',
          name: 'Finance & Operations',
          description: 'Expense tracking, invoicing, and reporting',
          icon: '💰',
          workflow_count: 8,
          avg_time_saved: '2 hours/week'
        },
        {
          id: 'productivity',
          name: 'Productivity',
          description: 'Task management, scheduling, and organization',
          icon: '📅',
          workflow_count: 14,
          avg_time_saved: '1 hour/day'
        }
      ],
      total_workflows: 77,
      total_time_saved: '15+ hours/week'
    });
  },

  'GET /workflows/templates': async (event) => {
    // Return popular workflow templates
    return createResponse(200, {
      templates: [
        {
          id: 'template_onboarding',
          name: 'Employee Onboarding',
          description: 'Automate new employee setup and training',
          category: 'HR',
          popularity: 96,
          setup_time: '10 minutes',
          steps: 8,
          integrations: ['Slack', 'Google Workspace', 'BambooHR'],
          preview_steps: [
            'Send welcome email',
            'Create accounts',
            'Assign training',
            'Schedule meetings'
          ]
        },
        {
          id: 'template_bug_triage',
          name: 'Bug Report Triage',
          description: 'Automatically categorize and assign bug reports',
          category: 'Development',
          popularity: 92,
          setup_time: '5 minutes',
          steps: 6,
          integrations: ['GitHub', 'Jira', 'Slack'],
          preview_steps: [
            'Analyze bug report',
            'Categorize severity',
            'Assign to team',
            'Create ticket'
          ]
        },
        {
          id: 'template_invoice_processing',
          name: 'Invoice Processing',
          description: 'Extract data from invoices and update accounting',
          category: 'Finance',
          popularity: 88,
          setup_time: '8 minutes',
          steps: 7,
          integrations: ['QuickBooks', 'Gmail', 'Google Drive'],
          preview_steps: [
            'Extract invoice data',
            'Validate information',
            'Route for approval',
            'Update records'
          ]
        }
      ]
    });
  },

  // Templates marketplace
  'GET /templates/marketplace': async (event) => {
    const limit = event.queryStringParameters?.limit || 12;

    return createResponse(200, {
      templates: [
        {
          id: 'marketplace_1',
          name: 'E-commerce Business Plan',
          description: 'Complete business plan template for e-commerce startups',
          category: 'business',
          price: 29.99,
          rating: 4.8,
          downloads: 1250,
          author: 'Business Experts',
          tags: ['ecommerce', 'startup', 'business-plan'],
          created_at: new Date().toISOString()
        },
        {
          id: 'marketplace_2',
          name: 'SaaS Marketing Strategy',
          description: 'Marketing strategy template for SaaS companies',
          category: 'marketing',
          price: 19.99,
          rating: 4.6,
          downloads: 890,
          author: 'Marketing Pros',
          tags: ['saas', 'marketing', 'strategy'],
          created_at: new Date().toISOString()
        },
        {
          id: 'marketplace_3',
          name: 'Financial Projection Model',
          description: 'Comprehensive financial modeling template',
          category: 'finance',
          price: 39.99,
          rating: 4.9,
          downloads: 567,
          author: 'Finance Experts',
          tags: ['finance', 'projections', 'modeling'],
          created_at: new Date().toISOString()
        }
      ].slice(0, parseInt(limit)),
      total: 3,
      page: 1,
      limit: parseInt(limit)
    });
  },

  // User's templates
  'GET /templates/my': async (event) => {
    const userId = getUserIdFromToken(event.headers.authorization);

    return createResponse(200, {
      templates: [
        {
          id: 'user_template_1',
          name: 'My Custom Business Plan',
          description: 'Customized business plan template',
          category: 'business',
          status: 'draft',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: userId
        }
      ],
      total: 1
    });
  },

  // Pipedream profiles
  'GET /pipedream/profiles': async (event) => {
    return createResponse(200, {
      profiles: [
        {
          id: 'profile_1',
          name: 'Business Automation',
          description: 'Automated business workflows',
          workflows_count: 15,
          created_at: new Date().toISOString()
        }
      ]
    });
  },

  // Projects endpoint
  'GET /projects': async (event) => {
    const userId = getUserIdFromToken(event.headers.authorization);
    if (!userId) {
      return createResponse(401, { error: 'Unauthorized' });
    }

    try {
      if (!supabase) {
        throw new Error('Supabase not configured');
      }

      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return createResponse(200, data || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
      // Return mock data if database fails - simulate projects created by agent initiation
      return createResponse(200, [
        {
          id: 'project_1753094000000_sample1',
          name: 'Market Research Dashboard',
          description: 'Comprehensive market research dashboard analyzing industry trends, customer segments, and competitive landscape',
          user_id: userId,
          status: 'active',
          created_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          updated_at: new Date().toISOString(),
          thread_id: 'thread_1753094000000_sample1',
          agent_id: 'agent_1753094000000_sample1'
        },
        {
          id: 'project_1753094100000_sample2',
          name: 'Business Plan Generator',
          description: 'AI-powered business plan creation tool with financial projections',
          user_id: userId,
          status: 'active',
          created_at: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
          updated_at: new Date().toISOString(),
          thread_id: 'thread_1753094100000_sample2',
          agent_id: 'agent_1753094100000_sample2'
        }
      ]);
    }
  },

  // Individual project endpoint
  'GET /project/:projectId': async (event) => {
    // Extract project ID from path
    const pathParts = event.path.split('/');
    const projectId = pathParts[pathParts.length - 1] || 'default-project';

    console.log('Getting project:', projectId);

    // Try to get user ID but don't fail if auth is missing
    const userId = getUserIdFromToken(event.headers.authorization) || 'anonymous-user';

    try {
      if (!supabase) {
        // If Supabase is not configured, return mock data for development
        return createResponse(200, {
          project_id: projectId,
          id: projectId,
          name: 'AI Co-Founder Project',
          description: 'Project created from agent initiation',
          account_id: userId,
          user_id: userId,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          sandbox: {
            id: '',
            pass: '',
            vnc_preview: '',
            sandbox_url: ''
          }
        });
      }

      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('project_id', projectId)
        .single();

      if (error) {
        // Check if it's a "no rows returned" error (project not found)
        if (error.code === 'PGRST116' || error.message.includes('no rows returned')) {
          return createResponse(404, {
            error: 'Project not found',
            message: `Project with ID ${projectId} does not exist or you don't have access to it.`
          });
        }

        // For other database errors, log and return 500
        console.error('Database error fetching project:', error);
        return createResponse(500, {
          error: 'Internal server error',
          message: 'Failed to fetch project data'
        });
      }

      return createResponse(200, data);
    } catch (error) {
      console.error('Unexpected error fetching project:', error);
      return createResponse(500, {
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the project'
      });
    }
  },

  // Create project endpoint
  'POST /projects': async (event) => {
    // Try to get user ID but don't fail if auth is missing
    const userId = getUserIdFromToken(event.headers.authorization) || 'anonymous-user';

    let projectData = {};
    if (event.body) {
      try {
        projectData = JSON.parse(event.body);
      } catch (e) {
        console.error('Error parsing project data:', e);
        return createResponse(400, { error: 'Invalid JSON in request body' });
      }
    }

    const { name = 'New Project', description = 'A new project' } = projectData;

    try {
      // Create new project with proper UUID
      const projectId = generateUUID();

      const newProject = {
        id: projectId,
        project_id: projectId, // For compatibility
        name: name,
        description: description,
        account_id: userId,
        user_id: userId,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sandbox: {
          id: '',
          pass: '',
          vnc_preview: ''
        }
      };

      console.log('Project created successfully:', projectId);
      return createResponse(200, newProject);
    } catch (error) {
      console.error('Error creating project:', error);
      return createResponse(500, { error: 'Failed to create project' });
    }
  },

  // Threads endpoint
  'GET /threads': async (event) => {
    const userId = event.headers.authorization ? getUserIdFromToken(event.headers.authorization) : 'anonymous-user';
    const projectId = event.queryStringParameters?.project_id;

    try {
      // Try to fetch from database first
      if (supabase) {
        let query = supabase
          .from('threads')
          .select('*')
          .eq('user_id', userId);

        // Filter by project_id if provided
        if (projectId) {
          query = query.eq('project_id', projectId);
        }

        const { data, error } = await query;

        if (!error && data && data.length > 0) {
          // Map database fields to expected format
          const mappedThreads = data.map(thread => ({
            id: thread.thread_id || thread.id,
            thread_id: thread.thread_id || thread.id,
            title: thread.title || 'Untitled Thread',
            project_id: thread.project_id,
            user_id: thread.user_id,
            agent_id: thread.agent_id || 'ai-co-founder',
            created_at: thread.created_at,
            updated_at: thread.updated_at,
            metadata: thread.metadata
          }));

          return createResponse(200, mappedThreads);
        }
      }

      // Fallback to mock data if database query fails or returns no results
      const mockThreads = [
        {
          id: 'thread_1',
          thread_id: 'thread_1',
          title: 'Sample Thread',
          project_id: projectId || 'default-project',
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          agent_id: 'ai-co-founder'
        }
      ];

      return createResponse(200, mockThreads);
    } catch (error) {
      console.error('Error fetching threads:', error);
      // Return empty array on error
      return createResponse(200, []);
    }
  },

  // Create thread endpoint
  'POST /threads': async (event) => {
    // Create a new thread with a unique ID
    const threadId = generateUUID();

    try {
      // Parse request body if present
      let title = 'New Thread';
      let agentId = 'ai-co-founder';
      let projectId = null;

      if (event.body) {
        try {
          const body = JSON.parse(event.body);
          title = body.title || title;
          agentId = body.agent_id || agentId;
          projectId = body.project_id || projectId;
        } catch (e) {
          console.error('Error parsing request body:', e);
        }
      }

      return createResponse(200, {
        id: threadId,
        thread_id: threadId, // For compatibility
        title: title,
        project_id: projectId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        agent_id: agentId
      });
    } catch (error) {
      console.error('Error creating thread:', error);
      return createResponse(500, { error: 'Failed to create thread' });
    }
  },

  // Individual thread endpoint
  'GET /thread': async (event) => {
    // Handle individual thread requests
    const threadId = event.queryStringParameters?.thread_id || 'default-thread';

    return createResponse(200, {
      id: threadId,
      title: 'AI Co-Founder Thread',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      agent_id: 'ai-co-founder',
      messages: []
    });
  },

  // Workflow endpoints
  'GET /pipedream/workflows': async (event) => {
    // Return realistic workflow templates
    return createResponse(200, {
      workflows: [
        {
          id: 'workflow_customer_support',
          name: 'Customer Support Automation',
          description: 'Automatically categorize and route customer inquiries to the right team',
          category: 'Customer Service',
          status: 'active',
          popularity: 94,
          estimated_time_saved: '2 hours/day',
          triggers: ['Email received', 'Slack mention', 'Form submission'],
          actions: ['Categorize inquiry', 'Assign to team', 'Send acknowledgment', 'Create ticket'],
          integrations: ['Gmail', 'Slack', 'Zendesk', 'Linear'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 7).toISOString(),
          icon: '🎧'
        },
        {
          id: 'workflow_lead_qualification',
          name: 'Lead Qualification & Scoring',
          description: 'Score and qualify leads automatically based on behavior and demographics',
          category: 'Sales & Marketing',
          status: 'active',
          popularity: 89,
          estimated_time_saved: '3 hours/day',
          triggers: ['Form submission', 'Website visit', 'Email engagement'],
          actions: ['Score lead', 'Enrich data', 'Assign to sales rep', 'Send follow-up'],
          integrations: ['HubSpot', 'Salesforce', 'Clearbit', 'Slack'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 14).toISOString(),
          icon: '🎯'
        },
        {
          id: 'workflow_content_publishing',
          name: 'Content Publishing Pipeline',
          description: 'Streamline content creation, review, and publishing across platforms',
          category: 'Content & Marketing',
          status: 'active',
          popularity: 82,
          estimated_time_saved: '4 hours/week',
          triggers: ['Content submitted', 'Review completed', 'Schedule reached'],
          actions: ['Review content', 'Optimize SEO', 'Schedule posts', 'Track performance'],
          integrations: ['Notion', 'WordPress', 'Twitter', 'LinkedIn', 'Google Analytics'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 21).toISOString(),
          icon: '📝'
        },
        {
          id: 'workflow_code_review',
          name: 'Automated Code Review',
          description: 'Automatically review pull requests and enforce coding standards',
          category: 'Development',
          status: 'active',
          popularity: 91,
          estimated_time_saved: '1 hour/day',
          triggers: ['PR opened', 'Code pushed', 'Review requested'],
          actions: ['Run tests', 'Check standards', 'Security scan', 'Assign reviewers'],
          integrations: ['GitHub', 'GitLab', 'Slack', 'Jira'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 28).toISOString(),
          icon: '🔍'
        },
        {
          id: 'workflow_expense_tracking',
          name: 'Expense Tracking & Approval',
          description: 'Automate expense report processing and approval workflows',
          category: 'Finance & Operations',
          status: 'active',
          popularity: 76,
          estimated_time_saved: '2 hours/week',
          triggers: ['Receipt uploaded', 'Expense submitted', 'Approval needed'],
          actions: ['Extract data', 'Categorize expense', 'Route for approval', 'Update records'],
          integrations: ['Expensify', 'QuickBooks', 'Slack', 'Google Sheets'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 35).toISOString(),
          icon: '💰'
        },
        {
          id: 'workflow_social_monitoring',
          name: 'Social Media Monitoring',
          description: 'Monitor brand mentions and respond to social media interactions',
          category: 'Social Media',
          status: 'active',
          popularity: 85,
          estimated_time_saved: '1.5 hours/day',
          triggers: ['Brand mention', 'Hashtag used', 'Comment received'],
          actions: ['Analyze sentiment', 'Categorize mention', 'Alert team', 'Draft response'],
          integrations: ['Twitter', 'Instagram', 'Facebook', 'Slack', 'Hootsuite'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 42).toISOString(),
          icon: '📱'
        },
        {
          id: 'workflow_inventory_management',
          name: 'Inventory Management',
          description: 'Automatically track inventory levels and reorder when stock is low',
          category: 'E-commerce',
          status: 'active',
          popularity: 78,
          estimated_time_saved: '3 hours/week',
          triggers: ['Stock level low', 'Sale completed', 'Shipment received'],
          actions: ['Update inventory', 'Create purchase order', 'Notify suppliers', 'Update listings'],
          integrations: ['Shopify', 'WooCommerce', 'QuickBooks', 'Slack'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 49).toISOString(),
          icon: '📦'
        },
        {
          id: 'workflow_meeting_scheduler',
          name: 'Smart Meeting Scheduler',
          description: 'Intelligently schedule meetings based on availability and preferences',
          category: 'Productivity',
          status: 'active',
          popularity: 88,
          estimated_time_saved: '30 minutes/day',
          triggers: ['Meeting request', 'Calendar update', 'Availability change'],
          actions: ['Check availability', 'Find optimal time', 'Send invites', 'Prepare agenda'],
          integrations: ['Google Calendar', 'Outlook', 'Zoom', 'Slack'],
          template: true,
          created_at: new Date(Date.now() - 86400000 * 56).toISOString(),
          icon: '📅'
        }
      ]
    });
  },

  'POST /pipedream/workflows': async (event) => {
    // Create a new workflow from template or custom
    let workflowData = {};

    if (event.body) {
      try {
        workflowData = JSON.parse(event.body);
      } catch (e) {
        console.error('Error parsing workflow data:', e);
        return createResponse(400, { error: 'Invalid JSON in request body' });
      }
    }

    const workflowId = 'workflow_custom_' + Date.now();
    const categories = ['Custom', 'Automation', 'Integration', 'Productivity', 'Development'];
    const icons = ['⚡', '🔧', '🎯', '📊', '🚀', '💡', '🔄', '📈'];

    const newWorkflow = {
      id: workflowId,
      name: workflowData.name || 'Custom Workflow',
      description: workflowData.description || 'A custom automation workflow',
      category: workflowData.category || 'Custom',
      status: 'active',
      popularity: 0, // New workflows start with 0 popularity
      estimated_time_saved: workflowData.estimated_time_saved || 'TBD',
      triggers: workflowData.triggers || ['Manual trigger'],
      actions: workflowData.actions || ['Custom action'],
      integrations: workflowData.integrations || [],
      template: false, // Custom workflows are not templates
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      icon: workflowData.icon || icons[Math.floor(Math.random() * icons.length)],
      created_by: 'user', // In real implementation, this would be the actual user ID
      steps: workflowData.steps || [
        {
          id: 'step_1',
          name: 'Trigger',
          type: 'trigger',
          config: workflowData.trigger_config || {},
          order: 1
        },
        {
          id: 'step_2',
          name: 'Action',
          type: 'action',
          config: workflowData.action_config || {},
          order: 2
        }
      ]
    };

    return createResponse(201, {
      workflow: newWorkflow,
      message: 'Workflow created successfully',
      next_steps: [
        'Configure your triggers',
        'Set up actions',
        'Test the workflow',
        'Activate automation'
      ]
    });
  },

  // Dynamic thread endpoint with ID in path
  'GET /thread/:threadId': async (event) => {
    // Extract thread ID from path
    const pathParts = event.path.split('/');
    const threadId = pathParts[pathParts.length - 1] || 'default-thread';

    console.log('Getting thread:', threadId);

    try {
      // Try to fetch from database first
      if (supabase) {
        const { data, error } = await supabase
          .from('threads')
          .select('*')
          .eq('thread_id', threadId)
          .single();

        if (!error && data) {
          // Map database fields to expected format
          return createResponse(200, {
            id: data.thread_id || data.id,
            thread_id: data.thread_id || data.id,
            title: data.title || 'AI Co-Founder Thread',
            project_id: data.project_id, // Include project_id!
            user_id: data.user_id,
            agent_id: data.agent_id || 'ai-co-founder',
            created_at: data.created_at,
            updated_at: data.updated_at,
            metadata: data.metadata,
            status: 'active'
          });
        }

        // If thread not found in database, return 404
        if (error && error.code === 'PGRST116') {
          return createResponse(404, {
            error: 'Thread not found',
            message: `Thread with ID ${threadId} does not exist or you don't have access to it.`
          });
        }
      }

      // Fallback to mock data if database is not available
      return createResponse(200, {
        id: threadId,
        thread_id: threadId,
        title: 'AI Co-Founder Thread',
        project_id: 'mock-project-id', // Include a mock project_id
        user_id: 'mock-user-id',
        agent_id: 'ai-co-founder',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metadata: {},
        status: 'active'
      });
    } catch (error) {
      console.error('Error fetching thread:', error);
      return createResponse(500, {
        error: 'Internal server error',
        message: 'Failed to fetch thread data'
      });
    }
  },

  // Thread messages endpoints
  'GET /thread/:threadId/messages': async (event) => {
    // Extract thread ID from path
    const pathParts = event.path.split('/');
    const threadIdIndex = pathParts.findIndex(part => part === 'thread') + 1;
    const threadId = pathParts[threadIdIndex] || 'default-thread';

    console.log('Getting messages for thread:', threadId);

    // Try to get user ID but don't fail if auth is missing
    const userId = getUserIdFromToken(event.headers.authorization) || 'anonymous-user';

    try {
      // Return mock messages for now
      return createResponse(200, [
        {
          id: `msg_${Date.now()}_1`,
          thread_id: threadId,
          role: 'user',
          content: 'Hello, I need help with my project.',
          timestamp: new Date(Date.now() - 60000).toISOString(),
          user_id: userId
        },
        {
          id: `msg_${Date.now()}_2`,
          thread_id: threadId,
          role: 'assistant',
          content: 'Hello! I\'d be happy to help you with your project. What specific assistance do you need?',
          timestamp: new Date().toISOString(),
          agent_id: 'ai-co-founder'
        }
      ]);
    } catch (error) {
      console.error('Error fetching messages:', error);
      return createResponse(200, []);
    }
  },

  'POST /thread/:threadId/messages': async (event) => {
    // Extract thread ID from path
    const pathParts = event.path.split('/');
    const threadIdIndex = pathParts.findIndex(part => part === 'thread') + 1;
    const threadId = pathParts[threadIdIndex] || 'default-thread';

    console.log('Adding message to thread:', threadId);

    // Try to get user ID but don't fail if auth is missing
    const userId = getUserIdFromToken(event.headers.authorization) || 'anonymous-user';

    let messageContent = '';
    if (event.body) {
      try {
        const data = JSON.parse(event.body);
        messageContent = data.content || '';
      } catch (e) {
        console.error('Error parsing message data:', e);
        return createResponse(400, { error: 'Invalid JSON in request body' });
      }
    }

    if (!messageContent.trim()) {
      return createResponse(400, { error: 'Message content is required' });
    }

    try {
      // Create new message
      const newMessage = {
        id: generateUUID(),
        thread_id: threadId,
        role: 'user',
        content: messageContent,
        timestamp: new Date().toISOString(),
        user_id: userId
      };

      console.log('Message added successfully:', newMessage.id);
      return createResponse(200, newMessage);
    } catch (error) {
      console.error('Error adding message:', error);
      return createResponse(500, { error: 'Failed to add message' });
    }
  },

  // Thread agent-runs endpoint
  'GET /thread/:threadId/agent-runs': async (event) => {
    // Extract thread ID from path
    const pathParts = event.path.split('/');
    const threadIdIndex = pathParts.findIndex(part => part === 'thread') + 1;
    const threadId = pathParts[threadIdIndex] || 'default-thread';

    console.log('Getting agent-runs for thread:', threadId);

    try {
      // Try to fetch from database first
      if (supabase) {
        const { data, error } = await supabase
          .from('agent_runs')
          .select('*')
          .eq('thread_id', threadId)
          .order('created_at', { ascending: true });

        if (!error && data && data.length > 0) {
          // Map database fields to expected format
          const agentRuns = data.map(run => ({
            id: run.agent_run_id || run.id,
            thread_id: run.thread_id,
            agent_id: run.agent_id,
            status: run.status,
            created_at: run.created_at,
            updated_at: run.updated_at,
            metadata: run.metadata,
            tool_calls: run.tool_calls || []
          }));

          return createResponse(200, agentRuns);
        }
      }

      // Fallback to mock data if database is not available or no agent runs found
      return createResponse(200, [
        {
          id: `agent_run_${Date.now()}_1`,
          thread_id: threadId,
          agent_id: 'ai-co-founder',
          status: 'completed',
          created_at: new Date(Date.now() - 120000).toISOString(),
          updated_at: new Date(Date.now() - 60000).toISOString(),
          metadata: {},
          tool_calls: [
            {
              id: `tool_call_${Date.now()}_1`,
              type: 'function',
              function: {
                name: 'web_search',
                arguments: '{"query": "business plan template"}',
                result: 'Found several business plan templates and resources.'
              },
              status: 'completed'
            }
          ]
        }
      ]);
    } catch (error) {
      console.error('Error fetching agent-runs:', error);
      return createResponse(500, {
        error: 'Internal server error',
        message: 'Failed to fetch agent runs'
      });
    }
  },

  // Agent endpoints
  'POST /agent/initiate': async (event) => {
    // Agent initiate endpoint - create agent, project, and thread
    console.log('Agent initiate called:', {
      method: event.httpMethod,
      headers: event.headers,
      body: event.body ? 'present' : 'empty'
    });

    // Parse the form data to get the prompt
    let prompt = '';
    let agentId = 'ai-co-founder';

    console.log('Request details:', {
      method: event.httpMethod,
      contentType: event.headers['content-type'],
      bodyPresent: !!event.body,
      bodyLength: event.body ? event.body.length : 0,
      bodyPreview: event.body ? event.body.substring(0, 200) : 'none'
    });

    if (event.body) {
      try {
        // Handle multipart form data
        if (event.headers['content-type']?.includes('multipart/form-data')) {
          // Decode base64 body if needed
          let bodyStr = event.body;
          if (event.isBase64Encoded) {
            bodyStr = Buffer.from(event.body, 'base64').toString('utf-8');
          }

          console.log('Decoded body preview:', bodyStr.substring(0, 300));

          // Pattern 1: Standard multipart
          let promptMatch = bodyStr.match(/name="prompt"[\s\S]*?\r?\n\r?\n([\s\S]*?)(?:\r?\n--|\r?\n$)/);
          if (promptMatch) {
            prompt = promptMatch[1].trim();
          }

          // Pattern 2: With Content-Disposition
          if (!prompt) {
            promptMatch = bodyStr.match(/Content-Disposition: form-data; name="prompt"[\s\S]*?\r?\n\r?\n([\s\S]*?)(?:\r?\n--|\r?\n$)/);
            if (promptMatch) {
              prompt = promptMatch[1].trim();
            }
          }

          // Pattern 3: Simple boundary search
          if (!prompt) {
            const lines = bodyStr.split('\n');
            let foundPromptField = false;
            for (let i = 0; i < lines.length; i++) {
              if (lines[i].includes('name="prompt"')) {
                foundPromptField = true;
                continue;
              }
              if (foundPromptField && lines[i].trim() === '') {
                // Next non-empty line should be the prompt
                for (let j = i + 1; j < lines.length; j++) {
                  if (lines[j].trim() && !lines[j].startsWith('--')) {
                    prompt = lines[j].trim();
                    break;
                  }
                }
                break;
              }
            }
          }

          console.log('Extracted prompt:', prompt ? prompt.substring(0, 100) + '...' : 'none');
        } else if (event.headers['content-type']?.includes('application/json')) {
          const data = JSON.parse(event.body);
          prompt = data.prompt || '';
          agentId = data.agent_id || 'ai-co-founder';
        }
      } catch (e) {
        console.error('Error parsing request body:', e);
      }
    }

    // Generate unique IDs
    const threadId = generateUUID();
    const projectId = generateUUID();
    const agentInstanceId = generateUUID();

    // Create project
    const project = {
      id: projectId,
      name: prompt ? `Project: ${prompt.substring(0, 50)}...` : 'New Project',
      description: prompt || 'Project created from message submission',
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      thread_id: threadId,
      agent_id: agentInstanceId
    };

    // Create agent instance
    const agent = {
      id: agentInstanceId,
      agent_id: agentId,
      name: 'AI Co-Founder',
      description: 'Your AI co-founder who gets things done',
      status: 'active',
      project_id: projectId,
      thread_id: threadId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Create thread
    const thread = {
      id: threadId,
      project_id: projectId,
      agent_id: agentInstanceId,
      initial_prompt: prompt,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      message_count: prompt ? 1 : 0
    };

    console.log('Created entities:', {
      project: project.id,
      agent: agent.id,
      thread: thread.id,
      prompt: prompt ? prompt.substring(0, 100) + '...' : 'none'
    });

    return createResponse(200, {
      status: 'success',
      message: 'Agent, project, and thread created successfully',
      thread_id: threadId,
      project_id: projectId,
      agent_id: agentInstanceId,
      agent_type: agentId,
      prompt: prompt,
      entities_created: {
        project,
        agent,
        thread
      },
      timestamp: new Date().toISOString(),
      service: 'netlify-functions'
    });
  },

  // Agent run endpoints
  'POST /thread/:threadId/agent/start': async (event) => {
    // Extract thread ID from path
    const pathParts = event.path.split('/');
    const threadIdIndex = pathParts.findIndex(part => part === 'thread') + 1;
    const threadId = pathParts[threadIdIndex] || 'default-thread';

    console.log('Starting agent for thread:', threadId);

    // Try to get user ID but don't fail if auth is missing
    const userId = getUserIdFromToken(event.headers.authorization) || 'anonymous-user';

    let options = {};
    if (event.body) {
      try {
        options = JSON.parse(event.body);
      } catch (e) {
        console.error('Error parsing agent options:', e);
      }
    }

    try {
      // Create new agent run
      const agentRunId = generateUUID();

      const agentRun = {
        id: agentRunId,
        thread_id: threadId,
        agent_id: options.agent_id || 'ai-co-founder',
        status: 'running',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        model_name: options.model_name || 'gpt-4',
        enable_thinking: options.enable_thinking || false
      };

      console.log('Agent run started:', agentRunId);
      return createResponse(200, agentRun);
    } catch (error) {
      console.error('Error starting agent:', error);
      return createResponse(500, { error: 'Failed to start agent' });
    }
  },

  'POST /agent-run/:agentRunId/stop': async (event) => {
    // Extract agent run ID from path
    const pathParts = event.path.split('/');
    const agentRunIdIndex = pathParts.findIndex(part => part === 'agent-run') + 1;
    const agentRunId = pathParts[agentRunIdIndex] || 'default-run';

    console.log('Stopping agent run:', agentRunId);

    const userId = getUserIdFromToken(event.headers.authorization);
    if (!userId) {
      return createResponse(401, { error: 'Unauthorized' });
    }

    try {
      console.log('Agent run stopped:', agentRunId);
      return createResponse(200, {
        id: agentRunId,
        status: 'stopped',
        stopped_at: new Date().toISOString(),
        message: 'Agent run stopped successfully'
      });
    } catch (error) {
      console.error('Error stopping agent run:', error);
      return createResponse(500, { error: 'Failed to stop agent run' });
    }
  },

  'GET /agent-run/:agentRunId': async (event) => {
    // Extract agent run ID from path
    const pathParts = event.path.split('/');
    const agentRunIdIndex = pathParts.findIndex(part => part === 'agent-run') + 1;
    const agentRunId = pathParts[agentRunIdIndex] || 'default-run';

    console.log('Getting agent run:', agentRunId);

    const userId = getUserIdFromToken(event.headers.authorization);
    if (!userId) {
      return createResponse(401, { error: 'Unauthorized' });
    }

    try {
      return createResponse(200, {
        id: agentRunId,
        thread_id: generateUUID(),
        agent_id: 'ai-co-founder',
        status: 'completed',
        created_at: new Date(Date.now() - 60000).toISOString(),
        updated_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        model_name: 'gpt-4',
        enable_thinking: false,
        result: {
          success: true,
          message: 'Agent run completed successfully'
        }
      });
    } catch (error) {
      console.error('Error getting agent run:', error);
      return createResponse(500, { error: 'Failed to get agent run' });
    }
  },

  // Integrations endpoints
  'POST /integrations/install': async (event) => {
    // Handle integration installation
    let integrationData = {};

    if (event.body) {
      try {
        integrationData = JSON.parse(event.body);
      } catch (e) {
        console.error('Error parsing integration data:', e);
        return createResponse(400, { error: 'Invalid JSON in request body' });
      }
    }

    const { provider, agent_id } = integrationData;
    console.log('Installing integration:', { provider, agent_id });

    if (!provider) {
      return createResponse(400, { error: 'Provider is required' });
    }

    // Generate OAuth URLs for different providers
    const oauthUrls = {
      slack: 'https://slack.com/oauth/v2/authorize?client_id=your_slack_client_id&scope=chat:write,channels:read&redirect_uri=' + encodeURIComponent('https://aicofounder.site/api/integrations/slack/callback'),
      discord: 'https://discord.com/api/oauth2/authorize?client_id=your_discord_client_id&redirect_uri=' + encodeURIComponent('https://aicofounder.site/api/integrations/discord/callback') + '&response_type=code&scope=bot',
      github: 'https://github.com/login/oauth/authorize?client_id=your_github_client_id&redirect_uri=' + encodeURIComponent('https://aicofounder.site/api/integrations/github/callback') + '&scope=repo',
      notion: 'https://api.notion.com/v1/oauth/authorize?client_id=your_notion_client_id&redirect_uri=' + encodeURIComponent('https://aicofounder.site/api/integrations/notion/callback') + '&response_type=code',
      google: 'https://accounts.google.com/o/oauth2/v2/auth?client_id=your_google_client_id&redirect_uri=' + encodeURIComponent('https://aicofounder.site/api/integrations/google/callback') + '&response_type=code&scope=https://www.googleapis.com/auth/spreadsheets',
      telegram: 'https://t.me/your_bot_name?start=auth_' + Date.now()
    };

    const installUrl = oauthUrls[provider];

    if (!installUrl) {
      return createResponse(400, {
        error: 'Unsupported provider',
        message: `Integration for ${provider} is not yet available. Coming soon!`
      });
    }

    return createResponse(200, {
      status: 'success',
      message: 'Integration authorization URL generated',
      install_url: installUrl,
      provider: provider,
      agent_id: agent_id
    });
  },

  'GET /integrations': async (event) => {
    // List available integrations with real data and proper logos
    return createResponse(200, {
      integrations: [
        {
          id: 'slack',
          name: 'Slack',
          provider: 'slack',
          status: 'available',
          description: 'Connect your AI agent to Slack workspaces for real-time communication',
          category: 'Communication',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/slack.svg',
          color: '#4A154B',
          features: ['Direct messages', 'Channel posting', 'Slash commands', 'Interactive buttons'],
          setup_time: '2 minutes',
          popularity: 95
        },
        {
          id: 'discord',
          name: 'Discord',
          provider: 'discord',
          status: 'available',
          description: 'Integrate with Discord servers for gaming and community interactions',
          category: 'Communication',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/discord.svg',
          color: '#5865F2',
          features: ['Server messages', 'Direct messages', 'Voice channels', 'Bot commands'],
          setup_time: '3 minutes',
          popularity: 88
        },
        {
          id: 'teams',
          name: 'Microsoft Teams',
          provider: 'teams',
          status: 'available',
          description: 'Connect to Microsoft Teams for enterprise communication',
          category: 'Communication',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/microsoftteams.svg',
          color: '#6264A7',
          features: ['Team chats', 'Meeting integration', 'File sharing', 'Adaptive cards'],
          setup_time: '5 minutes',
          popularity: 82
        },
        {
          id: 'github',
          name: 'GitHub',
          provider: 'github',
          status: 'available',
          description: 'Automate GitHub workflows, issues, and pull requests',
          category: 'Development',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/github.svg',
          color: '#181717',
          features: ['Issue management', 'PR automation', 'Code review', 'Repository insights'],
          setup_time: '3 minutes',
          popularity: 92
        },
        {
          id: 'notion',
          name: 'Notion',
          provider: 'notion',
          status: 'available',
          description: 'Sync data with Notion databases and pages',
          category: 'Productivity',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/notion.svg',
          color: '#000000',
          features: ['Database sync', 'Page creation', 'Content updates', 'Template automation'],
          setup_time: '4 minutes',
          popularity: 78
        },
        {
          id: 'google-sheets',
          name: 'Google Sheets',
          provider: 'google',
          status: 'available',
          description: 'Read and write data to Google Sheets spreadsheets',
          category: 'Productivity',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/googlesheets.svg',
          color: '#34A853',
          features: ['Data import/export', 'Real-time updates', 'Formula automation', 'Chart generation'],
          setup_time: '2 minutes',
          popularity: 85
        },
        {
          id: 'airtable',
          name: 'Airtable',
          provider: 'airtable',
          status: 'available',
          description: 'Manage Airtable bases and records programmatically',
          category: 'Productivity',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/airtable.svg',
          color: '#18BFFF',
          features: ['Record management', 'Base automation', 'Field updates', 'View filtering'],
          setup_time: '3 minutes',
          popularity: 71
        },
        {
          id: 'zapier',
          name: 'Zapier',
          provider: 'zapier',
          status: 'available',
          description: 'Trigger Zapier workflows and automations',
          category: 'Automation',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/zapier.svg',
          color: '#FF4A00',
          features: ['Workflow triggers', 'Multi-app automation', 'Data transformation', 'Conditional logic'],
          setup_time: '2 minutes',
          popularity: 89
        },
        {
          id: 'make',
          name: 'Make (Integromat)',
          provider: 'make',
          status: 'available',
          description: 'Connect to Make scenarios for advanced automation',
          category: 'Automation',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/make.svg',
          color: '#6366F1',
          features: ['Scenario execution', 'Data routing', 'Error handling', 'Visual workflows'],
          setup_time: '4 minutes',
          popularity: 76
        },
        {
          id: 'linear',
          name: 'Linear',
          provider: 'linear',
          status: 'available',
          description: 'Manage Linear issues and project tracking',
          category: 'Development',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/linear.svg',
          color: '#5E6AD2',
          features: ['Issue creation', 'Status updates', 'Project sync', 'Team notifications'],
          setup_time: '3 minutes',
          popularity: 68
        },
        {
          id: 'jira',
          name: 'Jira',
          provider: 'atlassian',
          status: 'coming_soon',
          description: 'Integrate with Atlassian Jira for issue tracking',
          category: 'Development',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/jira.svg',
          color: '#0052CC',
          features: ['Issue management', 'Sprint planning', 'Workflow automation', 'Reporting'],
          setup_time: '5 minutes',
          popularity: 84
        },
        {
          id: 'salesforce',
          name: 'Salesforce',
          provider: 'salesforce',
          status: 'coming_soon',
          description: 'Connect to Salesforce CRM for customer data management',
          category: 'CRM',
          logo: 'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/salesforce.svg',
          color: '#00A1E0',
          features: ['Lead management', 'Opportunity tracking', 'Contact sync', 'Custom objects'],
          setup_time: '7 minutes',
          popularity: 91
        }
      ]
    });
  },

  'GET /integrations/status': async (event) => {
    // Return integration availability status
    return createResponse(200, {
      status: {
        available: [
          'slack', 'discord', 'teams', 'github', 'notion',
          'google-sheets', 'airtable', 'zapier', 'make', 'linear'
        ],
        coming_soon: [
          'jira', 'salesforce', 'hubspot', 'zendesk', 'intercom',
          'stripe', 'shopify', 'mailchimp', 'calendly', 'figma'
        ],
        beta: [
          'openai-assistants', 'anthropic-claude', 'google-gemini'
        ]
      },
      total_available: 10,
      total_coming_soon: 10,
      total_beta: 3,
      last_updated: new Date().toISOString()
    });
  },

  'GET /integrations/categories': async (event) => {
    // Return integration categories
    return createResponse(200, {
      categories: [
        {
          id: 'communication',
          name: 'Communication',
          description: 'Chat, messaging, and collaboration tools',
          icon: '💬',
          count: 3
        },
        {
          id: 'development',
          name: 'Development',
          description: 'Code repositories, project management, and dev tools',
          icon: '💻',
          count: 4
        },
        {
          id: 'productivity',
          name: 'Productivity',
          description: 'Document management, spreadsheets, and organization',
          icon: '📊',
          count: 3
        },
        {
          id: 'automation',
          name: 'Automation',
          description: 'Workflow automation and integration platforms',
          icon: '⚡',
          count: 2
        },
        {
          id: 'crm',
          name: 'CRM & Sales',
          description: 'Customer relationship management and sales tools',
          icon: '🤝',
          count: 1
        },
        {
          id: 'ecommerce',
          name: 'E-commerce',
          description: 'Online stores, payment processing, and inventory',
          icon: '🛒',
          count: 0
        }
      ]
    });
  },

  // Admin endpoints
  'GET /admin/verify': async (event) => {
    // Simple admin verification endpoint
    return createResponse(200, {
      status: 'verified',
      timestamp: new Date().toISOString(),
      service: 'netlify-functions'
    });
  },

  // Default 404 handler
  'default': (event) => {
    console.log('Route not found:', {
      method: event.httpMethod,
      path: event.path,
      processedPath: event.processedPath || 'unknown'
    });

    return createResponse(404, {
      error: 'Not Found',
      message: 'API endpoint not found',
      requested: `${event.httpMethod} ${event.path}`,
      available_endpoints: [
        'GET /api/health',
        'GET /api/feature-flags/custom_agents',
        'GET /api/feature-flags/agent_marketplace',
        'GET /api/billing/subscription',
        'POST /api/billing/create-checkout-session',
        'POST /api/billing/payment-success',
        'POST /api/billing/update-payment-method',
        'POST /api/billing/cancel-subscription',
        'GET /api/billing/available-models',
        'GET /api/billing/usage-logs',
        'GET /api/agents',
        'GET /api/agents/:agentId',
        'GET /api/agents/:agentId/builder-chat-history',
        'POST /api/agents',
        'GET /api/projects',
        'POST /api/projects',
        'GET /api/project/:projectId',
        'GET /api/threads',
        'POST /api/threads',
        'GET /api/thread',
        'GET /api/thread/:threadId',
        'GET /api/thread/:threadId/messages',
        'POST /api/thread/:threadId/messages',
        'POST /api/thread/:threadId/agent/start',
        'POST /api/agent-run/:agentRunId/stop',
        'GET /api/agent-run/:agentRunId',
        'POST /api/agent/initiate',
        'GET /api/integrations',
        'GET /api/integrations/status',
        'GET /api/integrations/categories',
        'POST /api/integrations/install',
        'GET /api/pipedream/workflows',
        'POST /api/pipedream/workflows',
        'GET /api/workflows/categories',
        'GET /api/workflows/templates',
        'GET /api/templates/marketplace',
        'GET /api/templates/my',
        'GET /api/pipedream/profiles',
        'GET /api/admin/verify'
      ]
    });
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return createResponse(200, {});
  }

  try {
    // Get the path from query parameters (Netlify redirects use this)
    let apiPath = event.queryStringParameters?.splat || event.path || '';

    console.log('Raw event data:', {
      path: event.path,
      queryStringParameters: event.queryStringParameters,
      httpMethod: event.httpMethod
    });

    // Clean up the path - handle multiple possible formats
    if (apiPath.includes('/.netlify/functions/api')) {
      apiPath = apiPath.replace(/^.*\/\.netlify\/functions\/api/, '');
    } else if (apiPath.includes('/.netlify/functions/')) {
      // Handle direct endpoint calls like /.netlify/functions/feature-flags/custom_agents
      const parts = apiPath.split('/.netlify/functions/');
      if (parts.length > 1) {
        apiPath = '/' + parts[1]; // Convert to /feature-flags/custom_agents
      }
    }

    if (apiPath.startsWith('/api/')) {
      apiPath = apiPath.replace(/^\/api/, '');
    }

    // Ensure path starts with /
    if (!apiPath.startsWith('/')) {
      apiPath = '/' + apiPath;
    }

    // Handle root path
    if (apiPath === '/' || apiPath === '') {
      apiPath = '/health';
    }

    // Create route key
    const routeKey = `${event.httpMethod} ${apiPath}`;

    console.log('Processed request:', {
      originalPath: event.path,
      splatParam: event.queryStringParameters?.splat,
      processedPath: apiPath,
      routeKey: routeKey,
      method: event.httpMethod,
      availableRoutes: Object.keys(routes)
    });

    // Find matching route
    let handler = routes[routeKey];

    // If no handler found, try to find a direct endpoint handler
    // This handles cases where the frontend calls /.netlify/functions/feature-flags/custom_agents
    if (!handler && apiPath.includes('/')) {
      const directPath = apiPath.split('/').pop(); // Get last part of path
      const directRouteKey = `${event.httpMethod} /${directPath}`;
      console.log(`Trying direct endpoint handler: ${directRouteKey}`);
      handler = routes[directRouteKey];
    }

    // Handle dynamic routes
    if (!handler) {
      // Handle agent builder chat history like /agents/agent_123/builder-chat-history
      if (apiPath.includes('/agents/') && apiPath.endsWith('/builder-chat-history')) {
        const agentBuilderRouteKey = `${event.httpMethod} /agents/:agentId/builder-chat-history`;
        console.log(`Trying dynamic agent builder handler: ${agentBuilderRouteKey}`);
        handler = routes[agentBuilderRouteKey];
      }
      // Handle agent endpoints like /agents/suna
      else if (apiPath.startsWith('/agents/') && apiPath.split('/').length === 3) {
        const agentRouteKey = `${event.httpMethod} /agents/:agentId`;
        console.log(`Trying dynamic agent handler: ${agentRouteKey}`);
        handler = routes[agentRouteKey];
      }
      // Handle project endpoints like /project/project_123456789
      else if (apiPath.startsWith('/project/')) {
        const projectRouteKey = `${event.httpMethod} /project/:projectId`;
        console.log(`Trying dynamic project handler: ${projectRouteKey}`);
        handler = routes[projectRouteKey];
      }
      // Handle thread endpoints like /thread/thread_123456789
      else if (apiPath.startsWith('/thread/')) {
        // Check for message endpoints first
        if (apiPath.includes('/messages')) {
          const messageRouteKey = `${event.httpMethod} /thread/:threadId/messages`;
          console.log(`Trying dynamic thread messages handler: ${messageRouteKey}`);
          handler = routes[messageRouteKey];
        }
        // Check for agent-runs endpoints
        else if (apiPath.includes('/agent-runs')) {
          const agentRunsRouteKey = `${event.httpMethod} /thread/:threadId/agent-runs`;
          console.log(`Trying dynamic thread agent-runs handler: ${agentRunsRouteKey}`);
          handler = routes[agentRunsRouteKey];
        }
        // Check for agent start endpoints
        else if (apiPath.includes('/agent/start')) {
          const agentStartRouteKey = `${event.httpMethod} /thread/:threadId/agent/start`;
          console.log(`Trying dynamic thread agent start handler: ${agentStartRouteKey}`);
          handler = routes[agentStartRouteKey];
        }
        // Default thread endpoint
        else {
          const threadRouteKey = `${event.httpMethod} /thread/:threadId`;
          console.log(`Trying dynamic thread handler: ${threadRouteKey}`);
          handler = routes[threadRouteKey];
        }
      }
      // Handle agent-run endpoints like /agent-run/run_123456789
      else if (apiPath.startsWith('/agent-run/')) {
        const agentRunRouteKey = `${event.httpMethod} /agent-run/:agentRunId`;
        console.log(`Trying dynamic agent-run handler: ${agentRunRouteKey}`);
        handler = routes[agentRunRouteKey];
      }
    }

    if (!handler) {
      console.log('No handler found for route:', routeKey);
      console.log('Available routes:', Object.keys(routes));
      return routes['default'](event, context);
    }

    // Execute handler
    const response = await handler(event, context);

    console.log('Response generated:', {
      statusCode: response.statusCode,
      routeKey: routeKey
    });

    return response;

  } catch (error) {
    console.error('API Function error:', error);
    
    return createResponse(500, {
      error: 'Internal Server Error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};
