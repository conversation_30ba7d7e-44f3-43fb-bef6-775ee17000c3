// API Fix Verification Script
// Run this in browser console after deployment to verify all endpoints work

console.log('🔧 Testing API Fix - Post Deployment Verification');

const testApiEndpoints = async () => {
  const baseUrl = window.location.origin;
  console.log(`Testing on: ${baseUrl}`);
  
  const endpoints = [
    {
      name: 'Health Check',
      url: '/api/health',
      expected: { status: 'healthy' }
    },
    {
      name: 'Feature Flags - Custom Agents',
      url: '/api/feature-flags/custom_agents',
      expected: { enabled: true }
    },
    {
      name: 'Billing Subscription',
      url: '/api/billing/subscription',
      expected: { plan: 'free', status: 'active' }
    },
    {
      name: 'Billing Available Models',
      url: '/api/billing/available-models',
      expected: { models: [] }
    },
    {
      name: 'Admin Verify',
      url: '/api/admin/verify',
      expected: { status: 'verified' }
    }
  ];

  const results = [];
  let successCount = 0;

  for (const endpoint of endpoints) {
    try {
      console.log(`\n🧪 Testing: ${endpoint.name}`);
      console.log(`   URL: ${endpoint.url}`);
      
      const response = await fetch(`${baseUrl}${endpoint.url}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ SUCCESS (${response.status}):`, data);
        
        // Verify expected structure
        const hasExpectedKeys = Object.keys(endpoint.expected).every(key => 
          data.hasOwnProperty(key)
        );
        
        if (hasExpectedKeys) {
          console.log(`   ✅ Response structure matches expected format`);
          successCount++;
        } else {
          console.log(`   ⚠️  Response structure differs from expected:`, endpoint.expected);
        }
        
        results.push({
          ...endpoint,
          status: response.status,
          success: true,
          data
        });
      } else {
        const errorText = await response.text();
        console.log(`   ❌ FAILED (${response.status}): ${errorText}`);
        results.push({
          ...endpoint,
          status: response.status,
          success: false,
          error: errorText
        });
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      results.push({
        ...endpoint,
        success: false,
        error: error.message
      });
    }
  }

  // Summary
  console.log('\n📊 API Fix Verification Results:');
  console.log('================================');
  console.log(`✅ Successful: ${successCount}/${endpoints.length}`);
  console.log(`❌ Failed: ${endpoints.length - successCount}/${endpoints.length}`);
  
  if (successCount === endpoints.length) {
    console.log('\n🎉 ALL API ENDPOINTS WORKING! The fix was successful!');
    console.log('✅ No more 404 errors');
    console.log('✅ Netlify functions deployed correctly');
    console.log('✅ Redirects working properly');
  } else {
    console.log('\n⚠️  Some endpoints still need attention:');
    results.filter(r => !r.success).forEach(result => {
      console.log(`- ${result.name}: ${result.error || result.status}`);
    });
  }

  return results;
};

// Auto-run the test
testApiEndpoints().then(results => {
  console.log('\n🎯 API fix verification completed!');
  window.apiFixResults = results;
});

// Export for manual use
window.testApiEndpoints = testApiEndpoints;
