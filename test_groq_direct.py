#!/usr/bin/env python3
"""
Test GROQ API directly to ensure it's working
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append('/Users/<USER>/Documents/GitHub/suna/backend')

from services.llm import make_llm_api_call

async def test_groq_direct():
    """Test GROQ API directly"""
    print("🧪 Testing GROQ API Directly")
    print("=" * 50)
    
    try:
        print("\n1. Testing GROQ Model: llama-3.3-70b-versatile")
        response = await make_llm_api_call(
            messages=[{"role": "user", "content": "Hello! Please respond with exactly: 'GROQ is working!' and nothing else."}],
            model_name="llama-3.3-70b-versatile",
            max_tokens=50,
            temperature=0.1
        )
        print(f"   ✅ GROQ Response: {response}")
        
        # Extract the actual content
        if hasattr(response, 'choices') and response.choices:
            content = response.choices[0].message.content
            print(f"   📝 Content: {content}")
            
            if "GROQ is working!" in content:
                print("   🎉 SUCCESS: GROQ is working correctly!")
                return True
            else:
                print("   ⚠️  WARNING: GROQ responded but with unexpected content")
                return False
        else:
            print(f"   ❌ ERROR: Unexpected response format: {response}")
            return False
            
    except Exception as e:
        print(f"   ❌ GROQ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_groq_direct())
    if success:
        print("\n🎉 GROQ API is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ GROQ API test failed!")
        sys.exit(1)
