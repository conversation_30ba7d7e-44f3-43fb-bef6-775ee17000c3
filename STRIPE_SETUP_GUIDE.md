# 🔥 Stripe Products Setup Guide

## Overview
This guide will help you create all the subscription products for your AI Co-Founder application in Stripe.

## 📦 Products to Create

### Monthly Plans
- **Free - Monthly**: $0/month, $5 AI credits, public projects
- **Plus - Monthly**: $20/month, $20 AI credits, private projects  
- **Pro - Monthly**: $50/month, $50 AI credits, private projects
- **Ultra - Monthly**: $200/month, $200 AI credits, priority support

### Annual Plans (with discounts)
- **Free - Annual**: $0/year, $5 AI credits, public projects
- **Plus - Annual**: $204/year ($17/month, 15% off), $20 AI credits/month
- **Pro - Annual**: $516/year ($43/month, 14% off), $50 AI credits/month  
- **Ultra - Annual**: $2040/year ($170/month, 15% off), $200 AI credits/month

## 🚀 Setup Instructions

### Step 1: Install Dependencies
```bash
pip install stripe
```

### Step 2: Get Your Stripe API Key
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers > API keys**
3. Copy your **Secret key** (starts with `sk_test_` for test mode)

### Step 3: Run the Setup Script

**Option A: Using Environment Variable (Recommended)**
```bash
# Set your Stripe secret key
export STRIPE_SECRET_KEY="sk_test_your_actual_key_here"

# Run the setup script
python setup_stripe_products.py
```

**Option B: Using Command Line Argument**
```bash
python setup_stripe_products.py --api-key "sk_test_your_actual_key_here"
```

### Step 4: Verify in Stripe Dashboard
1. Go to **Products** in your Stripe dashboard
2. You should see 8 new products created
3. Each product will have associated prices

## 📋 Expected Output

The script will create products and show output like:
```
🚀 Setting up AI Co-Founder subscription products in Stripe...

✅ Created: Free - Monthly
   Product ID: prod_xxxxxxxxxxxxx
   Price ID: price_xxxxxxxxxxxxx
   Amount: $0.00 monthly

✅ Created: Plus - Monthly
   Product ID: prod_xxxxxxxxxxxxx
   Price ID: price_xxxxxxxxxxxxx
   Amount: $20.00 monthly

... (and so on for all 8 products)

🎉 Successfully created 8 products!
```

## 🔧 Integration with Your App

After running the script, you'll need to update your application with the generated price IDs:

### Update Environment Variables
Add these to your `.env.local` or Netlify environment variables:
```bash
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key

# Product Price IDs (replace with actual IDs from script output)
STRIPE_PRICE_FREE_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_PLUS_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_PRO_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_ULTRA_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_FREE_ANNUAL=price_xxxxxxxxxxxxx
STRIPE_PRICE_PLUS_ANNUAL=price_xxxxxxxxxxxxx
STRIPE_PRICE_PRO_ANNUAL=price_xxxxxxxxxxxxx
STRIPE_PRICE_ULTRA_ANNUAL=price_xxxxxxxxxxxxx
```

### Update Your Billing Component
The price IDs can be used in your billing/subscription components to create checkout sessions.

## 🔒 Security Notes

1. **Never commit API keys to version control**
2. **Use test keys for development** (`sk_test_`)
3. **Use live keys only for production** (`sk_live_`)
4. **Store keys in environment variables**
5. **The script will warn you if using live keys**

## 🧪 Testing

After setup, you can test in Stripe:
1. Use test card numbers (e.g., `****************`)
2. Create test subscriptions
3. Verify webhooks are working
4. Test the complete billing flow

## 🚀 Going Live

When ready for production:
1. Switch to live Stripe keys
2. Re-run the script with live keys
3. Update your app's environment variables
4. Test with real payment methods

## 📞 Support

If you encounter issues:
1. Check the Stripe dashboard for error logs
2. Verify API key permissions
3. Ensure you're in the correct Stripe mode (test/live)
4. Check the script output for specific error messages

## 🎯 Next Steps

After creating the products:
1. ✅ Products created in Stripe
2. 🔄 Update app environment variables  
3. 🔄 Test subscription flow
4. 🔄 Set up webhooks for subscription events
5. 🔄 Deploy to production with live keys

Your Stripe products are now ready for integration! 🎉
