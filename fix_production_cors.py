#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Production CORS and Domain Issues

This script fixes the CORS errors and domain mismatches in production
by updating the backend CORS configuration and frontend environment.
"""

import os
import shutil

def backup_file(file_path):
    """Create a backup of the current file"""
    if os.path.exists(file_path):
        backup_path = file_path + ".cors_backup"
        shutil.copy2(file_path, backup_path)
        print("Backed up " + file_path + " to " + backup_path)

def fix_cors_configuration():
    """Fix CORS configuration for production domains"""
    print("Fixing CORS configuration for production...")
    print("=" * 50)
    
    # Update backend CORS configuration
    backend_api_path = "backend/api.py"
    if os.path.exists(backend_api_path):
        backup_file(backend_api_path)
        
        with open(backend_api_path, 'r') as f:
            content = f.read()
        
        # Check if production domains are already added
        if "aicofounder.site" not in content:
            # Find the CORS configuration section and update it
            old_cors_config = '''# Define allowed origins based on environment
allowed_origins = ["https://www.suna.so", "https://suna.so"]
allow_origin_regex = None

# Add local development origins'''
            
            new_cors_config = '''# Define allowed origins based on environment
allowed_origins = ["https://www.suna.so", "https://suna.so"]
allow_origin_regex = None

# Add production domains
if config.ENV_MODE == EnvMode.PRODUCTION:
    allowed_origins.extend([
        "https://aicofounder.site",
        "https://www.aicofounder.site", 
        "https://ai-co-founder.netlify.app",
        "https://www.ai-co-founder.netlify.app"
    ])

# Add local development origins'''
            
            content = content.replace(old_cors_config, new_cors_config)
            
            with open(backend_api_path, 'w') as f:
                f.write(content)
            print("[SUCCESS] Updated backend CORS configuration")
        else:
            print("[INFO] Backend CORS configuration already includes production domains")
    
    # Update frontend environment
    frontend_env_path = "frontend/.env.local"
    if os.path.exists(frontend_env_path):
        backup_file(frontend_env_path)
        
        with open(frontend_env_path, 'r') as f:
            content = f.read()
        
        # Update the site URL to match actual production domain
        content = content.replace(
            "NEXT_PUBLIC_URL=https://ai-co-founder.netlify.app",
            "NEXT_PUBLIC_URL=https://aicofounder.site"
        )
        
        with open(frontend_env_path, 'w') as f:
            f.write(content)
        print("[SUCCESS] Updated frontend environment for production domain")
    
    # Update Supabase configuration
    supabase_config_path = "backend/supabase/config.toml"
    if os.path.exists(supabase_config_path):
        backup_file(supabase_config_path)
        
        with open(supabase_config_path, 'r') as f:
            content = f.read()
        
        # Update site_url and additional_redirect_urls
        content = content.replace(
            'site_url = "https://ai-co-founder.netlify.app"',
            'site_url = "https://aicofounder.site"'
        )
        
        # Update additional redirect URLs if not already updated
        if "aicofounder.site" not in content:
            content = content.replace(
                'additional_redirect_urls = ["https://ai-co-founder.netlify.app", "http://localhost:3000"]',
                'additional_redirect_urls = ["https://aicofounder.site", "https://ai-co-founder.netlify.app", "http://localhost:3000"]'
            )
        
        with open(supabase_config_path, 'w') as f:
            f.write(content)
        print("[SUCCESS] Updated Supabase auth configuration")
    
    # Update netlify.toml
    netlify_config_path = "netlify.toml"
    if os.path.exists(netlify_config_path):
        backup_file(netlify_config_path)
        
        with open(netlify_config_path, 'r') as f:
            content = f.read()
        
        # Update the production URL
        content = content.replace(
            'NEXT_PUBLIC_URL = "https://ai-co-founder.netlify.app"',
            'NEXT_PUBLIC_URL = "https://aicofounder.site"'
        )
        
        with open(netlify_config_path, 'w') as f:
            f.write(content)
        print("[SUCCESS] Updated Netlify configuration")
    
    print("")
    print("[SUCCESS] CORS and domain configuration fixed!")
    print("")
    print("Changes made:")
    print("1. Added production domains to backend CORS configuration")
    print("2. Updated frontend environment to use correct production domain")
    print("3. Updated Supabase auth configuration for production domain")
    print("4. Updated Netlify configuration")
    print("")
    print("Production domains now allowed:")
    print("- https://aicofounder.site")
    print("- https://www.aicofounder.site")
    print("- https://ai-co-founder.netlify.app")
    print("- https://www.ai-co-founder.netlify.app")
    print("")
    print("Next steps:")
    print("1. Restart your backend server if running locally")
    print("2. Deploy the backend changes to production")
    print("3. Deploy the frontend changes to Netlify")
    print("4. Test the application - CORS errors should be resolved")

def show_cors_status():
    """Show current CORS configuration status"""
    print("Current CORS Configuration Status:")
    print("-" * 40)
    
    # Check backend CORS config
    backend_api_path = "backend/api.py"
    if os.path.exists(backend_api_path):
        with open(backend_api_path, 'r') as f:
            content = f.read()
        
        if "aicofounder.site" in content:
            print("[✓] Backend: Production domains configured")
        else:
            print("[✗] Backend: Missing production domains")
    else:
        print("[?] Backend: api.py file not found")
    
    # Check frontend environment
    frontend_env_path = "frontend/.env.local"
    if os.path.exists(frontend_env_path):
        with open(frontend_env_path, 'r') as f:
            content = f.read()
        
        if "NEXT_PUBLIC_URL=https://aicofounder.site" in content:
            print("[✓] Frontend: Correct production domain")
        else:
            print("[✗] Frontend: Incorrect or missing production domain")
    else:
        print("[?] Frontend: .env.local file not found")
    
    # Check Supabase config
    supabase_config_path = "backend/supabase/config.toml"
    if os.path.exists(supabase_config_path):
        with open(supabase_config_path, 'r') as f:
            content = f.read()
        
        if 'site_url = "https://aicofounder.site"' in content:
            print("[✓] Supabase: Correct production site URL")
        else:
            print("[✗] Supabase: Incorrect site URL")
    else:
        print("[?] Supabase: config.toml file not found")

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--status':
        show_cors_status()
    else:
        fix_cors_configuration()

if __name__ == "__main__":
    main()
