#!/usr/bin/env python3
"""
Web-based migration applier for Supabase
This script will generate SQL files that you can copy-paste into Supabase SQL Editor
"""
import os
import glob

def create_migration_batches():
    """Create batches of migrations that can be applied safely"""
    
    # Get all migration files in order
    migration_dir = 'backend/supabase/migrations'
    migration_files = sorted(glob.glob(os.path.join(migration_dir, '*.sql')))
    
    if not migration_files:
        print("No migration files found!")
        return
    
    print(f"Found {len(migration_files)} migration files")
    
    # Create batches - group related migrations
    batches = [
        # Batch 1: Core Basejump Setup
        [
            '20240414161707_basejump-setup.sql',
            '20240414161947_basejump-accounts.sql',
            '20240414162100_basejump-invitations.sql',
            '20240414162131_basejump-billing.sql',
            '20250409211903_basejump-configure.sql'
        ],
        # Batch 2: Initial Suna Schema
        [
            '20250409212058_initial.sql',
            '20250416133920_agentpress_schema.sql'
        ],
        # Batch 3: Workflow System
        [
            '20250417000000_workflow_system.sql',
            '20250418000000_workflow_flows.sql',
            '20250504123828_fix_thread_select_policy.sql',
            '20250523133848_admin-view-access.sql'
        ],
        # Batch 4: Agents and Features
        [
            '20250524062639_agents_table.sql',
            '20250525000000_agent_versioning.sql',
            '20250526000000_secure_mcp_credentials.sql',
            '20250529125628_agent_marketplace.sql'
        ],
        # Batch 5: Recent Updates
        [
            '20250601000000_add_thread_metadata.sql',
            '20250602000000_add_custom_mcps_column.sql',
            '20250607000000_fix_encrypted_config_column.sql',
            '20250618000000_credential_profiles.sql',
            '20250624065047_secure_credentials.sql',
            '20250624093857_knowledge_base.sql'
        ],
        # Batch 6: Latest Features
        [
            '20250626092143_agent_agnostic_thread.sql',
            '20250626114642_kortix_team_agents.sql',
            '20250630070510_agent_triggers.sql',
            '20250701082739_agent_knowledge_base.sql',
            '20250701083536_agent_kb_files.sql'
        ],
        # Batch 7: Workflow Updates
        [
            '20250705155923_rollback_workflows.sql',
            '20250705161610_agent_workflows.sql',
            '20250705164211_fix_agent_workflows.sql',
            '20250706130554_simplify_workflow_steps.sql',
            '20250706130555_set_instruction_default.sql',
            '20250707140000_add_agent_run_metadata.sql',
            '20250708034613_add_steps_to_workflows.sql',
            '20250708123910_cleanup_db.sql'
        ]
    ]
    
    # Create output directory
    os.makedirs('migration_batches', exist_ok=True)
    
    for i, batch in enumerate(batches, 1):
        batch_content = f"-- MIGRATION BATCH {i}\n"
        batch_content += f"-- Apply this batch in Supabase SQL Editor\n"
        batch_content += f"-- Files included: {', '.join(batch)}\n\n"
        
        for filename in batch:
            filepath = os.path.join(migration_dir, filename)
            if os.path.exists(filepath):
                batch_content += f"-- ========================================\n"
                batch_content += f"-- FILE: {filename}\n"
                batch_content += f"-- ========================================\n\n"
                
                try:
                    with open(filepath, 'r') as f:
                        content = f.read()
                        batch_content += content + "\n\n"
                except Exception as e:
                    batch_content += f"-- ERROR READING FILE: {e}\n\n"
            else:
                batch_content += f"-- FILE NOT FOUND: {filename}\n\n"
        
        # Write batch file
        batch_filename = f'migration_batches/batch_{i:02d}.sql'
        with open(batch_filename, 'w') as f:
            f.write(batch_content)
        
        print(f"✅ Created {batch_filename} with {len(batch)} migrations")
    
    print(f"\n🎉 Created {len(batches)} migration batches in 'migration_batches/' directory")
    print("\nNext steps:")
    print("1. Go to your Supabase SQL Editor")
    print("2. Apply each batch file in order (batch_01.sql, batch_02.sql, etc.)")
    print("3. Run each batch completely before moving to the next")
    print("4. After all batches are applied, expose the 'basejump' schema in Project Settings")

if __name__ == "__main__":
    create_migration_batches()
