#!/usr/bin/env python3
"""
Fix database schema and test LLM functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
from services.supabase import db

async def fix_database_schema():
    """Apply the missing database migration"""
    print("🔧 Fixing Database Schema")
    print("=" * 50)
    
    try:
        client = await db.client
        
        # Check if is_llm_message column exists
        print("\n1. Checking messages table schema...")
        
        # Try to query the column to see if it exists
        try:
            result = await client.table('messages').select('is_llm_message').limit(1).execute()
            print("   ✅ is_llm_message column already exists")
            return True
        except Exception as e:
            if "column" in str(e).lower() and "does not exist" in str(e).lower():
                print("   ❌ is_llm_message column missing - applying fix...")
            else:
                print(f"   ❓ Unexpected error checking column: {str(e)}")
        
        # Apply the migration SQL
        print("\n2. Applying database migration...")
        
        migration_sql = """
        -- Add is_llm_message column if it doesn't exist
        ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;
        
        -- Update existing user messages to have is_llm_message = false
        UPDATE messages 
        SET is_llm_message = false 
        WHERE type = 'user' AND is_llm_message = true;
        
        -- Update existing assistant/system messages to have is_llm_message = true
        UPDATE messages 
        SET is_llm_message = true 
        WHERE type IN ('assistant', 'system', 'tool') AND is_llm_message = false;
        
        -- Add index for better performance
        CREATE INDEX IF NOT EXISTS idx_messages_is_llm_message ON messages(is_llm_message);
        CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);
        """
        
        # Execute the migration
        result = await client.rpc('exec_sql', {'sql': migration_sql}).execute()
        print("   ✅ Migration applied successfully")
        
        # Verify the column now exists
        try:
            result = await client.table('messages').select('is_llm_message').limit(1).execute()
            print("   ✅ Verified: is_llm_message column now exists")
            return True
        except Exception as e:
            print(f"   ❌ Verification failed: {str(e)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error applying migration: {str(e)}")
        
        # Try alternative approach - direct SQL execution
        print("\n3. Trying alternative approach...")
        try:
            # Use raw SQL execution if available
            await client.postgrest.rpc('exec_sql', {'sql': migration_sql}).execute()
            print("   ✅ Alternative migration approach successful")
            return True
        except Exception as e2:
            print(f"   ❌ Alternative approach failed: {str(e2)}")
            
            # Manual column addition
            print("\n4. Trying manual column addition...")
            try:
                # Try to add the column directly
                await client.postgrest.schema('public').table('messages').alter().add_column('is_llm_message', 'boolean', default=True).execute()
                print("   ✅ Manual column addition successful")
                return True
            except Exception as e3:
                print(f"   ❌ Manual approach failed: {str(e3)}")
                print("\n   💡 You may need to apply the migration manually in your Supabase dashboard:")
                print("   ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;")
                return False

async def test_message_creation():
    """Test creating a message to verify the schema fix"""
    print("\n🧪 Testing Message Creation")
    print("=" * 30)
    
    try:
        client = await db.client
        
        # Try to create a test message
        test_message = {
            "content": "Test message for schema verification",
            "type": "user",
            "thread_id": "test-thread-id",
            "is_llm_message": False
        }
        
        result = await client.table('messages').insert(test_message).execute()
        
        if result.data:
            message_id = result.data[0]['id']
            print(f"   ✅ Test message created successfully: {message_id}")
            
            # Clean up the test message
            await client.table('messages').delete().eq('id', message_id).execute()
            print("   ✅ Test message cleaned up")
            return True
        else:
            print("   ❌ Failed to create test message")
            return False
            
    except Exception as e:
        print(f"   ❌ Error creating test message: {str(e)}")
        return False

async def main():
    """Main function to fix database and test"""
    print("🚀 Database Fix and LLM Test")
    print("=" * 50)
    
    # Step 1: Fix database schema
    schema_fixed = await fix_database_schema()
    
    if schema_fixed:
        # Step 2: Test message creation
        message_test = await test_message_creation()
        
        if message_test:
            print("\n🎉 SUCCESS!")
            print("=" * 50)
            print("✅ Database schema fixed")
            print("✅ Message creation working")
            print("✅ Ready to test LLM functionality")
            print("\nNow try sending a message in the Suna UI!")
            print("The system should use GROQ first, then Gemini as fallback.")
        else:
            print("\n⚠️  PARTIAL SUCCESS")
            print("=" * 50)
            print("✅ Database schema might be fixed")
            print("❌ Message creation test failed")
            print("💡 Try sending a message in the UI anyway")
    else:
        print("\n❌ SCHEMA FIX FAILED")
        print("=" * 50)
        print("❌ Could not fix database schema automatically")
        print("💡 Manual intervention required:")
        print("   1. Go to your Supabase dashboard")
        print("   2. Run this SQL in the SQL editor:")
        print("   ALTER TABLE messages ADD COLUMN IF NOT EXISTS is_llm_message BOOLEAN NOT NULL DEFAULT TRUE;")

if __name__ == "__main__":
    asyncio.run(main())
