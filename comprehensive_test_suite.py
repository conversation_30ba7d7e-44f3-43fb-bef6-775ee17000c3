#!/usr/bin/env python3
"""
Comprehensive Test Suite for Suna Application
Tests all major functionality to ensure the application is working perfectly.
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

class SunaTestSuite:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if details and not success:
            print(f"   Details: {details}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "details": details
        })
    
    def test_health_check(self) -> bool:
        """Test basic health check endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/health")
            success = response.status_code == 200
            data = response.json() if success else None
            self.log_test("Health Check", success, 
                         f"Status: {response.status_code}" + (f", Instance: {data.get('instance_id', 'unknown')}" if data else ""),
                         response.text if not success else None)
            return success
        except Exception as e:
            self.log_test("Health Check", False, f"Exception: {str(e)}")
            return False
    
    def test_cors_headers(self) -> bool:
        """Test CORS headers are properly set"""
        try:
            response = self.session.options(f"{self.base_url}/api/health",
                                          headers={
                                              "Origin": "http://localhost:3000",
                                              "Access-Control-Request-Method": "GET",
                                              "Access-Control-Request-Headers": "Content-Type,Authorization"
                                          })
            cors_headers = {
                "access-control-allow-origin": response.headers.get("access-control-allow-origin"),
                "access-control-allow-methods": response.headers.get("access-control-allow-methods"),
                "access-control-allow-headers": response.headers.get("access-control-allow-headers")
            }
            success = all(header is not None for header in cors_headers.values())
            self.log_test("CORS Headers", success,
                         f"Headers present: {success}", cors_headers if not success else None)
            return success
        except Exception as e:
            self.log_test("CORS Headers", False, f"Exception: {str(e)}")
            return False
    
    def test_projects_endpoint(self) -> bool:
        """Test projects endpoint accessibility"""
        try:
            response = self.session.get(f"{self.base_url}/api/projects",
                                      headers={"Origin": "http://localhost:3000"})
            # We expect 401 without auth, which means the endpoint is working
            success = response.status_code in [200, 401]
            self.log_test("Projects Endpoint", success, 
                         f"Status: {response.status_code} (expected 200 or 401)")
            return success
        except Exception as e:
            self.log_test("Projects Endpoint", False, f"Exception: {str(e)}")
            return False
    
    def test_agents_endpoint(self) -> bool:
        """Test agents endpoint accessibility"""
        try:
            response = self.session.get(f"{self.base_url}/api/agents",
                                      headers={"Origin": "http://localhost:3000"})
            # We expect 401 without auth, which means the endpoint is working
            success = response.status_code in [200, 401]
            self.log_test("Agents Endpoint", success, 
                         f"Status: {response.status_code} (expected 200 or 401)")
            return success
        except Exception as e:
            self.log_test("Agents Endpoint", False, f"Exception: {str(e)}")
            return False
    
    def test_thread_endpoint_without_auth(self) -> bool:
        """Test thread endpoint returns proper 401 without auth"""
        try:
            test_thread_id = "test-thread-id"
            response = self.session.get(f"{self.base_url}/api/thread/{test_thread_id}")
            # Should return 401 without proper auth
            success = response.status_code == 401
            self.log_test("Thread Endpoint (No Auth)", success, 
                         f"Status: {response.status_code} (expected 401)")
            return success
        except Exception as e:
            self.log_test("Thread Endpoint (No Auth)", False, f"Exception: {str(e)}")
            return False
    
    def test_agent_initiate_endpoint(self) -> bool:
        """Test agent initiate endpoint structure"""
        try:
            response = self.session.post(f"{self.base_url}/api/agent/initiate",
                                       json={"test": "data"},
                                       headers={"Origin": "http://localhost:3000"})
            # We expect 401 or 422 without proper auth/data, which means endpoint is working
            success = response.status_code in [401, 422]
            self.log_test("Agent Initiate Endpoint", success, 
                         f"Status: {response.status_code} (expected 401 or 422)")
            return success
        except Exception as e:
            self.log_test("Agent Initiate Endpoint", False, f"Exception: {str(e)}")
            return False
    
    def test_frontend_accessibility(self) -> bool:
        """Test frontend is accessible"""
        try:
            response = requests.get("http://localhost:3000", timeout=15)
            success = response.status_code == 200
            self.log_test("Frontend Accessibility", success,
                         f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Frontend Accessibility", False, f"Exception: {str(e)}")
            return False
    
    def test_database_schema_compatibility(self) -> bool:
        """Test that database operations don't throw schema errors"""
        try:
            # This should trigger database queries and reveal any schema issues
            response = self.session.get(f"{self.base_url}/api/health")
            success = response.status_code == 200
            self.log_test("Database Schema Compatibility", success, 
                         "No database schema errors detected")
            return success
        except Exception as e:
            self.log_test("Database Schema Compatibility", False, f"Exception: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return summary"""
        print("🚀 Starting Comprehensive Suna Application Test Suite")
        print("=" * 60)
        
        tests = [
            self.test_health_check,
            self.test_cors_headers,
            self.test_projects_endpoint,
            self.test_agents_endpoint,
            self.test_thread_endpoint_without_auth,
            self.test_agent_initiate_endpoint,
            self.test_frontend_accessibility,
            self.test_database_schema_compatibility
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            time.sleep(0.5)  # Small delay between tests
        
        print("\n" + "=" * 60)
        print(f"📊 TEST SUMMARY: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Suna application is working perfectly!")
            return {"status": "success", "passed": passed, "total": total, "results": self.test_results}
        else:
            print(f"⚠️  {total - passed} tests failed. See details above.")
            return {"status": "partial", "passed": passed, "total": total, "results": self.test_results}

def main():
    """Main test runner"""
    suite = SunaTestSuite()
    results = suite.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if results["status"] == "success" else 1)

if __name__ == "__main__":
    main()
