-- FIX AGENTS TABLE STRUCTURE
-- Copy and paste this into your Supabase SQL Editor

-- First, let's check and fix the agents table structure
DO $$ 
BEGIN
    -- Check if agents table exists and add missing columns
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'agents' AND table_schema = 'public') THEN
        -- Add account_id column if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'account_id' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE;
        END IF;
        
        -- Add other missing columns
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'name' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN name text NOT NULL DEFAULT 'Unnamed Agent';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'description' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN description text;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'instructions' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN instructions text;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'is_default' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN is_default boolean DEFAULT false;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'metadata' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN metadata jsonb DEFAULT '{}'::jsonb;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'created_at' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN created_at timestamp with time zone DEFAULT now();
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'updated_at' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN updated_at timestamp with time zone DEFAULT now();
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'created_by' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN created_by uuid references auth.users;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agents' AND column_name = 'updated_by' AND table_schema = 'public') THEN
            ALTER TABLE public.agents ADD COLUMN updated_by uuid references auth.users;
        END IF;
        
    ELSE
        -- Create the agents table from scratch if it doesn't exist
        CREATE TABLE public.agents (
            id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
            account_id uuid REFERENCES basejump.accounts(id) ON DELETE CASCADE,
            name text NOT NULL,
            description text,
            instructions text,
            is_default boolean DEFAULT false,
            metadata jsonb DEFAULT '{}'::jsonb,
            created_at timestamp with time zone DEFAULT now(),
            updated_at timestamp with time zone DEFAULT now(),
            created_by uuid references auth.users,
            updated_by uuid references auth.users
        );
    END IF;
END $$;

-- Ensure proper permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.agents TO authenticated, service_role;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view agents in their accounts" ON public.agents;
DROP POLICY IF EXISTS "Users can insert agents in their accounts" ON public.agents;
DROP POLICY IF EXISTS "Users can update agents in their accounts" ON public.agents;
DROP POLICY IF EXISTS "Users can delete agents in their accounts" ON public.agents;

-- Create policies for agents
CREATE POLICY "Users can view agents in their accounts" ON public.agents
    FOR SELECT TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can insert agents in their accounts" ON public.agents
    FOR INSERT TO authenticated WITH CHECK (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can update agents in their accounts" ON public.agents
    FOR UPDATE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

CREATE POLICY "Users can delete agents in their accounts" ON public.agents
    FOR DELETE TO authenticated USING (
        account_id IN (
            SELECT id FROM basejump.accounts 
            WHERE primary_owner_user_id = auth.uid()
               OR id IN (
                   SELECT account_id FROM basejump.account_user 
                   WHERE user_id = auth.uid()
               )
        )
    );

-- Create the timestamp function if it doesn't exist
CREATE OR REPLACE FUNCTION public.trigger_set_timestamps()
    RETURNS TRIGGER AS
$$
BEGIN
    if TG_OP = 'INSERT' then
        NEW.created_at = now();
        NEW.updated_at = now();
    else
        NEW.updated_at = now();
        NEW.created_at = OLD.created_at;
    end if;
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Apply timestamp triggers to agents
DROP TRIGGER IF EXISTS set_timestamps_agents ON public.agents;
CREATE TRIGGER set_timestamps_agents BEFORE INSERT OR UPDATE ON public.agents
    FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamps();

-- Update existing agents to have account_id if they don't have one
-- This assigns them to the first available account
UPDATE public.agents 
SET account_id = (SELECT id FROM basejump.accounts LIMIT 1)
WHERE account_id IS NULL;

-- Insert default agents for accounts that don't have any
INSERT INTO public.agents (account_id, name, description, instructions, is_default)
SELECT 
    id,
    'Suna Assistant',
    'Your default AI assistant powered by Suna',
    'You are Suna, a helpful AI assistant. Be concise, accurate, and helpful in your responses.',
    true
FROM basejump.accounts
WHERE id NOT IN (SELECT DISTINCT account_id FROM public.agents WHERE account_id IS NOT NULL AND is_default = true)
ON CONFLICT DO NOTHING;
