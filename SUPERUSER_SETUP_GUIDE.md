# 🛡️ Superuser System Setup Guide

## Overview
This guide will set up `<EMAIL>` as a superuser with full admin access to the Suna application, including a comprehensive admin dashboard.

## ✅ What You'll Get

### 🎯 **Superuser Capabilities:**
- **View All Users**: See all registered users and their activity
- **System Statistics**: Monitor total users, messages, threads, projects, agents
- **Admin Dashboard**: Dedicated `/admin` route with full system overview
- **Activity Monitoring**: Track all admin actions and user activity
- **User Management**: View detailed user information and activity
- **Security**: Role-based access with proper authentication

### 🖥️ **Admin Dashboard Features:**
- **System Stats Cards**: Users, messages, projects, agents counts
- **User Management Table**: Search, view, and manage all users
- **Activity Log**: Track all administrative actions
- **Real-time Data**: Live updates of system statistics
- **Responsive Design**: Works on desktop and mobile

## 📋 Setup Instructions

### Step 1: Run the SQL Setup

1. **Open your Supabase Dashboard**: https://pldcxtmyivlpueddnuml.supabase.co
2. **Navigate to**: SQL Editor
3. **Copy and paste** the entire contents of `superuser_setup_manual.sql`
4. **Click "Run"** to execute the SQL

The SQL will:
- ✅ Create `superusers` table
- ✅ Create `admin_activity_log` table  
- ✅ Create `is_superuser()` function
- ✅ Create `log_admin_activity()` function
- ✅ Add `<EMAIL>` as superuser
- ✅ Set up proper RLS policies
- ✅ Grant necessary permissions

### Step 2: Verify the Setup

Run the verification script:
```bash
cd backend
python3 verify_superuser.py
```

You should see:
```
✅ SUPERUSER SYSTEM IS WORKING!
   📧 Email: <EMAIL>
   🔑 User ID: [uuid]
   ✅ Active: true
   📅 Created: [timestamp]

🚀 <EMAIL> can now access:
   • Admin Dashboard at /admin
   • View all users and their activity
   • System statistics and monitoring
   • Admin activity logs
```

### Step 3: Access the Admin Dashboard

1. **Sign in** to the application as `<EMAIL>`
2. **Look for the "Admin Dashboard"** link in the sidebar (with SUPER badge)
3. **Navigate to** `/admin` or click the sidebar link
4. **Enjoy full superuser access!**

## 🔧 Technical Details

### Database Schema

#### `superusers` Table:
- `id`: UUID primary key
- `user_id`: Reference to auth.users
- `email`: User email (unique)
- `is_active`: Boolean flag
- `permissions`: JSONB with granular permissions
- `created_at`: Timestamp
- `last_login_at`: Last admin access time

#### `admin_activity_log` Table:
- `id`: UUID primary key
- `admin_user_id`: Who performed the action
- `action`: What action was performed
- `target_type`: Type of target (user, thread, etc.)
- `target_id`: ID of the target
- `details`: JSONB with additional details
- `created_at`: When the action occurred

### API Endpoints

The admin API provides these endpoints:

- `GET /api/admin/verify` - Verify superuser access
- `GET /api/admin/users` - Get all users with activity summary
- `GET /api/admin/stats` - Get system-wide statistics
- `GET /api/admin/activity` - Get admin activity log
- `GET /api/admin/user/{id}/details` - Get detailed user information
- `GET /api/admin/superusers` - Get all superusers
- `POST /api/admin/user/{id}/disable` - Disable a user account

### Frontend Components

- **Admin Dashboard Page**: `/admin` route with full dashboard
- **Sidebar Integration**: Admin link appears for superusers only
- **User Management**: Search, view, and manage users
- **Activity Monitoring**: Real-time admin activity tracking
- **System Statistics**: Live system metrics and counts

## 🔒 Security Features

### Authentication & Authorization:
- ✅ JWT-based authentication required
- ✅ Superuser verification on every admin request
- ✅ Row Level Security (RLS) policies
- ✅ Activity logging for all admin actions
- ✅ Granular permissions system

### Access Control:
- ✅ Only `<EMAIL>` has superuser access
- ✅ Admin dashboard only visible to superusers
- ✅ All admin actions are logged
- ✅ Proper error handling and validation

## 🚀 Usage Examples

### Viewing System Statistics:
The admin dashboard shows:
- Total users and active users (24h/7d)
- Total messages and threads
- Total projects and agents
- Agent runs and activity metrics

### Managing Users:
- Search users by email
- View user activity summary
- See user creation date and last sign-in
- View detailed user information
- Track user's threads, messages, projects

### Monitoring Activity:
- See all admin actions in real-time
- Track who did what and when
- Monitor system usage patterns
- Audit administrative changes

## 🛠️ Troubleshooting

### If the SQL setup fails:
1. Check that you're using the Service Role key
2. Ensure the user `<EMAIL>` exists in auth.users
3. Run each SQL block separately if needed

### If the admin dashboard doesn't appear:
1. Verify the user is signed in as `<EMAIL>`
2. Check that the superuser record exists in the database
3. Ensure the frontend code changes are deployed

### If API calls fail:
1. Check that the admin API is included in the main router
2. Verify JWT authentication is working
3. Check Supabase connection and permissions

## 📞 Support

If you encounter any issues:
1. Run the verification script to check the setup
2. Check the browser console for any JavaScript errors
3. Check the backend logs for API errors
4. Verify all SQL commands executed successfully

---

**🎉 Once setup is complete, `<EMAIL>` will have full superuser access with a comprehensive admin dashboard for managing the entire Suna application!**
