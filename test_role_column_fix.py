#!/usr/bin/env python3
"""
Test the role column fix - providing required role field
"""

import requests
import json
import uuid

def test_role_column_fix():
    """Test that the role column fix works"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Role Column Fix (providing required role field)")
    print("=" * 65)
    
    # Test 1: Agent initiate endpoint
    print("\n1. Testing Agent Initiate Endpoint...")
    
    form_data = {
        'prompt': 'Hello, this is a test to verify the role column fix works!',
        'model_name': 'groq/llama-3.1-70b-versatile',
        'enable_thinking': 'false',
        'stream': 'true'
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/agent/initiate",
            data=form_data,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ SUCCESS: Returns 401 (auth required) - no database error!")
        elif response.status_code == 500:
            print("   ❌ FAILED: Still returns 500 error")
            if "null value" in response.text.lower():
                print("   ❌ Still has null value constraint error")
            elif "role" in response.text.lower():
                print("   ❌ Still has role-related error")
            elif "column" in response.text.lower():
                print("   ❌ Still has column-related error")
            print(f"   Full response: {response.text}")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    # Test 2: Message endpoint
    print("\n2. Testing Message Endpoint...")
    
    test_thread_id = str(uuid.uuid4())
    message_data = {
        "content": "Test message with role column fix"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/thread/{test_thread_id}/messages",
            json=message_data,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ SUCCESS: Returns 401 (auth required) - no database error!")
        elif response.status_code == 500:
            print("   ❌ FAILED: Still returns 500 error")
            if "null value" in response.text.lower():
                print("   ❌ Still has null value constraint error")
            elif "role" in response.text.lower():
                print("   ❌ Still has role-related error")
            elif "column" in response.text.lower():
                print("   ❌ Still has column-related error")
            print(f"   Full response: {response.text}")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    # Test 3: Agent start endpoint
    print("\n3. Testing Agent Start Endpoint...")
    
    agent_data = {
        "model_name": "groq/llama-3.1-70b-versatile",
        "enable_thinking": False,
        "stream": True
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/thread/{test_thread_id}/agent/start",
            json=agent_data,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ SUCCESS: Returns 401 (auth required) - no database error!")
        elif response.status_code == 500:
            print("   ❌ FAILED: Still returns 500 error")
            if "null value" in response.text.lower():
                print("   ❌ Still has null value constraint error")
            elif "role" in response.text.lower():
                print("   ❌ Still has role-related error")
            elif "column" in response.text.lower():
                print("   ❌ Still has column-related error")
            print(f"   Full response: {response.text}")
        else:
            print(f"   ✅ SUCCESS: Returns {response.status_code} (not 500)")
            
    except Exception as e:
        print(f"   ❌ FAILED: Exception occurred: {e}")
    
    print("\n" + "=" * 65)
    print("🎯 FINAL SUMMARY:")
    print("If all tests show 401 (auth required) instead of 500 (database error),")
    print("then the database schema issue has been COMPLETELY RESOLVED!")
    print("The frontend should now be able to send messages successfully.")
    print("Users can now interact with the Suna application without errors!")

if __name__ == "__main__":
    test_role_column_fix()
