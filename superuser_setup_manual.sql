-- SUPERUSER SYSTEM SETUP FOR SUNA APPLICATION
-- Run this in your Supabase SQL Editor <NAME_EMAIL> a superuser

-- =====================================================
-- 1. CREATE SUPERUSER TABLES
-- =====================================================

-- Create superusers table
CREATE TABLE IF NOT EXISTS public.superusers (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    email text NOT NULL UNIQUE,
    created_at timestamp with time zone DEFAULT now(),
    created_by uuid REFERENCES auth.users(id),
    is_active boolean DEFAULT true,
    permissions jsonb DEFAULT '{
        "view_all_users": true,
        "view_all_threads": true,
        "view_all_messages": true,
        "view_all_projects": true,
        "view_all_agents": true,
        "view_system_stats": true,
        "manage_users": true,
        "manage_feature_flags": true,
        "view_billing": true,
        "system_maintenance": true
    }'::jsonb,
    last_login_at timestamp with time zone,
    notes text
);

-- Create admin activity log
CREATE TABLE IF NOT EXISTS public.admin_activity_log (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    admin_user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    action text NOT NULL,
    target_type text,
    target_id text,
    details jsonb DEFAULT '{}'::jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now()
);

-- =====================================================
-- 2. CREATE SUPERUSER FUNCTIONS
-- =====================================================

-- Function to check if a user is a superuser
CREATE OR REPLACE FUNCTION public.is_superuser(user_email text DEFAULT NULL)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    check_email text;
    is_super boolean := false;
BEGIN
    -- Use provided email or get from JWT
    check_email := COALESCE(user_email, auth.jwt() ->> 'email');
    
    IF check_email IS NULL THEN
        RETURN false;
    END IF;
    
    SELECT EXISTS(
        SELECT 1 FROM public.superusers 
        WHERE email = check_email AND is_active = true
    ) INTO is_super;
    
    RETURN is_super;
END;
$$;

-- Function to log admin activity
CREATE OR REPLACE FUNCTION public.log_admin_activity(
    action_name text,
    target_type_param text DEFAULT NULL,
    target_id_param text DEFAULT NULL,
    details_param jsonb DEFAULT '{}'::jsonb
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.admin_activity_log (
        admin_user_id,
        action,
        target_type,
        target_id,
        details,
        created_at
    ) VALUES (
        auth.uid(),
        action_name,
        target_type_param,
        target_id_param,
        details_param,
        now()
    );
END;
$$;

-- =====================================================
-- 3. INSERT SUPERUSER <NAME_EMAIL>
-- =====================================================

-- Insert <EMAIL> as superuser
DO $$
DECLARE
    target_user_id uuid;
BEGIN
    -- Get user_<NAME_EMAIL>
    SELECT id INTO target_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF target_user_id IS NOT NULL THEN
        -- Insert superuser record
        INSERT INTO public.superusers (user_id, email, created_at, is_active)
        VALUES (target_user_id, '<EMAIL>', now(), true)
        ON CONFLICT (email) DO UPDATE SET
            is_active = true,
            permissions = '{
                "view_all_users": true,
                "view_all_threads": true,
                "view_all_messages": true,
                "view_all_projects": true,
                "view_all_agents": true,
                "view_system_stats": true,
                "manage_users": true,
                "manage_feature_flags": true,
                "view_billing": true,
                "system_maintenance": true
            }'::jsonb;
        
        RAISE NOTICE 'Superuser record created/<NAME_EMAIL> (user_id: %)', target_user_id;
    ELSE
        RAISE NOTICE 'User <EMAIL> not found. They need to sign up first.';
    END IF;
END $$;

-- =====================================================
-- 4. ENABLE RLS AND CREATE POLICIES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE public.superusers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_activity_log ENABLE ROW LEVEL SECURITY;

-- Superusers table policies
CREATE POLICY "Superusers can view all superuser records" ON public.superusers
    FOR SELECT
    USING (public.is_superuser());

CREATE POLICY "Only superusers can modify superuser records" ON public.superusers
    FOR ALL
    USING (public.is_superuser());

-- Admin activity log policies
CREATE POLICY "Superusers can view all admin activity" ON public.admin_activity_log
    FOR SELECT
    USING (public.is_superuser());

CREATE POLICY "Authenticated users can insert their own admin activity" ON public.admin_activity_log
    FOR INSERT
    WITH CHECK (admin_user_id = auth.uid());

-- =====================================================
-- 5. GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated and service_role
GRANT ALL PRIVILEGES ON TABLE public.superusers TO authenticated, service_role;
GRANT ALL PRIVILEGES ON TABLE public.admin_activity_log TO authenticated, service_role;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.is_superuser(text) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.log_admin_activity(text, text, text, jsonb) TO authenticated, service_role;

-- =====================================================
-- 6. VERIFICATION
-- =====================================================

-- Show superuser records
SELECT 
    'Superuser records:' as info,
    email,
    is_active,
    created_at
FROM public.superusers;

-- Test superuser function
SELECT 
    'Superuser test:' as info,
    '<EMAIL>' as email,
    public.is_superuser('<EMAIL>') as is_superuser_result;

-- Show success message
SELECT 'Superuser system setup completed successfully!' as status;
