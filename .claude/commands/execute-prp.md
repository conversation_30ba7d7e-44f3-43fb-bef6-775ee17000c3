# Execute AI Co-Founder PRP

Implement a feature using the PRP file for the AI Co-Founder platform.

## PRP File: $ARGUMENTS

## Execution Process

1. **Load PRP**
   - Read the specified PRP file
   - Understand all context and requirements
   - Follow all instructions in the PRP and extend the research if needed
   - Ensure you have all needed context to implement the PRP fully
   - Do more web searches and codebase exploration as needed

2. **ULTRATHINK**
   - Think hard before you execute the plan. Create a comprehensive plan addressing all requirements.
   - Break down complex tasks into smaller, manageable steps using your todos tools.
   - Use the TodoWrite tool to create and track your implementation plan.
   - Identify implementation patterns from existing AI Co-Founder code to follow.
   - Consider multi-agent architecture implications
   - Plan credit usage tracking if applicable
   - Consider real-time features if applicable

3. **Execute the Plan**
   - Execute the PRP following AI Co-Founder patterns
   - Implement all the code (frontend and/or backend)
   - Follow existing patterns for:
     - React components (TypeScript + Tailwind + ShadCN)
     - FastAPI routes and services
     - CrewAI agent implementations
     - Database models and migrations
     - Authentication and authorization
     - Credit system integration
     - Real-time features (LiveKit)
     - Third-party integrations

4. **Validate**
   - Run each validation command
   - Fix any failures following AI Co-Founder patterns
   - Re-run until all pass
   - Test integration with existing features
   - Verify agent collaboration if applicable
   - Test credit usage tracking if applicable

5. **Complete**
   - Ensure all checklist items done
   - Run final validation suite
   - Test with existing AI Co-Founder features
   - Verify no breaking changes
   - Report completion status
   - Read the PRP again to ensure you have implemented everything

6. **Reference the PRP**
   - You can always reference the PRP again if needed
   - Cross-check with AI Co-Founder architecture requirements

## AI Co-Founder Specific Validation

### Frontend Validation
```bash
# Start frontend development server
npm run dev

# Check TypeScript compilation
npm run type-check

# Run linting
npm run lint

# Run tests
npm run test

# Build for production
npm run build
```

### Backend Validation
```bash
# Start backend development server
cd backend && python -m uvicorn main:app --reload

# Check Python linting
cd backend && python -m ruff check --fix

# Check type hints
cd backend && python -m mypy .

# Run tests
cd backend && python -m pytest tests/ -v

# Test API endpoints
curl -X GET http://localhost:8000/api/v1/health
```

### Integration Testing
```bash
# Test agent functionality (if applicable)
curl -X POST http://localhost:8000/api/v1/agents/chat \
  -H "Authorization: Bearer demo_token" \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "agent_type": "strategic"}'

# Test billing integration (if applicable)
curl -X GET http://localhost:8000/api/v1/billing/balance \
  -H "Authorization: Bearer demo_token"

# Test LiveKit integration (if applicable)
curl -X POST http://localhost:8000/api/v1/livekit/token \
  -H "Authorization: Bearer demo_token" \
  -H "Content-Type: application/json" \
  -d '{"agent_id": "strategic"}'
```

Note: If validation fails, use error patterns in PRP to fix and retry. Always follow AI Co-Founder architectural patterns and maintain compatibility with existing features.
