# Create AI Co-Founder PRP

## Feature file: $ARGUMENTS

Generate a complete PRP (Product Requirements Prompt) for AI Co-Founder feature implementation with thorough research. Ensure context is passed to the AI agent to enable self-validation and iterative refinement.

Read the feature file first to understand what needs to be created, how the examples provided help, and any other considerations.

The AI agent only gets the context you are appending to the PRP and training data. Assume the AI agent has access to the codebase and the same knowledge cutoff as you, so it's important that your research findings are included or referenced in the PRP.

The Agent has web search capabilities, so pass URLs to documentation and examples.

## Research Process

1. **Codebase Analysis**
   - Search for similar features/patterns in the AI Co-Founder codebase
   - Identify files to reference in PRP (frontend components, backend services, agent implementations)
   - Note existing conventions to follow (React patterns, FastAPI routes, CrewAI agent structure)
   - Check test patterns for validation approach (Jest/Vitest for frontend, pytest for backend)
   - Review existing agent implementations in `backend/app/services/agents/`
   - Check integration patterns in `backend/app/services/`

2. **AI Co-Founder Specific Research**
   - Multi-agent architecture patterns (CrewAI framework)
   - Credit-based pricing implementation (Stripe integration)
   - Real-time features (LiveKit integration)
   - Authentication patterns (JWT-based)
   - Frontend patterns (React + TypeScript + Tailwind + ShadCN)
   - Backend patterns (FastAPI + SQLAlchemy + Pydantic)

3. **External Research**
   - Search for similar features/patterns online
   - Library documentation (include specific URLs)
   - Implementation examples (GitHub/StackOverflow/blogs)
   - Best practices and common pitfalls
   - CrewAI documentation and patterns
   - Stripe API documentation
   - LiveKit integration guides

4. **User Clarification** (if needed)
   - Specific patterns to mirror and where to find them?
   - Integration requirements and where to find them?
   - Agent collaboration requirements?
   - Credit usage implications?

## PRP Generation

Using PRPs/templates/ai_cofounder_prp_base.md as template:

### Critical Context to Include and pass to the AI agent as part of the PRP

- **Documentation**: URLs with specific sections (CrewAI, Stripe, LiveKit, FastAPI, React)
- **Code Examples**: Real snippets from AI Co-Founder codebase
- **Agent Patterns**: Existing agent implementations to follow
- **Integration Patterns**: Stripe, LiveKit, Composio integration examples
- **Frontend Patterns**: React component patterns, Tailwind styling, state management
- **Backend Patterns**: FastAPI route patterns, SQLAlchemy models, service layer
- **Gotchas**: Library quirks, version issues, AI Co-Founder specific constraints
- **Credit System**: How features impact credit usage and billing

### Implementation Blueprint

- Start with pseudocode showing approach
- Reference real files for patterns from AI Co-Founder codebase
- Include error handling strategy following existing patterns
- Consider agent collaboration if applicable
- Include credit usage tracking if applicable
- List tasks to be completed to fulfill the PRP in the order they should be completed

### Validation Gates (Must be Executable)

Frontend:
```bash
# Linting and Type Checking
npm run lint
npm run type-check

# Unit Tests
npm run test

# Build Test
npm run build
```

Backend:
```bash
# Linting and Type Checking
cd backend && python -m ruff check --fix
cd backend && python -m mypy .

# Unit Tests
cd backend && python -m pytest tests/ -v

# API Health Check
curl -X GET http://localhost:8000/api/v1/health
```

***CRITICAL AFTER YOU ARE DONE RESEARCHING AND EXPLORING THE CODEBASE BEFORE YOU START WRITING THE PRP***

***ULTRATHINK ABOUT THE PRP AND PLAN YOUR APPROACH THEN START WRITING THE PRP***

## Output

Save as: `PRPs/{feature-name}.md`

## Quality Checklist

- [ ] All necessary AI Co-Founder context included
- [ ] Validation gates are executable by AI
- [ ] References existing patterns from codebase
- [ ] Clear implementation path
- [ ] Error handling documented
- [ ] Agent collaboration considered if applicable
- [ ] Credit usage implications addressed
- [ ] Frontend and backend patterns included
- [ ] Integration requirements specified

Score the PRP on a scale of 1-10 (confidence level to succeed in one-pass implementation using Claude Code)

Remember: The goal is one-pass implementation success through comprehensive context specific to the AI Co-Founder platform architecture and patterns.
