{"permissions": {"codebase": "read_write", "terminal": "read_write", "web_search": "enabled", "file_operations": "enabled"}, "project_context": {"name": "AI Co-Founder Platform", "type": "full_stack_web_app", "frontend": "React + TypeScript + Tailwind + ShadCN", "backend": "FastAPI + Python + SQLAlchemy", "ai_framework": "CrewAI", "payment": "Stripe", "realtime": "LiveKit", "deployment": "Netlify + Railway"}, "validation_commands": {"frontend": ["npm run lint", "npm run type-check", "npm run test", "npm run build"], "backend": ["cd backend && python -m ruff check --fix", "cd backend && python -m mypy .", "cd backend && python -m pytest tests/ -v"]}, "context_files": ["CLAUDE.md", "README.md", "package.json", "backend/requirements.txt"]}